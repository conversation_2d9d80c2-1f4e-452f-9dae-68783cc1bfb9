import { _decorator } from 'cc';
import { BaseActionHandler } from './IActionHandler';
import { eEmitterActionType } from '../../data/EventActionData';
import { Emitter, eEmitterStatus } from '../Emitter';
const { ccclass } = _decorator;

/**
 * Handler for Emitter Active state
 */
export class EmitterActiveHandler extends BaseActionHandler<boolean> {
    constructor() {
        super(eEmitterActionType.Emitter_Active, 'boolean', 'Emitter Active');
    }
    
    getValue(emitter: Emitter): boolean {
        return emitter.status !== eEmitterStatus.None;
    }
    
    setValue(emitter: Emitter, value: boolean): void {
        emitter.isActive = value;
    }
    
    canInterpolate(): boolean {
        return false;
    }
    
    isValidTarget(target: any): boolean {
        return target instanceof Emitter;
    }
}

/**
 * Handler for Emitter Initial Delay
 */
export class EmitterInitialDelayHandler extends BaseActionHandler<number> {
    constructor() {
        super(eEmitterActionType.Emitter_InitialDelay, 'number', 'Initial Delay');
    }
    
    getValue(emitter: Emitter): number {
        return emitter.initialDelay;
    }
    
    setValue(emitter: Emitter, value: number): void {
        emitter.initialDelay = value;
    }
    
    canInterpolate(): boolean {
        return true;
    }
    
    isValidTarget(target: any): boolean {
        return target instanceof Emitter;
    }
}

/**
 * Handler for Emitter Prewarm state
 */
export class EmitterPrewarmHandler extends BaseActionHandler<boolean> {
    constructor() {
        super(eEmitterActionType.Emitter_Prewarm, 'boolean', 'Prewarm');
    }
    
    getValue(emitter: Emitter): boolean {
        return emitter.isPreWarm;
    }
    
    setValue(emitter: Emitter, value: boolean): void {
        emitter.isPreWarm = value;
    }
    
    canInterpolate(): boolean {
        return false;
    }
    
    isValidTarget(target: any): boolean {
        return target instanceof Emitter;
    }
}

/**
 * Handler for Emitter Prewarm Duration
 */
export class EmitterPrewarmDurationHandler extends BaseActionHandler<number> {
    constructor() {
        super(eEmitterActionType.Emitter_PrewarmDuration, 'number', 'Prewarm Duration');
    }
    
    getValue(emitter: Emitter): number {
        return emitter.preWarmDuration;
    }
    
    setValue(emitter: Emitter, value: number): void {
        emitter.preWarmDuration = value;
    }
    
    canInterpolate(): boolean {
        return true;
    }
    
    isValidTarget(target: any): boolean {
        return target instanceof Emitter;
    }
}

/**
 * Handler for Emitter Duration
 */
export class EmitterDurationHandler extends BaseActionHandler<number> {
    constructor() {
        super(eEmitterActionType.Emitter_Duration, 'number', 'Duration');
    }
    
    getValue(emitter: Emitter): number {
        return emitter.emitDuration;
    }
    
    setValue(emitter: Emitter, value: number): void {
        emitter.emitDuration = value;
    }
    
    canInterpolate(): boolean {
        return true;
    }
    
    isValidTarget(target: any): boolean {
        return target instanceof Emitter;
    }
}

/**
 * Handler for Emitter Elapsed Time
 */
export class EmitterElapsedTimeHandler extends BaseActionHandler<number> {
    constructor() {
        super(eEmitterActionType.Emitter_ElapsedTime, 'number', 'Elapsed Time');
    }
    
    getValue(emitter: Emitter): number {
        return emitter.totalElapsedTime;
    }
    
    setValue(emitter: Emitter, value: number): void {
        // Note: This sets the internal status elapsed time, not total elapsed time
        emitter['_statusElapsedTime'] = value;
    }
    
    canInterpolate(): boolean {
        return true;
    }
    
    isValidTarget(target: any): boolean {
        return target instanceof Emitter;
    }
}

/**
 * Handler for Emitter Loop state
 */
export class EmitterLoopHandler extends BaseActionHandler<boolean> {
    constructor() {
        super(eEmitterActionType.Emitter_Loop, 'boolean', 'Loop');
    }
    
    getValue(emitter: Emitter): boolean {
        return emitter.isLoop;
    }
    
    setValue(emitter: Emitter, value: boolean): void {
        emitter.isLoop = value;
    }
    
    canInterpolate(): boolean {
        return false;
    }
    
    isValidTarget(target: any): boolean {
        return target instanceof Emitter;
    }
}

/**
 * Handler for Emitter Loop Interval
 */
export class EmitterLoopIntervalHandler extends BaseActionHandler<number> {
    constructor() {
        super(eEmitterActionType.Emitter_LoopInterval, 'number', 'Loop Interval');
    }

    getValue(emitter: Emitter): number {
        return emitter.loopInterval;
    }

    setValue(emitter: Emitter, value: number): void {
        emitter.loopInterval = value;
    }

    canInterpolate(): boolean {
        return true;
    }

    isValidTarget(target: any): boolean {
        return target instanceof Emitter;
    }
}

/**
 * Handler for Emitter Per Emit Interval
 */
export class EmitterPerEmitIntervalHandler extends BaseActionHandler<number> {
    constructor() {
        super(eEmitterActionType.Emitter_PerEmitInterval, 'number', 'Per Emit Interval');
    }

    getValue(emitter: Emitter): number {
        return emitter.perEmitInterval;
    }

    setValue(emitter: Emitter, value: number): void {
        emitter.perEmitInterval = value;
    }

    canInterpolate(): boolean {
        return true;
    }

    isValidTarget(target: any): boolean {
        return target instanceof Emitter;
    }
}

/**
 * Handler for Emitter Per Emit Count
 */
export class EmitterPerEmitCountHandler extends BaseActionHandler<number> {
    constructor() {
        super(eEmitterActionType.Emitter_PerEmitCount, 'number', 'Per Emit Count');
    }

    getValue(emitter: Emitter): number {
        return emitter.perEmitCount;
    }

    setValue(emitter: Emitter, value: number): void {
        emitter.perEmitCount = value;
    }

    canInterpolate(): boolean {
        return true;
    }

    isValidTarget(target: any): boolean {
        return target instanceof Emitter;
    }
}

/**
 * Handler for Emitter Per Emit Offset X
 */
export class EmitterPerEmitOffsetXHandler extends BaseActionHandler<number> {
    constructor() {
        super(eEmitterActionType.Emitter_PerEmitOffsetX, 'number', 'Per Emit Offset X');
    }

    getValue(emitter: Emitter): number {
        return emitter.perEmitOffsetX;
    }

    setValue(emitter: Emitter, value: number): void {
        emitter.perEmitOffsetX = value;
    }

    canInterpolate(): boolean {
        return true;
    }

    isValidTarget(target: any): boolean {
        return target instanceof Emitter;
    }
}

/**
 * Handler for Emitter Angle
 */
export class EmitterAngleHandler extends BaseActionHandler<number> {
    constructor() {
        super(eEmitterActionType.Emitter_Angle, 'number', 'Angle');
    }

    getValue(emitter: Emitter): number {
        return emitter.angle;
    }

    setValue(emitter: Emitter, value: number): void {
        emitter.angle = value;
    }

    canInterpolate(): boolean {
        return true;
    }

    isValidTarget(target: any): boolean {
        return target instanceof Emitter;
    }
}

/**
 * Handler for Emitter Count
 */
export class EmitterCountHandler extends BaseActionHandler<number> {
    constructor() {
        super(eEmitterActionType.Emitter_Count, 'number', 'Count');
    }

    getValue(emitter: Emitter): number {
        return emitter.count;
    }

    setValue(emitter: Emitter, value: number): void {
        emitter.count = value;
    }

    canInterpolate(): boolean {
        return true;
    }

    isValidTarget(target: any): boolean {
        return target instanceof Emitter;
    }
}
