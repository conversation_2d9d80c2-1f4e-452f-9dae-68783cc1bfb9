import { _decorator, find, Vec3, Node } from "cc";
import { Bullet } from "./Bullet";
import { Emitter } from "./Emitter";
import { eEventConditionType } from "../data/EventConditionData";
import { EventActionData, EmitterActionData, BulletActionData } from "../data/EventActionData";
import { EventActionRunner } from "./EventRunner";
import { ActionRegistry, GenericActionRunner, ActionValue, eEasing } from "./actions";
const { ccclass } = _decorator;

/**
 * BulletSystem - manages all bullets in the game world
 * Handles bullet creation, movement, collision, and cleanup
 */
export class BulletSystem {

    /**
     * All active bullets
     */
    public static allBullets: Bullet[] = [];

    /**
     * All active emitters
     */
    public static allEmitters: Emitter[] = [];

    /**
     * All active action runners (legacy)
     */
    public static allActionRunners: EventActionRunner[] = [];

    /**
     * All active generic action runners (new system)
     */
    public static allGenericActionRunners: GenericActionRunner[] = [];

    /**
     * Flag to control which action system to use
     * true = use new generic system, false = use legacy system
     */
    public static useNewActionSystem: boolean = false;

    public static bulletParentPath: string = 'Canvas/GameUI/bullet_root';
    // public static isEmitterEnabled: boolean = true;
    // public static isBulletEnabled: boolean = true;
    public static bulletParent: Node;

    /**
     * Initialize the bullet system
     */
    public static initialize() {
        // Initialize the new action system
        ActionRegistry.initialize();
        console.log('BulletSystem initialized with new action system support');
    }

    /**
     * Enable the new action system
     */
    public static enableNewActionSystem() {
        if (!ActionRegistry.getHandlerCount()) {
            ActionRegistry.initialize();
        }
        this.useNewActionSystem = true;
        console.log('New action system enabled');
    }

    /**
     * Disable the new action system (fallback to legacy)
     */
    public static disableNewActionSystem() {
        this.useNewActionSystem = false;
        console.log('Fallback to legacy action system');
    }

    /**
     * Main update loop
     */
    public static tick(dt: number) {
        this.tickEmitters(dt);
        this.tickBullets(dt);
        this.tickActionRunners(dt);
        this.tickGenericActionRunners(dt);
    }

    public static tickEmitters(dt:number) {
        for (const emitter of this.allEmitters) {
            emitter.tick(dt);
        }
    }

    public static tickBullets(dt:number) {
        for (const bullet of this.allBullets) {
            bullet.tick(dt);
        }
    }

    public static tickActionRunners(dt: number) {
        for (let i = this.allActionRunners.length - 1; i >= 0; i--) {
            const runner = this.allActionRunners[i];
            runner.tick(dt);
            if (runner.isCompleted) {
                this.allActionRunners.splice(i, 1);
            }
        }
    }

    public static tickGenericActionRunners(dt: number) {
        for (let i = this.allGenericActionRunners.length - 1; i >= 0; i--) {
            const runner = this.allGenericActionRunners[i];
            runner.tick(dt);
            if (runner.isCompleted) {
                this.allGenericActionRunners.splice(i, 1);
            }
        }
    }

    public static onCreateEmitter(emitter:Emitter) {
        for (let i = 0; i < this.allEmitters.length; i++) {
            if (this.allEmitters[i] === emitter) {
                return;
            }
        }

        this.allEmitters.push(emitter);

        if (!this.bulletParent || !this.bulletParent.isValid) {
            if (this.bulletParentPath.length > 0) {
                this.bulletParent = find(this.bulletParentPath);
                if (!this.bulletParent) {
                    console.warn('没有找到子弹父节点请检查路径:' + this.bulletParentPath);
                    this.bulletParent = emitter.node;
                }
            }
        }
    }

    public static onDestroyEmitter(emitter:Emitter) {
        this.allEmitters = this.allEmitters.filter(e => e !== emitter);
    }

    public static onCreateBullet(bullet: Bullet) {
        for (let i = 0; i < this.allBullets.length; i++) {
            if (this.allBullets[i] === bullet) {
                return;
            }
        }

        bullet.onCreate();
        this.allBullets.push(bullet);
        bullet.node.setParent(this.bulletParent, true);
    }

    public static onDestroyBullet(bullet: Bullet) {
        this.allBullets = this.allBullets.filter(b => b !== bullet);
    }

    public static destroyAllBullets() {
        for (const bullet of this.allBullets) {
            bullet.destroySelf();
        }
        this.allBullets = [];
    }

    /**
     * EventCondition & EventAction 系统
     */

    // 记录条件状态
    static allConditions: Map<eEventConditionType, number> = new Map();
    // 条件监听者
    static conditionListeners: Map<eEventConditionType, Function[]> = new Map();

    public static getConditionValue(conditionType: eEventConditionType): number {
        return this.allConditions.get(conditionType) || 0;
    }

    /**
     * 这个函数需要在游戏各个阶段去调用设置
     */
    public static setConditionValue(conditionType: eEventConditionType, value: number) {
        // check value changed
        const oldValue = this.allConditions.get(conditionType);
        if (oldValue !== value) {
            this.allConditions.set(conditionType, value);
            this.emit(conditionType, value);
        }
    }

    public static on(conditionType: eEventConditionType, listener: Function) {
        if (!this.conditionListeners.has(conditionType)) {
            this.conditionListeners.set(conditionType, []);
        }
        this.conditionListeners.get(conditionType).push(listener);
    }

    public static off(conditionType: eEventConditionType, listener: Function) {
        if (this.conditionListeners.has(conditionType)) {
            const listeners = this.conditionListeners.get(conditionType);
            this.conditionListeners.set(conditionType, listeners.filter(l => l !== listener));
        }
    }

    public static emit(conditionType: eEventConditionType, ...args: any[]) {
        if (this.conditionListeners.has(conditionType)) {
            for (const listener of this.conditionListeners.get(conditionType)) {
                listener(...args);
            }
        }
    }

    public static createActionRunner(owner: Emitter | Bullet, data: EventActionData): EventActionRunner {
        const runner = new EventActionRunner(owner, data);
        runner.start();
        this.allActionRunners.push(runner);
        return runner;
    }

    /**
     * Create a new generic action runner (new system)
     */
    public static createGenericActionRunner(
        owner: Emitter | Bullet,
        actionType: number,
        targetValue: ActionValue,
        duration: number = 0,
        easing: eEasing = eEasing.Linear,
        isRandom: boolean = false,
        minValue?: ActionValue,
        maxValue?: ActionValue
    ): GenericActionRunner | null {
        try {
            const runner = new GenericActionRunner(
                owner,
                actionType,
                duration,
                easing,
                targetValue,
                isRandom,
                minValue,
                maxValue
            );
            runner.start();
            this.allGenericActionRunners.push(runner);
            return runner;
        } catch (error) {
            console.error('Failed to create generic action runner:', error);
            return null;
        }
    }

    /**
     * Create action runner using the appropriate system based on configuration
     */
    public static createActionRunnerAuto(owner: Emitter | Bullet, data: EventActionData): EventActionRunner | GenericActionRunner | null {
        if (this.useNewActionSystem) {
            // Use new system
            const targetValue = data.isRandom ?
                (data.minValue + data.maxValue) / 2 : // Use average for random values
                (data.boolValue ? 1 : data.minValue); // Use boolValue or minValue

            return this.createGenericActionRunner(
                owner,
                data.actionType,
                targetValue,
                data.duration,
                data.easing,
                data.isRandom,
                data.minValue,
                data.maxValue
            );
        } else {
            // Use legacy system
            return this.createActionRunner(owner, data);
        }
    }

    /**
     * Get debug information about active action runners
     */
    public static getActionRunnersDebugInfo(): string {
        const legacyCount = this.allActionRunners.length;
        const newCount = this.allGenericActionRunners.length;
        const systemInUse = this.useNewActionSystem ? 'New' : 'Legacy';

        return `Action Runners Debug Info:
System in use: ${systemInUse}
Legacy runners: ${legacyCount}
Generic runners: ${newCount}
Total active: ${legacyCount + newCount}`;
    }
}