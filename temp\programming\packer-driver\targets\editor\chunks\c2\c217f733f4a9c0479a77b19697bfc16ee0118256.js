System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, EmitterActiveHandler, EmitterInitialDelayHandler, EmitterPrewarmHandler, EmitterPrewarmDurationHandler, EmitterDurationHandler, Emitter<PERSON><PERSON>sed<PERSON>ime<PERSON><PERSON><PERSON>, <PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>itter<PERSON>oop<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EmitterPerEmitIntervalHandler, EmitterPerEmitCountHandler, EmitterPerEmitOffsetXHandler, Emitter<PERSON>ngle<PERSON><PERSON><PERSON>, EmitterCountHandler, BulletDurationHandler, BulletElapsedTimeHandler, BulletPosXHandler, BulletPosYHandler, BulletDamageHand<PERSON>, <PERSON>etSpeedH<PERSON><PERSON>, BulletSpeedAngle<PERSON><PERSON><PERSON>, <PERSON>et<PERSON>cceleration<PERSON><PERSON><PERSON>, BulletAcceleration<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>et<PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>, BulletColor<PERSON><PERSON>and<PERSON>, BulletColorBHandler, BulletColorAHand<PERSON>, BulletFaceMovingDirHandler, BulletTrackingTargetHandler, BulletDestructiveHandler, BulletDestructiveOnHitHandler, ActionRegistry, _crd, ccclass;

  function _reportPossibleCrUseOfIActionHandler(extras) {
    _reporterNs.report("IActionHandler", "./IActionHandler", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitterActiveHandler(extras) {
    _reporterNs.report("EmitterActiveHandler", "./EmitterActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitterInitialDelayHandler(extras) {
    _reporterNs.report("EmitterInitialDelayHandler", "./EmitterActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitterPrewarmHandler(extras) {
    _reporterNs.report("EmitterPrewarmHandler", "./EmitterActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitterPrewarmDurationHandler(extras) {
    _reporterNs.report("EmitterPrewarmDurationHandler", "./EmitterActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitterDurationHandler(extras) {
    _reporterNs.report("EmitterDurationHandler", "./EmitterActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitterElapsedTimeHandler(extras) {
    _reporterNs.report("EmitterElapsedTimeHandler", "./EmitterActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitterLoopHandler(extras) {
    _reporterNs.report("EmitterLoopHandler", "./EmitterActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitterLoopIntervalHandler(extras) {
    _reporterNs.report("EmitterLoopIntervalHandler", "./EmitterActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitterPerEmitIntervalHandler(extras) {
    _reporterNs.report("EmitterPerEmitIntervalHandler", "./EmitterActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitterPerEmitCountHandler(extras) {
    _reporterNs.report("EmitterPerEmitCountHandler", "./EmitterActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitterPerEmitOffsetXHandler(extras) {
    _reporterNs.report("EmitterPerEmitOffsetXHandler", "./EmitterActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitterAngleHandler(extras) {
    _reporterNs.report("EmitterAngleHandler", "./EmitterActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitterCountHandler(extras) {
    _reporterNs.report("EmitterCountHandler", "./EmitterActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletDurationHandler(extras) {
    _reporterNs.report("BulletDurationHandler", "./BulletActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletElapsedTimeHandler(extras) {
    _reporterNs.report("BulletElapsedTimeHandler", "./BulletActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletPosXHandler(extras) {
    _reporterNs.report("BulletPosXHandler", "./BulletActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletPosYHandler(extras) {
    _reporterNs.report("BulletPosYHandler", "./BulletActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletDamageHandler(extras) {
    _reporterNs.report("BulletDamageHandler", "./BulletActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletSpeedHandler(extras) {
    _reporterNs.report("BulletSpeedHandler", "./BulletActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletSpeedAngleHandler(extras) {
    _reporterNs.report("BulletSpeedAngleHandler", "./BulletActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletAccelerationHandler(extras) {
    _reporterNs.report("BulletAccelerationHandler", "./BulletActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletAccelerationAngleHandler(extras) {
    _reporterNs.report("BulletAccelerationAngleHandler", "./BulletActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletScaleHandler(extras) {
    _reporterNs.report("BulletScaleHandler", "./BulletActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletColorRHandler(extras) {
    _reporterNs.report("BulletColorRHandler", "./BulletActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletColorGHandler(extras) {
    _reporterNs.report("BulletColorGHandler", "./BulletActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletColorBHandler(extras) {
    _reporterNs.report("BulletColorBHandler", "./BulletActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletColorAHandler(extras) {
    _reporterNs.report("BulletColorAHandler", "./BulletActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletFaceMovingDirHandler(extras) {
    _reporterNs.report("BulletFaceMovingDirHandler", "./BulletActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletTrackingTargetHandler(extras) {
    _reporterNs.report("BulletTrackingTargetHandler", "./BulletActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletDestructiveHandler(extras) {
    _reporterNs.report("BulletDestructiveHandler", "./BulletActionHandlers", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletDestructiveOnHitHandler(extras) {
    _reporterNs.report("BulletDestructiveOnHitHandler", "./BulletActionHandlers", _context.meta, extras);
  }

  _export("ActionRegistry", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      EmitterActiveHandler = _unresolved_2.EmitterActiveHandler;
      EmitterInitialDelayHandler = _unresolved_2.EmitterInitialDelayHandler;
      EmitterPrewarmHandler = _unresolved_2.EmitterPrewarmHandler;
      EmitterPrewarmDurationHandler = _unresolved_2.EmitterPrewarmDurationHandler;
      EmitterDurationHandler = _unresolved_2.EmitterDurationHandler;
      EmitterElapsedTimeHandler = _unresolved_2.EmitterElapsedTimeHandler;
      EmitterLoopHandler = _unresolved_2.EmitterLoopHandler;
      EmitterLoopIntervalHandler = _unresolved_2.EmitterLoopIntervalHandler;
      EmitterPerEmitIntervalHandler = _unresolved_2.EmitterPerEmitIntervalHandler;
      EmitterPerEmitCountHandler = _unresolved_2.EmitterPerEmitCountHandler;
      EmitterPerEmitOffsetXHandler = _unresolved_2.EmitterPerEmitOffsetXHandler;
      EmitterAngleHandler = _unresolved_2.EmitterAngleHandler;
      EmitterCountHandler = _unresolved_2.EmitterCountHandler;
    }, function (_unresolved_3) {
      BulletDurationHandler = _unresolved_3.BulletDurationHandler;
      BulletElapsedTimeHandler = _unresolved_3.BulletElapsedTimeHandler;
      BulletPosXHandler = _unresolved_3.BulletPosXHandler;
      BulletPosYHandler = _unresolved_3.BulletPosYHandler;
      BulletDamageHandler = _unresolved_3.BulletDamageHandler;
      BulletSpeedHandler = _unresolved_3.BulletSpeedHandler;
      BulletSpeedAngleHandler = _unresolved_3.BulletSpeedAngleHandler;
      BulletAccelerationHandler = _unresolved_3.BulletAccelerationHandler;
      BulletAccelerationAngleHandler = _unresolved_3.BulletAccelerationAngleHandler;
      BulletScaleHandler = _unresolved_3.BulletScaleHandler;
      BulletColorRHandler = _unresolved_3.BulletColorRHandler;
      BulletColorGHandler = _unresolved_3.BulletColorGHandler;
      BulletColorBHandler = _unresolved_3.BulletColorBHandler;
      BulletColorAHandler = _unresolved_3.BulletColorAHandler;
      BulletFaceMovingDirHandler = _unresolved_3.BulletFaceMovingDirHandler;
      BulletTrackingTargetHandler = _unresolved_3.BulletTrackingTargetHandler;
      BulletDestructiveHandler = _unresolved_3.BulletDestructiveHandler;
      BulletDestructiveOnHitHandler = _unresolved_3.BulletDestructiveOnHitHandler;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "964c6ykei1JzJZe7fKeBgTx", "ActionRegistry", undefined);

      __checkObsolete__(['_decorator']);

      ({
        ccclass
      } = _decorator);
      /**
       * Registry for managing action handlers
       */

      _export("ActionRegistry", ActionRegistry = class ActionRegistry {
        /**
         * Initialize the registry with all available handlers
         */
        static initialize() {
          if (this.initialized) {
            console.warn('ActionRegistry already initialized');
            return;
          }

          console.log('Initializing ActionRegistry...'); // Register emitter handlers

          this.register(new (_crd && EmitterActiveHandler === void 0 ? (_reportPossibleCrUseOfEmitterActiveHandler({
            error: Error()
          }), EmitterActiveHandler) : EmitterActiveHandler)());
          this.register(new (_crd && EmitterInitialDelayHandler === void 0 ? (_reportPossibleCrUseOfEmitterInitialDelayHandler({
            error: Error()
          }), EmitterInitialDelayHandler) : EmitterInitialDelayHandler)());
          this.register(new (_crd && EmitterPrewarmHandler === void 0 ? (_reportPossibleCrUseOfEmitterPrewarmHandler({
            error: Error()
          }), EmitterPrewarmHandler) : EmitterPrewarmHandler)());
          this.register(new (_crd && EmitterPrewarmDurationHandler === void 0 ? (_reportPossibleCrUseOfEmitterPrewarmDurationHandler({
            error: Error()
          }), EmitterPrewarmDurationHandler) : EmitterPrewarmDurationHandler)());
          this.register(new (_crd && EmitterDurationHandler === void 0 ? (_reportPossibleCrUseOfEmitterDurationHandler({
            error: Error()
          }), EmitterDurationHandler) : EmitterDurationHandler)());
          this.register(new (_crd && EmitterElapsedTimeHandler === void 0 ? (_reportPossibleCrUseOfEmitterElapsedTimeHandler({
            error: Error()
          }), EmitterElapsedTimeHandler) : EmitterElapsedTimeHandler)());
          this.register(new (_crd && EmitterLoopHandler === void 0 ? (_reportPossibleCrUseOfEmitterLoopHandler({
            error: Error()
          }), EmitterLoopHandler) : EmitterLoopHandler)());
          this.register(new (_crd && EmitterLoopIntervalHandler === void 0 ? (_reportPossibleCrUseOfEmitterLoopIntervalHandler({
            error: Error()
          }), EmitterLoopIntervalHandler) : EmitterLoopIntervalHandler)());
          this.register(new (_crd && EmitterPerEmitIntervalHandler === void 0 ? (_reportPossibleCrUseOfEmitterPerEmitIntervalHandler({
            error: Error()
          }), EmitterPerEmitIntervalHandler) : EmitterPerEmitIntervalHandler)());
          this.register(new (_crd && EmitterPerEmitCountHandler === void 0 ? (_reportPossibleCrUseOfEmitterPerEmitCountHandler({
            error: Error()
          }), EmitterPerEmitCountHandler) : EmitterPerEmitCountHandler)());
          this.register(new (_crd && EmitterPerEmitOffsetXHandler === void 0 ? (_reportPossibleCrUseOfEmitterPerEmitOffsetXHandler({
            error: Error()
          }), EmitterPerEmitOffsetXHandler) : EmitterPerEmitOffsetXHandler)());
          this.register(new (_crd && EmitterAngleHandler === void 0 ? (_reportPossibleCrUseOfEmitterAngleHandler({
            error: Error()
          }), EmitterAngleHandler) : EmitterAngleHandler)());
          this.register(new (_crd && EmitterCountHandler === void 0 ? (_reportPossibleCrUseOfEmitterCountHandler({
            error: Error()
          }), EmitterCountHandler) : EmitterCountHandler)()); // Register bullet handlers

          this.register(new (_crd && BulletDurationHandler === void 0 ? (_reportPossibleCrUseOfBulletDurationHandler({
            error: Error()
          }), BulletDurationHandler) : BulletDurationHandler)());
          this.register(new (_crd && BulletElapsedTimeHandler === void 0 ? (_reportPossibleCrUseOfBulletElapsedTimeHandler({
            error: Error()
          }), BulletElapsedTimeHandler) : BulletElapsedTimeHandler)());
          this.register(new (_crd && BulletPosXHandler === void 0 ? (_reportPossibleCrUseOfBulletPosXHandler({
            error: Error()
          }), BulletPosXHandler) : BulletPosXHandler)());
          this.register(new (_crd && BulletPosYHandler === void 0 ? (_reportPossibleCrUseOfBulletPosYHandler({
            error: Error()
          }), BulletPosYHandler) : BulletPosYHandler)());
          this.register(new (_crd && BulletDamageHandler === void 0 ? (_reportPossibleCrUseOfBulletDamageHandler({
            error: Error()
          }), BulletDamageHandler) : BulletDamageHandler)());
          this.register(new (_crd && BulletSpeedHandler === void 0 ? (_reportPossibleCrUseOfBulletSpeedHandler({
            error: Error()
          }), BulletSpeedHandler) : BulletSpeedHandler)());
          this.register(new (_crd && BulletSpeedAngleHandler === void 0 ? (_reportPossibleCrUseOfBulletSpeedAngleHandler({
            error: Error()
          }), BulletSpeedAngleHandler) : BulletSpeedAngleHandler)());
          this.register(new (_crd && BulletAccelerationHandler === void 0 ? (_reportPossibleCrUseOfBulletAccelerationHandler({
            error: Error()
          }), BulletAccelerationHandler) : BulletAccelerationHandler)());
          this.register(new (_crd && BulletAccelerationAngleHandler === void 0 ? (_reportPossibleCrUseOfBulletAccelerationAngleHandler({
            error: Error()
          }), BulletAccelerationAngleHandler) : BulletAccelerationAngleHandler)());
          this.register(new (_crd && BulletScaleHandler === void 0 ? (_reportPossibleCrUseOfBulletScaleHandler({
            error: Error()
          }), BulletScaleHandler) : BulletScaleHandler)());
          this.register(new (_crd && BulletColorRHandler === void 0 ? (_reportPossibleCrUseOfBulletColorRHandler({
            error: Error()
          }), BulletColorRHandler) : BulletColorRHandler)());
          this.register(new (_crd && BulletColorGHandler === void 0 ? (_reportPossibleCrUseOfBulletColorGHandler({
            error: Error()
          }), BulletColorGHandler) : BulletColorGHandler)());
          this.register(new (_crd && BulletColorBHandler === void 0 ? (_reportPossibleCrUseOfBulletColorBHandler({
            error: Error()
          }), BulletColorBHandler) : BulletColorBHandler)());
          this.register(new (_crd && BulletColorAHandler === void 0 ? (_reportPossibleCrUseOfBulletColorAHandler({
            error: Error()
          }), BulletColorAHandler) : BulletColorAHandler)());
          this.register(new (_crd && BulletFaceMovingDirHandler === void 0 ? (_reportPossibleCrUseOfBulletFaceMovingDirHandler({
            error: Error()
          }), BulletFaceMovingDirHandler) : BulletFaceMovingDirHandler)());
          this.register(new (_crd && BulletTrackingTargetHandler === void 0 ? (_reportPossibleCrUseOfBulletTrackingTargetHandler({
            error: Error()
          }), BulletTrackingTargetHandler) : BulletTrackingTargetHandler)());
          this.register(new (_crd && BulletDestructiveHandler === void 0 ? (_reportPossibleCrUseOfBulletDestructiveHandler({
            error: Error()
          }), BulletDestructiveHandler) : BulletDestructiveHandler)());
          this.register(new (_crd && BulletDestructiveOnHitHandler === void 0 ? (_reportPossibleCrUseOfBulletDestructiveOnHitHandler({
            error: Error()
          }), BulletDestructiveOnHitHandler) : BulletDestructiveOnHitHandler)());
          this.initialized = true;
          console.log(`ActionRegistry initialized with ${this.handlers.size} handlers`);
        }
        /**
         * Register a new action handler
         */


        static register(handler) {
          if (this.handlers.has(handler.actionType)) {
            console.warn(`Handler for action type ${handler.actionType} already exists, overwriting`);
          }

          this.handlers.set(handler.actionType, handler);
          console.log(`Registered handler: ${handler.getDisplayName == null ? void 0 : handler.getDisplayName()} (${handler.actionType})`);
        }
        /**
         * Get a handler for a specific action type
         */


        static getHandler(actionType) {
          if (!this.initialized) {
            console.error('ActionRegistry not initialized. Call ActionRegistry.initialize() first.');
            return null;
          }

          return this.handlers.get(actionType) || null;
        }
        /**
         * Get all registered handlers
         */


        static getAllHandlers() {
          return Array.from(this.handlers.values());
        }
        /**
         * Get handlers by value type
         */


        static getHandlersByValueType(valueType) {
          return this.getAllHandlers().filter(handler => handler.valueType === valueType);
        }
        /**
         * Check if a handler exists for an action type
         */


        static hasHandler(actionType) {
          return this.handlers.has(actionType);
        }
        /**
         * Get the number of registered handlers
         */


        static getHandlerCount() {
          return this.handlers.size;
        }
        /**
         * Clear all handlers (useful for testing)
         */


        static clear() {
          this.handlers.clear();
          this.initialized = false;
          console.log('ActionRegistry cleared');
        }
        /**
         * Get debug information about registered handlers
         */


        static getDebugInfo() {
          const handlers = this.getAllHandlers();
          const info = handlers.map(handler => `${handler.actionType}: ${handler.getDisplayName == null ? void 0 : handler.getDisplayName()} (${handler.valueType})`).join('\n');
          return `ActionRegistry Debug Info:\n${info}`;
        }

      });

      ActionRegistry.handlers = new Map();
      ActionRegistry.initialized = false;

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c217f733f4a9c0479a77b19697bfc16ee0118256.js.map