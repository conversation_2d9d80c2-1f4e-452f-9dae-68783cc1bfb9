import { _decorator, misc, Component, Enum, Vec3 } from 'cc';
import { eEmitterActionType, eBulletActionType, EmitterActionData, BulletActionData } from '../data/EventActionData';
import { eConditionGroupOp, EmitterGroupData, BulletGroupData } from '../data/EventGroupData';
import { eEventConditionOp } from '../data/EventConditionData';
import { Bullet } from './Bullet';
import { eEmitterStatus, Emitter } from './Emitter';
import { eEasing } from '../move/IMovable';
import { BulletSystem } from './BulletSystem';

export class EventActionRunner {

    private startValue: number = 0;
    private targetValue: number = 0;
    private elapsedTime: number = 0;
    private _isCompleted: boolean = false;
    get isCompleted(): boolean {
        return this._isCompleted;
    }
    
    get isRunning(): boolean {
        return !this._isCompleted && this.elapsedTime > 0;
    }

    // readonly EventActionData
    readonly data: EmitterActionData | BulletActionData;
    owner: Emitter | Bullet = null;

    constructor(owner: Emitter | Bullet, data: EmitterActionData | BulletActionData) {
        this.owner = owner;
        this.data = data;
    }

    getOwnerAsEmitter(): Emitter {
        return this.owner as Emitter;
    }

    getOwnerAsBullet(): Bullet {
        return this.owner as Bullet;
    }

    static canLerp(actionType: number): boolean {
        if (actionType < eBulletActionType.Bullet_Duration) {
            switch (actionType) {
                case eEmitterActionType.Emitter_InitialDelay:
                case eEmitterActionType.Emitter_PrewarmDuration:
                case eEmitterActionType.Emitter_Duration:
                case eEmitterActionType.Emitter_ElapsedTime:
                case eEmitterActionType.Emitter_LoopInterval:
                case eEmitterActionType.Emitter_PerEmitInterval:
                case eEmitterActionType.Emitter_PerEmitCount:
                case eEmitterActionType.Emitter_PerEmitOffsetX:
                case eEmitterActionType.Emitter_Angle:
                    return true;
                default:
                    return false;
            }
        }
        else {
            switch (actionType) {
                case eBulletActionType.Bullet_Duration:
                case eBulletActionType.Bullet_ElapsedTime:
                case eBulletActionType.Bullet_PosX:
                case eBulletActionType.Bullet_PosY:
                case eBulletActionType.Bullet_Damage:
                case eBulletActionType.Bullet_Speed:
                case eBulletActionType.Bullet_SpeedAngle:
                case eBulletActionType.Bullet_Acceleration:
                case eBulletActionType.Bullet_AccelerationAngle:
                case eBulletActionType.Bullet_Scale:
                case eBulletActionType.Bullet_ColorR:
                case eBulletActionType.Bullet_ColorG:
                case eBulletActionType.Bullet_ColorB:
                case eBulletActionType.Bullet_ColorA:
                    return true;
                default:
                    return false;
            }
        }
    }

    getInitialValue(): number {
        switch (this.data.actionType) {
            case eEmitterActionType.Emitter_Active:
                return this.getOwnerAsEmitter().status != eEmitterStatus.None ? 1 : 0;
            case eEmitterActionType.Emitter_InitialDelay:
                return this.getOwnerAsEmitter().initialDelay;
            case eEmitterActionType.Emitter_PrewarmDuration:
                return this.getOwnerAsEmitter().preWarmDuration;
            case eEmitterActionType.Emitter_Duration:
                return this.getOwnerAsEmitter().emitDuration;
            case eEmitterActionType.Emitter_ElapsedTime:
                return this.getOwnerAsEmitter().totalElapsedTime;
            case eEmitterActionType.Emitter_LoopInterval:
                return this.getOwnerAsEmitter().loopInterval;
            case eEmitterActionType.Emitter_PerEmitInterval:
                return this.getOwnerAsEmitter().perEmitInterval;
            case eEmitterActionType.Emitter_PerEmitCount:
                return this.getOwnerAsEmitter().perEmitCount;
            case eEmitterActionType.Emitter_PerEmitOffsetX:
                return this.getOwnerAsEmitter().perEmitOffsetX;
            case eEmitterActionType.Emitter_Angle:
                return this.getOwnerAsEmitter().angle;
            case eEmitterActionType.Emitter_Count:
                return this.getOwnerAsEmitter().count;

            // 子弹部分
            case eBulletActionType.Bullet_Duration:
                return this.getOwnerAsBullet().duration;
            case eBulletActionType.Bullet_ElapsedTime:
                return this.getOwnerAsBullet().elapsedTime;
            case eBulletActionType.Bullet_PosX:
                return this.getOwnerAsBullet().node.getPosition().x;
            case eBulletActionType.Bullet_PosY:
                return this.getOwnerAsBullet().node.getPosition().y;
            case eBulletActionType.Bullet_Damage:
                return this.getOwnerAsBullet().damage;
            case eBulletActionType.Bullet_Speed:
                return this.getOwnerAsBullet().mover.speed;
            case eBulletActionType.Bullet_SpeedAngle:
                return this.getOwnerAsBullet().mover.speedAngle;
            case eBulletActionType.Bullet_Acceleration:
                return this.getOwnerAsBullet().mover.acceleration;
            case eBulletActionType.Bullet_AccelerationAngle:
                return this.getOwnerAsBullet().mover.accelerationAngle;
            case eBulletActionType.Bullet_Scale:
                return this.getOwnerAsBullet().node.getScale().x;
            case eBulletActionType.Bullet_ColorR:
                return this.getOwnerAsBullet().bulletSprite.color.r;
            case eBulletActionType.Bullet_ColorG:
                return this.getOwnerAsBullet().bulletSprite.color.g;
            case eBulletActionType.Bullet_ColorB:
                return this.getOwnerAsBullet().bulletSprite.color.b;
            case eBulletActionType.Bullet_ColorA:
                return this.getOwnerAsBullet().bulletSprite.color.a;
            case eBulletActionType.Bullet_FaceMovingDir:
                return this.getOwnerAsBullet().mover.isFacingMoveDir ? 1 : 0;
            case eBulletActionType.Bullet_TrackingTarget:
                return this.getOwnerAsBullet().mover.isTrackingTarget ? 1 : 0;
            case eBulletActionType.Bullet_Destructive:
                return this.getOwnerAsBullet().isDestructive ? 1 : 0;
            case eBulletActionType.Bullet_DestructiveOnHit:
                return this.getOwnerAsBullet().isDestructiveOnHit ? 1 : 0;

            default: break;
        }

        return 0;
    }

    getTargetValue(): number {
        if (EventActionRunner.canLerp(this.data.actionType)) {
            if (this.data.isRandom) {
                return Math.random() * (this.data.maxValue - this.data.minValue) + this.data.minValue;
            }
            return this.data.minValue;
        }

        return this.data.boolValue ? 1 : 0;
    }

    start() {
        if (!this.data) {
            console.error('EventActionRunner: data is null');
            return;
        }
        
        this.startValue = this.getInitialValue();
        this.targetValue = this.getTargetValue();
        this.elapsedTime = 0;
        this._isCompleted = false;
    }

    tick(dt: number) {
        // Update current time
        this.elapsedTime += dt;

        if (this.elapsedTime >= this.data.duration) {
            this._isCompleted = true;
            this.apply(this.targetValue);
        }
        else {
            if (EventActionRunner.canLerp(this.data.actionType)) {
                const currentValue = this.lerpValue(this.startValue, this.targetValue);
                this.apply(currentValue);
            }
        }
    }

    lerpValue(startValue: number, targetValue: number): number {
        // Calculate progress ratio (0 to 1)
        let progress = Math.min(1.0, this.elapsedTime / this.data.duration);
        // Apply easing function
        let easedProgress = EventActionRunner.applyEasing(this.data.easing, progress);
        // Interpolate between start value and target value
        return startValue + (targetValue - startValue) * easedProgress;
    }

    apply(currentValue: number) {
        if (this.data.actionType < eBulletActionType.Bullet_Duration) {
            this.getOwnerAsEmitter().applyAction(this.data.actionType as eEmitterActionType, currentValue);
        }
        else {
            this.getOwnerAsBullet().applyAction(this.data.actionType as eBulletActionType, currentValue);
        }
    }
    
    static applyEasing(easing: eEasing, t: number): number {
        switch (easing) {
            case eEasing.Linear:
                return t;

            case eEasing.InSine:
                return 1 - Math.cos(t * Math.PI / 2);

            case eEasing.OutSine:
                return Math.sin(t * Math.PI / 2);

            case eEasing.InOutSine:
                return -(Math.cos(Math.PI * t) - 1) / 2;

            case eEasing.InQuad:
                return t * t;

            case eEasing.OutQuad:
                return 1 - (1 - t) * (1 - t);

            case eEasing.InOutQuad:
                return t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2;

            default:
                return t;
        }
    }
}

export class EventGroupRunner {
    readonly data: EmitterGroupData | BulletGroupData;
    owner: Emitter | Bullet = null;
    isTriggered: boolean = false;
    isConditionAMet: boolean = false;
    isConditionBMet: boolean = false;

    constructor(data: EmitterGroupData | BulletGroupData) {
        this.data = data;
    }

    public init(owner: Emitter | Bullet) {
        this.owner = owner;
        this.isTriggered = this.isConditionAMet = this.isConditionBMet = false;
        this.registerListeners();
    }

    private tryTrigger() {
        switch (this.data.groupOp) {
            case eConditionGroupOp.And:
                this.isTriggered = this.isConditionAMet && this.isConditionBMet;
                break;
            case eConditionGroupOp.Or:
                this.isTriggered = this.isConditionAMet || this.isConditionBMet;
                break;
        }

        if (this.isTriggered) {
            for (const action of this.data.actions) {
                BulletSystem.createActionRunner(this.owner, action);
            }
            this.unRegisterListeners();
        }
    }

    private registerListeners() {
        if (this.data.conditionA) {
            this.isConditionAMet = false;
            BulletSystem.on(this.data.conditionA.eventType, this.onConditionAChanged.bind(this));
        }
        else {
            this.isConditionAMet = true;
        }

        if (this.data.conditionB) {
            this.isConditionBMet = false;
            BulletSystem.on(this.data.conditionB.eventType, this.onConditionBChanged.bind(this));
        }
        else {
            this.isConditionBMet = true;
        }
    }

    private unRegisterListeners() {
        if (this.data.conditionA) {
            BulletSystem.off(this.data.conditionA.eventType, this.onConditionAChanged.bind(this));
        }

        if (this.data.conditionB) {
            BulletSystem.off(this.data.conditionB.eventType, this.onConditionBChanged.bind(this));
        }
    }

    private onConditionAChanged(value: number) {
        // Handle condition A change
        this.isConditionAMet = EventGroupRunner.compareValue(this.data.conditionA.eventOp, value, this.data.conditionA.targetValue);
        this.tryTrigger();
    }

    private onConditionBChanged(value: number) {
        // Handle condition B change
        this.isConditionBMet = EventGroupRunner.compareValue(this.data.conditionB.eventOp, value, this.data.conditionB.targetValue);
        this.tryTrigger();
    }


    static compareValue(op: eEventConditionOp, a: number, b: number): boolean {
        switch (op) {
            case eEventConditionOp.Equal:
                return a === b;
            case eEventConditionOp.NotEqual:
                return a !== b;
            case eEventConditionOp.Greater:
                return a > b;
            case eEventConditionOp.Less:
                return a < b;
            case eEventConditionOp.GreaterEqual:
                return a >= b;
            case eEventConditionOp.LessEqual:
                return a <= b;
            default:
                return false;
        }
    }
}