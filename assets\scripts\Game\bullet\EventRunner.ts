import { _decorator, misc, Component, Enum, Vec3 } from 'cc';
import { eEmitterActionType, eBulletActionType, EmitterActionData, BulletActionData } from '../data/EventActionData';
import { eConditionGroupOp, EmitterGroupData, BulletGroupData } from '../data/EventGroupData';
import { eEventConditionOp } from '../data/EventConditionData';
import { Bullet } from './Bullet';
import { eEmitterStatus, Emitter } from './Emitter';
import { BulletSystem } from './BulletSystem';

export class EmitterEventGroup {
    readonly data: EmitterGroupData;
    owner: Emitter = null;
    isTriggered: boolean = false;
    isConditionAMet: boolean = false;
    isConditionBMet: boolean = false;

    constructor(data: EmitterGroupData) {
        this.data = data;
    }

    public init(owner: Emitter) {
        this.owner = owner;
        this.isTriggered = this.isConditionAMet = this.isConditionBMet = false;
        this.registerListeners();
    }

    private tryTrigger() {
        switch (this.data.groupOp) {
            case eConditionGroupOp.And:
                this.isTriggered = this.isConditionAMet && this.isConditionBMet;
                break;
            case eConditionGroupOp.Or:
                this.isTriggered = this.isConditionAMet || this.isConditionBMet;
                break;
        }

        if (this.isTriggered) {
            for (const action of this.data.actions) {
                BulletSystem.createActionRunner(this.owner, action);
            }
            this.unRegisterListeners();
        }
    }

    private registerListeners() {
        if (this.data.conditionA) {
            this.isConditionAMet = false;
            BulletSystem.on(this.data.conditionA.eventType, this.onConditionAChanged.bind(this));
        }
        else {
            this.isConditionAMet = true;
        }

        if (this.data.conditionB) {
            this.isConditionBMet = false;
            BulletSystem.on(this.data.conditionB.eventType, this.onConditionBChanged.bind(this));
        }
        else {
            this.isConditionBMet = true;
        }
    }

    private unRegisterListeners() {
        if (this.data.conditionA) {
            BulletSystem.off(this.data.conditionA.eventType, this.onConditionAChanged.bind(this));
        }

        if (this.data.conditionB) {
            BulletSystem.off(this.data.conditionB.eventType, this.onConditionBChanged.bind(this));
        }
    }

    private onConditionAChanged(value: number) {
        // Handle condition A change
        this.isConditionAMet = EventGroupRunner.compareValue(this.data.conditionA.eventOp, value, this.data.conditionA.targetValue);
        this.tryTrigger();
    }

    private onConditionBChanged(value: number) {
        // Handle condition B change
        this.isConditionBMet = EventGroupRunner.compareValue(this.data.conditionB.eventOp, value, this.data.conditionB.targetValue);
        this.tryTrigger();
    }

    static compareValue(op: eEventConditionOp, a: number, b: number): boolean {
        switch (op) {
            case eEventConditionOp.Equal:
                return a === b;
            case eEventConditionOp.NotEqual:
                return a !== b;
            case eEventConditionOp.Greater:
                return a > b;
            case eEventConditionOp.Less:
                return a < b;
            case eEventConditionOp.GreaterEqual:
                return a >= b;
            case eEventConditionOp.LessEqual:
                return a <= b;
            default:
                return false;
        }
    }
}