{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/actions/ActionValue.ts"], "names": ["NumberInterpolator", "BooleanInterpolator", "StringInterpolator", "RPNInterpolator", "InterpolatorFactory", "_decorator", "ccclass", "canInterpolate", "interpolate", "start", "target", "progress", "getRandomValue", "min", "max", "Math", "random", "create", "valueType", "console", "warn"], "mappings": ";;;8EAoBaA,kB,EAiBAC,mB,EAkBAC,kB,EAkBAC,e,EAoBAC,mB;;;;;;;;;;;;;;;AA7FJC,MAAAA,U,OAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA;AAAF,O,GAAcD,U;AAEpB;AACA;AACA;;AAGA;AACA;AACA;;AAOA;AACA;AACA;oCACaL,kB,GAAN,MAAMA,kBAAN,CAA+D;AAClEO,QAAAA,cAAc,GAAY;AACtB,iBAAO,IAAP;AACH;;AAEDC,QAAAA,WAAW,CAACC,KAAD,EAAgBC,MAAhB,EAAgCC,QAAhC,EAA0D;AACjE,iBAAOF,KAAK,GAAG,CAACC,MAAM,GAAGD,KAAV,IAAmBE,QAAlC;AACH;;AAEDC,QAAAA,cAAc,CAACC,GAAD,EAAcC,GAAd,EAAmC;AAC7C,iBAAOC,IAAI,CAACC,MAAL,MAAiBF,GAAG,GAAGD,GAAvB,IAA8BA,GAArC;AACH;;AAXiE,O;AActE;AACA;AACA;;;qCACaZ,mB,GAAN,MAAMA,mBAAN,CAAiE;AACpEM,QAAAA,cAAc,GAAY;AACtB,iBAAO,KAAP;AACH;;AAEDC,QAAAA,WAAW,CAACC,KAAD,EAAiBC,MAAjB,EAAkCC,QAAlC,EAA6D;AACpE;AACA,iBAAOA,QAAQ,IAAI,GAAZ,GAAkBD,MAAlB,GAA2BD,KAAlC;AACH;;AAEDG,QAAAA,cAAc,CAACC,GAAD,EAAeC,GAAf,EAAsC;AAChD,iBAAOC,IAAI,CAACC,MAAL,KAAgB,GAAvB;AACH;;AAZmE,O;AAexE;AACA;AACA;;;oCACad,kB,GAAN,MAAMA,kBAAN,CAA+D;AAClEK,QAAAA,cAAc,GAAY;AACtB,iBAAO,KAAP;AACH;;AAEDC,QAAAA,WAAW,CAACC,KAAD,EAAgBC,MAAhB,EAAgCC,QAAhC,EAA0D;AACjE,iBAAOA,QAAQ,IAAI,GAAZ,GAAkBD,MAAlB,GAA2BD,KAAlC;AACH;;AAEDG,QAAAA,cAAc,CAACC,GAAD,EAAcC,GAAd,EAAmC;AAC7C;AACA,iBAAOC,IAAI,CAACC,MAAL,KAAgB,GAAhB,GAAsBF,GAAtB,GAA4BD,GAAnC;AACH;;AAZiE,O;AAetE;AACA;AACA;;;iCACaV,e,GAAN,MAAMA,eAAN,CAA4D;AAC/DI,QAAAA,cAAc,GAAY;AACtB,iBAAO,KAAP,CADsB,CACR;AACjB;;AAEDC,QAAAA,WAAW,CAACC,KAAD,EAAgBC,MAAhB,EAAgCC,QAAhC,EAA0D;AACjE;AACA;AACA,iBAAOA,QAAQ,IAAI,GAAZ,GAAkBD,MAAlB,GAA2BD,KAAlC;AACH;;AAEDG,QAAAA,cAAc,CAACC,GAAD,EAAcC,GAAd,EAAmC;AAC7C;AACA,iBAAOC,IAAI,CAACC,MAAL,KAAgB,GAAhB,GAAsBF,GAAtB,GAA4BD,GAAnC;AACH;;AAd8D,O;AAiBnE;AACA;AACA;;;qCACaT,mB,GAAN,MAAMA,mBAAN,CAA0B;AAChB,eAANa,MAAM,CAACC,SAAD,EAAsF;AAC/F,kBAAQA,SAAR;AACI,iBAAK,QAAL;AACI,qBAAO,IAAIlB,kBAAJ,EAAP;;AACJ,iBAAK,SAAL;AACI,qBAAO,IAAIC,mBAAJ,EAAP;;AACJ,iBAAK,QAAL;AACI,qBAAO,IAAIC,kBAAJ,EAAP;;AACJ,iBAAK,KAAL;AACI,qBAAO,IAAIC,eAAJ,EAAP;;AACJ;AACIgB,cAAAA,OAAO,CAACC,IAAR,CAAc,uBAAsBF,SAAU,qCAA9C;AACA,qBAAO,IAAIlB,kBAAJ,EAAP;AAXR;AAaH;;AAf4B,O", "sourcesContent": ["import { _decorator } from 'cc';\nconst { ccclass } = _decorator;\n\n/**\n * Generic value type that can hold different types\n */\nexport type ActionValue = number | boolean | string | any;\n\n/**\n * Interface for value interpolation\n */\nexport interface IValueInterpolator<T> {\n    canInterpolate(): boolean;\n    interpolate(start: T, target: T, progress: number): T;\n    getRandomValue(min: T, max: T): T;\n}\n\n/**\n * Number interpolator - supports smooth interpolation between numeric values\n */\nexport class NumberInterpolator implements IValueInterpolator<number> {\n    canInterpolate(): boolean { \n        return true; \n    }\n    \n    interpolate(start: number, target: number, progress: number): number {\n        return start + (target - start) * progress;\n    }\n    \n    getRandomValue(min: number, max: number): number {\n        return Math.random() * (max - min) + min;\n    }\n}\n\n/**\n * Boolean interpolator - no smooth interpolation, switches at threshold\n */\nexport class BooleanInterpolator implements IValueInterpolator<boolean> {\n    canInterpolate(): boolean { \n        return false; \n    }\n    \n    interpolate(start: boolean, target: boolean, progress: number): boolean {\n        // Switch at 50% progress, or immediately if no duration\n        return progress >= 0.5 ? target : start;\n    }\n    \n    getRandomValue(min: boolean, max: boolean): boolean {\n        return Math.random() > 0.5;\n    }\n}\n\n/**\n * String interpolator - no interpolation, switches at completion\n */\nexport class StringInterpolator implements IValueInterpolator<string> {\n    canInterpolate(): boolean { \n        return false; \n    }\n    \n    interpolate(start: string, target: string, progress: number): string {\n        return progress >= 1.0 ? target : start;\n    }\n    \n    getRandomValue(min: string, max: string): string {\n        // For strings, randomly pick between min and max\n        return Math.random() > 0.5 ? max : min;\n    }\n}\n\n/**\n * RPN (Reverse Polish Notation) interpolator - for future expression support\n */\nexport class RPNInterpolator implements IValueInterpolator<string> {\n    canInterpolate(): boolean { \n        return false; // RPN expressions are evaluated, not interpolated\n    }\n    \n    interpolate(start: string, target: string, progress: number): string {\n        // For RPN, we would evaluate the expression at the current progress\n        // This is a placeholder for future implementation\n        return progress >= 1.0 ? target : start;\n    }\n    \n    getRandomValue(min: string, max: string): string {\n        // For RPN expressions, this would need special handling\n        return Math.random() > 0.5 ? max : min;\n    }\n}\n\n/**\n * Factory for creating appropriate interpolators based on value type\n */\nexport class InterpolatorFactory {\n    static create(valueType: 'number' | 'boolean' | 'string' | 'rpn'): IValueInterpolator<ActionValue> {\n        switch (valueType) {\n            case 'number': \n                return new NumberInterpolator();\n            case 'boolean': \n                return new BooleanInterpolator();\n            case 'string': \n                return new StringInterpolator();\n            case 'rpn': \n                return new RPNInterpolator();\n            default: \n                console.warn(`Unknown value type: ${valueType}, defaulting to number interpolator`);\n                return new NumberInterpolator();\n        }\n    }\n}\n"]}