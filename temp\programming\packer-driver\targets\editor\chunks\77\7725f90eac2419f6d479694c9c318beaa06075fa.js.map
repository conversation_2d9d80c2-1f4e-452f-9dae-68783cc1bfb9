{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/Emitter.ts"], "names": ["_decorator", "misc", "Prefab", "Component", "EDITOR", "Bullet", "EmitterData", "ObjectPool", "BulletSystem", "eEmitterActionType", "ccclass", "executeInEditMode", "property", "playOnFocus", "degreesToRadians", "radiansToDegrees", "eEmitterStatus", "Emitter", "type", "displayName", "isOnlyInScreen", "isPreWarm", "isLoop", "initialDelay", "preWarmDuration", "emitDuration", "emitInterval", "emitPower", "loopInterval", "perEmitCount", "perEmitInterval", "perEmitOffsetX", "angle", "count", "arc", "radius", "updateInEditor", "_isActive", "_status", "None", "_statusElapsedTime", "_totalElapsedTime", "_isEmitting", "_nextEmitTime", "isActive", "status", "isEmitting", "statusElapsedTime", "totalElapsedTime", "start", "onCreateEmitter", "resetProperties", "update", "dt", "console", "log", "tick", "tickBullets", "tickActionRunners", "resetInEditor", "onFocusInEditor", "onLostFocusInEditor", "bulletParent", "<PERSON><PERSON><PERSON><PERSON>", "destroyAllBullets", "emitterData", "changeStatus", "scheduleNextEmit", "startEmitting", "stopEmitting", "unscheduleAllCallbacks", "canEmit", "emit", "i", "localIndex", "j", "localPerEmitIndex", "scheduleOnce", "emitSingle", "tryEmit", "index", "perEmitIndex", "direction", "getSpawnDirection", "position", "getSpawnPosition", "createBullet", "angleOffset", "radian", "x", "Math", "cos", "y", "sin", "bulletPrefab", "warn", "bulletNode", "getNode", "error", "bullet", "getComponent", "destroy", "name", "kBulletNameInEditor", "emitterPos", "node", "getWorldPosition", "setWorldPosition", "z", "onCreateBullet", "mover", "speedAngle", "atan2", "speed", "applyAction", "actType", "actValue", "Emitter_Active", "Emitter_InitialDelay", "Emitter_Prewarm", "Emitter_PrewarmDuration", "Emitter_Duration", "Emitter_ElapsedTime", "Emitter_Loop", "Emitter_LoopInterval", "Emitter_PerEmitInterval", "Emitter_PerEmitCount", "Emitter_PerEmitOffsetX", "Emitter_Angle", "Emitter_Count", "isInScreen", "deltaTime", "updateStatusNone", "Prewarm", "updateStatusPrewarm", "Emitting", "updateStatusEmitting", "Loop<PERSON>ndReached", "updateStatusLoopEndReached", "Completed", "updateStatusCompleted"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAyBC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;;AAC7CC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,kB,iBAAAA,kB;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,iBAAX;AAA8BC,QAAAA,QAA9B;AAAwCC,QAAAA;AAAxC,O,GAAwDb,U;OACxD;AAAEc,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyCd,I;;gCAEnCe,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;;;yBAOCC,O,WAHZP,OAAO,CAAC,SAAD,C,UAOHE,QAAQ,CAAC;AAACM,QAAAA,IAAI,EAAEhB,MAAP;AAAeiB,QAAAA,WAAW,EAAE;AAA5B,OAAD,C,UAGRP,QAAQ,CAAC;AAACM,QAAAA,IAAI;AAAA;AAAA,sCAAL;AAAoBC,QAAAA,WAAW,EAAE;AAAjC,OAAD,C,gBATZR,iB,UACAE,W,gCAFD,MAGaI,OAHb,SAG6Bd,SAH7B,CAGuC;AAAA;AAAA;;AAAA;;AAAA;;AAUnC;AAVmC,eAW5BiB,cAX4B,GAWD,IAXC;AAWO;AAXP,eAY5BC,SAZ4B,GAYN,KAZM;AAYO;AAZP,eAa5BC,MAb4B,GAaT,IAbS;AAaO;AAbP,eAe5BC,YAf4B,GAeJ,GAfI;AAeO;AAfP,eAgB5BC,eAhB4B,GAgBD,GAhBC;AAgBO;AAhBP,eAkB5BC,YAlB4B,GAkBJ,GAlBI;AAkBO;AAlBP,eAmB5BC,YAnB4B,GAmBJ,GAnBI;AAmBO;AAnBP,eAoB5BC,SApB4B,GAoBP,GApBO;AAoBO;AApBP,eAqB5BC,YArB4B,GAqBJ,GArBI;AAqBO;AArBP,eAuB5BC,YAvB4B,GAuBJ,CAvBI;AAuBO;AAvBP,eAwB5BC,eAxB4B,GAwBD,GAxBC;AAwBO;AAxBP,eAyB5BC,cAzB4B,GAyBF,GAzBE;AAyBO;AAzBP,eA2B5BC,KA3B4B,GA2BX,CAAC,EA3BU;AA2BO;AA3BP,eA4B5BC,KA5B4B,GA4BX,CA5BW;AA4BO;AA5BP,eA6B5BC,GA7B4B,GA6BX,EA7BW;AA6BO;AA7BP,eA8B5BC,MA9B4B,GA8BV,GA9BU;AA8BO;AA9BP,eAgC5BC,cAhC4B,GAgCD,KAhCC;AAgCM;AAhCN,eAiCzBC,SAjCyB,GAiCJ,KAjCI;AAAA,eAkCzBC,OAlCyB,GAkCCtB,cAAc,CAACuB,IAlChB;AAAA,eAmCzBC,kBAnCyB,GAmCI,CAnCJ;AAAA,eAoCzBC,iBApCyB,GAoCG,CApCH;AAAA,eAqCzBC,WArCyB,GAqCF,KArCE;AAAA,eAsCzBC,aAtCyB,GAsCD,CAtCC;AAAA;;AAwCvB,YAARC,QAAQ,GAAY;AACpB,iBAAO,KAAKP,SAAZ;AACH;;AAES,YAANQ,MAAM,GAAmB;AACzB,iBAAO,KAAKP,OAAZ;AACH;;AAEa,YAAVQ,UAAU,GAAY;AACtB,iBAAO,KAAKJ,WAAZ;AACH;;AAEoB,YAAjBK,iBAAiB,GAAW;AAC5B,iBAAO,KAAKP,kBAAZ;AACH;;AAEmB,YAAhBQ,gBAAgB,GAAW;AAC3B,iBAAO,KAAKP,iBAAZ;AACH;;AAESQ,QAAAA,KAAK,GAAU;AACrB;AAAA;AAAA,4CAAaC,eAAb,CAA6B,IAA7B;AACA,eAAKb,SAAL,GAAiB,IAAjB;AACA,eAAKc,eAAL;AACH;;AAESC,QAAAA,MAAM,CAACC,EAAD,EAAoB;AAChCC,UAAAA,OAAO,CAACC,GAAR,CAAY,uBAAZ,EAAqC,KAAKnB,cAA1C;;AACA,cAAIhC,MAAM,IAAI,KAAKgC,cAAnB,EAAmC;AAC/B,iBAAKoB,IAAL,CAAUH,EAAV;AACA;AAAA;AAAA,8CAAaI,WAAb,CAAyBJ,EAAzB;AACA;AAAA;AAAA,8CAAaK,iBAAb,CAA+BL,EAA/B;AACH;AACJ;;AAEMM,QAAAA,aAAa,GAAG;AACnB,eAAKvB,cAAL,GAAsB,IAAtB;AACH;;AAEMwB,QAAAA,eAAe,GAAG;AACrB,eAAKxB,cAAL,GAAsB,IAAtB;AACA,eAAKe,eAAL;AACH;;AAEMU,QAAAA,mBAAmB,GAAG;AACzB,eAAKzB,cAAL,GAAsB,KAAtB;;AACA,cAAI;AAAA;AAAA,4CAAa0B,YAAb,IAA6B;AAAA;AAAA,4CAAaA,YAAb,CAA0BC,OAA3D,EAAoE;AAChE;AAAA;AAAA,8CAAaC,iBAAb;AACH;AACJ,SAzFkC,CA2FnC;;;AACUb,QAAAA,eAAe,GAAG;AACxB,eAAK/B,cAAL,GAAsB,KAAK6C,WAAL,CAAiB7C,cAAvC;AACA,eAAKC,SAAL,GAAiB,KAAK4C,WAAL,CAAiB5C,SAAlC;AACA,eAAKC,MAAL,GAAc,KAAK2C,WAAL,CAAiB3C,MAA/B;AACA,eAAKC,YAAL,GAAoB,KAAK0C,WAAL,CAAiB1C,YAArC;AACA,eAAKC,eAAL,GAAuB,KAAKyC,WAAL,CAAiBzC,eAAxC;AACA,eAAKC,YAAL,GAAoB,KAAKwC,WAAL,CAAiBxC,YAArC;AACA,eAAKC,YAAL,GAAoB,KAAKuC,WAAL,CAAiBvC,YAArC;AACA,eAAKC,SAAL,GAAiB,KAAKsC,WAAL,CAAiBtC,SAAlC;AACA,eAAKC,YAAL,GAAoB,KAAKqC,WAAL,CAAiBrC,YAArC;AACA,eAAKC,YAAL,GAAoB,KAAKoC,WAAL,CAAiBpC,YAArC;AACA,eAAKC,eAAL,GAAuB,KAAKmC,WAAL,CAAiBnC,eAAxC;AACA,eAAKC,cAAL,GAAsB,KAAKkC,WAAL,CAAiBlC,cAAvC;AACA,eAAKC,KAAL,GAAa,KAAKiC,WAAL,CAAiBjC,KAA9B;AACA,eAAKC,KAAL,GAAa,KAAKgC,WAAL,CAAiBhC,KAA9B;AACA,eAAKC,GAAL,GAAW,KAAK+B,WAAL,CAAiB/B,GAA5B;AACA,eAAKC,MAAL,GAAc,KAAK8B,WAAL,CAAiB9B,MAA/B;AACH;AACD;AACJ;AACA;;;AACI+B,QAAAA,YAAY,CAACrB,MAAD,EAAyB;AACjC,eAAKP,OAAL,GAAeO,MAAf;AACA,eAAKL,kBAAL,GAA0B,CAA1B;AACH;;AAES2B,QAAAA,gBAAgB,GAAG;AACzB;AACA,eAAKxB,aAAL,GAAqB,KAAKH,kBAAL,GAA0B,KAAKd,YAApD;AACH;;AAES0C,QAAAA,aAAa,GAAG;AACtB,eAAK1B,WAAL,GAAmB,IAAnB,CADsB,CAEtB;AACA;AACH;;AAES2B,QAAAA,YAAY,GAAG;AACrB,eAAK3B,WAAL,GAAmB,KAAnB,CADqB,CAErB;;AACA,eAAK4B,sBAAL;AACH;;AAESC,QAAAA,OAAO,GAAY;AACzB;AACA;AACA,iBAAO,IAAP;AACH;;AAESC,QAAAA,IAAI,GAAS;AACnB,cAAI,KAAK1C,eAAL,GAAuB,CAA3B,EAA8B;AAC1B,iBAAK,IAAI2C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKxC,KAAzB,EAAgCwC,CAAC,EAAjC,EAAqC;AACjC,kBAAIC,UAAU,GAAGD,CAAjB;;AACA,mBAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK9C,YAAzB,EAAuC8C,CAAC,EAAxC,EAA4C;AACxC,oBAAIC,iBAAiB,GAAGD,CAAxB;AACA,qBAAKE,YAAL,CAAkB,MAAM;AACpB,uBAAKC,UAAL,CAAgBJ,UAAhB,EAA4BE,iBAA5B,EADoB,CAEpB;AACH,iBAHD,EAGG,KAAK9C,eAAL,GAAuB8C,iBAH1B;AAIH;AACJ;AACJ,WAXD,MAYK;AACD,iBAAK,IAAIH,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKxC,KAAzB,EAAgCwC,CAAC,EAAjC,EAAqC;AACjC,mBAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK9C,YAAzB,EAAuC8C,CAAC,EAAxC,EAA4C;AACxC,qBAAKG,UAAL,CAAgBL,CAAhB,EAAmBE,CAAnB;AACH;AACJ;AACJ;AACJ;;AAESI,QAAAA,OAAO,GAAY;AACzB,cAAI,KAAKR,OAAL,EAAJ,EAAoB;AAChB,iBAAKC,IAAL;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAESM,QAAAA,UAAU,CAACE,KAAD,EAAeC,YAAf,EAAqC;AACrD3B,UAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ;AACA,gBAAM2B,SAAS,GAAG,KAAKC,iBAAL,CAAuBH,KAAvB,CAAlB;AACA,gBAAMI,QAAQ,GAAG,KAAKC,gBAAL,CAAsBL,KAAtB,EAA6BC,YAA7B,CAAjB;AACA,eAAKK,YAAL,CAAkBJ,SAAlB,EAA6BE,QAA7B;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACID,QAAAA,iBAAiB,CAACH,KAAD,EAA0C;AACvD;AACA,gBAAMO,WAAW,GAAG,KAAKtD,KAAL,GAAa,CAAb,GAAkB,KAAKC,GAAL,IAAY,KAAKD,KAAL,GAAa,CAAzB,CAAD,GAAgC+C,KAAhC,GAAwC,KAAK9C,GAAL,GAAW,CAApE,GAAwE,CAA5F;AACA,gBAAMsD,MAAM,GAAG1E,gBAAgB,CAAC,KAAKkB,KAAL,GAAauD,WAAd,CAA/B;AACA,iBAAO;AACHE,YAAAA,CAAC,EAAEC,IAAI,CAACC,GAAL,CAASH,MAAT,CADA;AAEHI,YAAAA,CAAC,EAAEF,IAAI,CAACG,GAAL,CAASL,MAAT;AAFA,WAAP;AAIH;AAED;AACJ;AACA;AACA;AACA;;;AACIH,QAAAA,gBAAgB,CAACL,KAAD,EAAgBC,YAAhB,EAAgE;AAC5E;AACA,gBAAMlD,cAAc,GAAI,KAAKF,YAAL,GAAoB,CAApB,GAAyB,KAAKE,cAAL,IAAuB,KAAKF,YAAL,GAAoB,CAA3C,CAAD,GAAkDoD,YAAlD,GAAiE,KAAKlD,cAAL,GAAsB,CAA/G,GAAmH,CAA3I;;AACA,cAAI,KAAKI,MAAL,IAAe,CAAnB,EAAsB;AAClB,mBAAO;AAAEsD,cAAAA,CAAC,EAAE1D,cAAL;AAAqB6D,cAAAA,CAAC,EAAE;AAAxB,aAAP;AACH;;AAED,gBAAMV,SAAS,GAAG,KAAKC,iBAAL,CAAuBH,KAAvB,CAAlB;AACA,iBAAO;AACHS,YAAAA,CAAC,EAAEP,SAAS,CAACO,CAAV,GAAc,KAAKtD,MAAnB,GAA4BJ,cAD5B;AAEH6D,YAAAA,CAAC,EAAEV,SAAS,CAACU,CAAV,GAAc,KAAKzD;AAFnB,WAAP;AAIH;;AAEDmD,QAAAA,YAAY,CAACJ,SAAD,EAAsCE,QAAtC,EAA0E;AAClF,cAAI,CAAC,KAAKU,YAAV,EAAwB;AACpBxC,YAAAA,OAAO,CAACyC,IAAR,CAAa,uCAAb;AACA,mBAAO,IAAP;AACH;;AAED,gBAAMC,UAAU,GAAG;AAAA;AAAA,wCAAWC,OAAX,CAAmB;AAAA;AAAA,4CAAanC,YAAhC,EAA8C,KAAKgC,YAAnD,CAAnB;;AACA,cAAI,CAACE,UAAL,EAAiB;AACb1C,YAAAA,OAAO,CAAC4C,KAAR,CAAc,iDAAd;AACA,mBAAO,IAAP;AACH,WAViF,CAYlF;;;AACA,gBAAMC,MAAM,GAAGH,UAAU,CAACI,YAAX;AAAA;AAAA,+BAAf;;AACA,cAAI,CAACD,MAAL,EAAa;AACT7C,YAAAA,OAAO,CAAC4C,KAAR,CAAc,uDAAd;AACAF,YAAAA,UAAU,CAACK,OAAX;AACA,mBAAO,IAAP;AACH;;AAED,cAAIjG,MAAJ,EAAY;AACR4F,YAAAA,UAAU,CAACM,IAAX,GAAkBrF,OAAO,CAACsF,mBAA1B;AACH,WAtBiF,CAwBlF;;;AACA,gBAAMC,UAAU,GAAG,KAAKC,IAAL,CAAUC,gBAAV,EAAnB;AACAV,UAAAA,UAAU,CAACW,gBAAX,CACIH,UAAU,CAACf,CAAX,GAAeL,QAAQ,CAACK,CAD5B,EAEIe,UAAU,CAACZ,CAAX,GAAeR,QAAQ,CAACQ,CAF5B,EAGIY,UAAU,CAACI,CAHf;AAMA;AAAA;AAAA,4CAAaC,cAAb,CAA4BV,MAA5B,EAhCkF,CAkClF;;AACAA,UAAAA,MAAM,CAACW,KAAP,CAAaC,UAAb,GAA0BhG,gBAAgB,CAAC2E,IAAI,CAACsB,KAAL,CAAW9B,SAAS,CAACU,CAArB,EAAwBV,SAAS,CAACO,CAAlC,CAAD,CAA1C;AACAU,UAAAA,MAAM,CAACW,KAAP,CAAaG,KAAb,IAAsB,KAAKtF,SAA3B,CApCkF,CAqClF;;AAEA,iBAAOqE,UAAP;AACH;;AAEMkB,QAAAA,WAAW,CAACC,OAAD,EAA8BC,QAA9B,EAAgD;AAC9D,kBAAQD,OAAR;AACI,iBAAK;AAAA;AAAA,0DAAmBE,cAAxB;AACI,mBAAKhF,SAAL,GAAiB+E,QAAQ,KAAK,CAAb,GAAiB,IAAjB,GAAwB,KAAzC;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBE,oBAAxB;AACI,mBAAK/F,YAAL,GAAoB6F,QAApB;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBG,eAAxB;AACI,mBAAKlG,SAAL,GAAiB+F,QAAQ,KAAK,CAAb,GAAiB,IAAjB,GAAwB,KAAzC;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBI,uBAAxB;AACI,mBAAKhG,eAAL,GAAuB4F,QAAvB;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBK,gBAAxB;AACI,mBAAKhG,YAAL,GAAoB2F,QAApB;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBM,mBAAxB;AACI,mBAAKlF,kBAAL,GAA0B4E,QAA1B;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBO,YAAxB;AACI,mBAAKrG,MAAL,GAAc8F,QAAQ,KAAK,CAAb,GAAiB,IAAjB,GAAwB,KAAtC;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBQ,oBAAxB;AACI,mBAAKhG,YAAL,GAAoBwF,QAApB;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBS,uBAAxB;AACI,mBAAK/F,eAAL,GAAuBsF,QAAvB;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBU,oBAAxB;AACI,mBAAKjG,YAAL,GAAoBuF,QAApB;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBW,sBAAxB;AACI,mBAAKhG,cAAL,GAAsBqF,QAAtB;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBY,aAAxB;AACI,mBAAKhG,KAAL,GAAaoF,QAAb;AACA;;AACJ,iBAAK;AAAA;AAAA,0DAAmBa,aAAxB;AACI,mBAAKhG,KAAL,GAAamF,QAAb;AACA;AACJ;;AACA;AAAS;AAzCb;AA2CH;AAED;AACJ;AACA;;;AACcc,QAAAA,UAAU,GAAa;AAC7B;AACA,iBAAO,IAAP;AACH;;AAEM1E,QAAAA,IAAI,CAAC2E,SAAD,EAA0B;AACjC,cAAI,CAAC,KAAK9F,SAAV,EAAqB;AACjB;AACH;;AAED,eAAKG,kBAAL,IAA2B2F,SAA3B;AACA,eAAK1F,iBAAL,IAA0B0F,SAA1B;;AAEA,kBAAQ,KAAK7F,OAAb;AAEI,iBAAKtB,cAAc,CAACuB,IAApB;AACI,mBAAK6F,gBAAL;AACA;;AACJ,iBAAKpH,cAAc,CAACqH,OAApB;AACI,mBAAKC,mBAAL;AACA;;AACJ,iBAAKtH,cAAc,CAACuH,QAApB;AACI,mBAAKC,oBAAL,CAA0BL,SAA1B;AACA;;AACJ,iBAAKnH,cAAc,CAACyH,cAApB;AACI,mBAAKC,0BAAL;AACA;;AACJ,iBAAK1H,cAAc,CAAC2H,SAApB;AACI,mBAAKC,qBAAL;AACA;;AACJ;AACI;AAlBR;AAoBH;;AAESR,QAAAA,gBAAgB,GAAG;AACzB,cAAI,KAAK5F,kBAAL,IAA2B,KAAKjB,YAApC,EAAkD;AAC9C,iBAAK2C,YAAL,CAAkBlD,cAAc,CAACqH,OAAjC;AACH;AACJ;;AAESC,QAAAA,mBAAmB,GAAG;AAC5B,cAAI,CAAC,KAAKjH,SAAV,EACI,KAAK6C,YAAL,CAAkBlD,cAAc,CAACuH,QAAjC,EADJ,KAEK;AACD,gBAAI,KAAK/F,kBAAL,IAA2B,KAAKhB,eAApC,EAAqD;AACjD,mBAAK0C,YAAL,CAAkBlD,cAAc,CAACuH,QAAjC;AACH;AACJ;AACJ;;AAESC,QAAAA,oBAAoB,CAACL,SAAD,EAAoB;AAC9C,cAAI,KAAK3F,kBAAL,GAA0B,KAAKf,YAAnC,EAAiD;AAC7C,iBAAK4C,YAAL;AACA,gBAAI,KAAK/C,MAAT,EACI,KAAK4C,YAAL,CAAkBlD,cAAc,CAACyH,cAAjC,EADJ,KAGI,KAAKvE,YAAL,CAAkBlD,cAAc,CAAC2H,SAAjC;AACJ;AACH,WAR6C,CAU9C;;;AACA,cAAI,CAAC,KAAKjG,WAAV,EAAuB;AACnB,iBAAK0B,aAAL;AACH,WAFD,MAGK,IAAI,KAAK1B,WAAL,IAAoB,KAAKF,kBAAL,IAA2B,KAAKG,aAAxD,EAAuE;AACxE;AACA,iBAAKoC,OAAL;AACA,iBAAKZ,gBAAL;AACH;AACJ;;AAESuE,QAAAA,0BAA0B,GAAG;AACnC,cAAI,KAAKlG,kBAAL,IAA2B,KAAKZ,YAApC,EAAkD;AAC9C,iBAAKsC,YAAL,CAAkBlD,cAAc,CAACuH,QAAjC;AACH;AACJ;;AAESK,QAAAA,qBAAqB,GAAG,CAC9B;AACH;;AA/XkC,O,UAE5BrC,mB,GAA6B,U;;;;;iBAGb,I;;;;;;;iBAGI,I", "sourcesContent": ["import { _decorator, misc, instantiate, Node, Prefab, Component } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { Bullet } from './Bullet';\r\nimport { EmitterData } from '../data/EmitterData';\r\nimport { ObjectPool } from './ObjectPool';\r\nimport { BulletSystem } from './BulletSystem';\r\nimport { eEmitterActionType } from '../data/EventActionData';\r\nconst { ccclass, executeInEditMode, property, playOnFocus } = _decorator;\r\nconst { degreesToRadians, radiansToDegrees } = misc;\r\n\r\nexport enum eEmitterStatus {\r\n    None, Prewarm, Emitting, LoopEndReached, Completed\r\n}\r\n\r\n@ccclass('Emitter')\r\n@executeInEditMode\r\n@playOnFocus\r\nexport class Emitter extends Component {\r\n\r\n    static kBulletNameInEditor:string = \"_bullet_\";\r\n\r\n    @property({type: Prefab, displayName: \"Bullet Prefab\"})\r\n    bulletPrefab: Prefab = null;\r\n\r\n    @property({type: EmitterData, displayName: \"Emitter Data\"})\r\n    emitterData: EmitterData = null;\r\n\r\n    // 以下属性重新定义作为可修改的属性\r\n    public isOnlyInScreen : boolean = true;   // 仅在屏幕内才发射\r\n    public isPreWarm : boolean = false;       // 是否预热\r\n    public isLoop : boolean = true;           // 是否循环\r\n\r\n    public initialDelay : number = 0.0;       // 初始延迟\r\n    public preWarmDuration : number = 0.0;    // 预热持续时长\r\n\r\n    public emitDuration : number = 1.0;       // 发射器持续时间\r\n    public emitInterval : number = 1.0;       // 发射间隔\r\n    public emitPower : number = 1.0;          // 用来修改子弹初始速度的乘数(备用)\r\n    public loopInterval : number = 0.0;       // 循环间隔\r\n\r\n    public perEmitCount : number = 1;         // 单次发射数量\r\n    public perEmitInterval : number = 0.0;    // 单次发射多个子弹时的间隔\r\n    public perEmitOffsetX : number = 0.0;     // 单次发射多个子弹时的x偏移\r\n\r\n    public angle : number = -90;              // 发射角度: -90朝下\r\n    public count : number = 1;                // 发射条数(弹道数量)\r\n    public arc   : number = 60;               // 发射范围(弧度范围)\r\n    public radius : number = 1.0;             // 发射半径\r\n\r\n    public updateInEditor : boolean = false; // 是否在编辑器中更新\r\n    protected _isActive: boolean = false;\r\n    protected _status: eEmitterStatus = eEmitterStatus.None;\r\n    protected _statusElapsedTime: number = 0;\r\n    protected _totalElapsedTime: number = 0;\r\n    protected _isEmitting: boolean = false;\r\n    protected _nextEmitTime: number = 0;\r\n\r\n    get isActive(): boolean {\r\n        return this._isActive;\r\n    }\r\n\r\n    get status(): eEmitterStatus {\r\n        return this._status;\r\n    }\r\n\r\n    get isEmitting(): boolean {\r\n        return this._isEmitting;\r\n    }\r\n\r\n    get statusElapsedTime(): number {\r\n        return this._statusElapsedTime;\r\n    }\r\n\r\n    get totalElapsedTime(): number {\r\n        return this._totalElapsedTime;\r\n    }\r\n\r\n    protected start() : void {\r\n        BulletSystem.onCreateEmitter(this);\r\n        this._isActive = true;\r\n        this.resetProperties();\r\n    }\r\n\r\n    protected update(dt : number): void {\r\n        console.log(\"lost focus in editor \", this.updateInEditor);\r\n        if (EDITOR && this.updateInEditor) {\r\n            this.tick(dt);\r\n            BulletSystem.tickBullets(dt);\r\n            BulletSystem.tickActionRunners(dt);\r\n        }\r\n    }\r\n\r\n    public resetInEditor() {\r\n        this.updateInEditor = true;\r\n    }\r\n\r\n    public onFocusInEditor() {\r\n        this.updateInEditor = true;\r\n        this.resetProperties();\r\n    }\r\n\r\n    public onLostFocusInEditor() {\r\n        this.updateInEditor = false;\r\n        if (BulletSystem.bulletParent && BulletSystem.bulletParent.isValid) {\r\n            BulletSystem.destroyAllBullets()\r\n        }\r\n    }\r\n\r\n    // reset properties from emitterData\r\n    protected resetProperties() {\r\n        this.isOnlyInScreen = this.emitterData.isOnlyInScreen;\r\n        this.isPreWarm = this.emitterData.isPreWarm;\r\n        this.isLoop = this.emitterData.isLoop;\r\n        this.initialDelay = this.emitterData.initialDelay;\r\n        this.preWarmDuration = this.emitterData.preWarmDuration;\r\n        this.emitDuration = this.emitterData.emitDuration;\r\n        this.emitInterval = this.emitterData.emitInterval;\r\n        this.emitPower = this.emitterData.emitPower;\r\n        this.loopInterval = this.emitterData.loopInterval;\r\n        this.perEmitCount = this.emitterData.perEmitCount;\r\n        this.perEmitInterval = this.emitterData.perEmitInterval;\r\n        this.perEmitOffsetX = this.emitterData.perEmitOffsetX;\r\n        this.angle = this.emitterData.angle;\r\n        this.count = this.emitterData.count;\r\n        this.arc = this.emitterData.arc;\r\n        this.radius = this.emitterData.radius;\r\n    }\r\n    /**\r\n     * public apis\r\n     */\r\n    changeStatus(status: eEmitterStatus) {\r\n        this._status = status;\r\n        this._statusElapsedTime = 0;\r\n    }\r\n\r\n    protected scheduleNextEmit() {\r\n        // Schedule the next emit after emitInterval\r\n        this._nextEmitTime = this._statusElapsedTime + this.emitInterval;\r\n    }\r\n\r\n    protected startEmitting() {\r\n        this._isEmitting = true;\r\n        // 下一次update时触发发射\r\n        // 或者在这里调用 this.tryEmit() && this.scheduleNextEmit(); 立即触发发射\r\n    }\r\n    \r\n    protected stopEmitting() {\r\n        this._isEmitting = false;\r\n        // Clear any scheduled per-emit bullets\r\n        this.unscheduleAllCallbacks();\r\n    }\r\n\r\n    protected canEmit(): boolean {\r\n        // 检查是否可以触发发射\r\n        // Override this method in subclasses to add custom trigger conditions\r\n        return true;\r\n    }\r\n\r\n    protected emit(): void {\r\n        if (this.perEmitInterval > 0) {\r\n            for (let i = 0; i < this.count; i++) {\r\n                let localIndex = i;\r\n                for (let j = 0; j < this.perEmitCount; j++) {\r\n                    let localPerEmitIndex = j;\r\n                    this.scheduleOnce(() => {\r\n                        this.emitSingle(localIndex, localPerEmitIndex);\r\n                        // Logic to emit the bullet after the interval\r\n                    }, this.perEmitInterval * localPerEmitIndex);\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            for (let i = 0; i < this.count; i++) {\r\n                for (let j = 0; j < this.perEmitCount; j++) {\r\n                    this.emitSingle(i, j);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    protected tryEmit(): boolean {\r\n        if (this.canEmit()) {\r\n            this.emit();\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    protected emitSingle(index:number, perEmitIndex: number) {\r\n        console.log(\"emit a bullet\");\r\n        const direction = this.getSpawnDirection(index);\r\n        const position = this.getSpawnPosition(index, perEmitIndex);\r\n        this.createBullet(direction, position);\r\n    }\r\n\r\n    /**\r\n     * Calculate the direction for a bullet at the given index\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Direction vector {x, y}\r\n     */\r\n    getSpawnDirection(index: number): { x: number, y: number } {\r\n        // 计算发射方向\r\n        const angleOffset = this.count > 1 ? (this.arc / (this.count - 1)) * index - this.arc / 2 : 0;\r\n        const radian = degreesToRadians(this.angle + angleOffset);\r\n        return {\r\n            x: Math.cos(radian),\r\n            y: Math.sin(radian)\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Get the spawn position for a bullet at the given index\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Position offset from emitter center\r\n     */\r\n    getSpawnPosition(index: number, perEmitIndex: number): { x: number, y: number } {\r\n        // add perEmitOffsetX by perEmitIndex\r\n        const perEmitOffsetX = (this.perEmitCount > 1 ? (this.perEmitOffsetX / (this.perEmitCount - 1)) * perEmitIndex - this.perEmitOffsetX / 2 : 0);\r\n        if (this.radius <= 0) {\r\n            return { x: perEmitOffsetX, y: 0 };\r\n        }\r\n\r\n        const direction = this.getSpawnDirection(index);\r\n        return {\r\n            x: direction.x * this.radius + perEmitOffsetX,\r\n            y: direction.y * this.radius\r\n        };\r\n    }\r\n\r\n    createBullet(direction: { x: number, y: number }, position: { x: number, y: number }) {\r\n        if (!this.bulletPrefab) {\r\n            console.warn(\"EmitterArc: No bullet prefab assigned\");\r\n            return null;\r\n        }\r\n\r\n        const bulletNode = ObjectPool.getNode(BulletSystem.bulletParent, this.bulletPrefab);\r\n        if (!bulletNode) {\r\n            console.error(\"EmitterArc: Failed to instantiate bullet prefab\");\r\n            return null;\r\n        }\r\n\r\n        // Get the bullet component\r\n        const bullet = bulletNode.getComponent(Bullet);\r\n        if (!bullet) {\r\n            console.error(\"Emitter: Bullet prefab does not have Bullet component\");\r\n            bulletNode.destroy();\r\n            return null;\r\n        }\r\n\r\n        if (EDITOR) {\r\n            bulletNode.name = Emitter.kBulletNameInEditor;\r\n        }\r\n\r\n        // Set bullet position relative to emitter\r\n        const emitterPos = this.node.getWorldPosition();\r\n        bulletNode.setWorldPosition(\r\n            emitterPos.x + position.x,\r\n            emitterPos.y + position.y,\r\n            emitterPos.z\r\n        );\r\n\r\n        BulletSystem.onCreateBullet(bullet);\r\n        \r\n        // Post set bullet properties\r\n        bullet.mover.speedAngle = radiansToDegrees(Math.atan2(direction.y, direction.x));\r\n        bullet.mover.speed *= this.emitPower;\r\n        // bulletNode.setRotationFromEuler(0, 0, radiansToDegrees(Math.atan2(direction.y, direction.x)));\r\n\r\n        return bulletNode;\r\n    }\r\n\r\n    public applyAction(actType: eEmitterActionType, actValue: number) {\r\n        switch (actType) {\r\n            case eEmitterActionType.Emitter_Active:\r\n                this._isActive = actValue === 1 ? true : false;\r\n                break;\r\n            case eEmitterActionType.Emitter_InitialDelay:\r\n                this.initialDelay = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_Prewarm:\r\n                this.isPreWarm = actValue === 1 ? true : false;\r\n                break;\r\n            case eEmitterActionType.Emitter_PrewarmDuration:\r\n                this.preWarmDuration = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_Duration:\r\n                this.emitDuration = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_ElapsedTime:\r\n                this._statusElapsedTime = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_Loop:\r\n                this.isLoop = actValue === 1 ? true : false;\r\n                break;\r\n            case eEmitterActionType.Emitter_LoopInterval:\r\n                this.loopInterval = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_PerEmitInterval:\r\n                this.perEmitInterval = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_PerEmitCount:\r\n                this.perEmitCount = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_PerEmitOffsetX:\r\n                this.perEmitOffsetX = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_Angle:\r\n                this.angle = actValue;\r\n                break;\r\n            case eEmitterActionType.Emitter_Count:\r\n                this.count = actValue;\r\n                break;\r\n            // TODO: 补充更多的行为实现\r\n            default: break;\r\n        }\r\n    }\r\n    \r\n    /**\r\n     * Return true if this.node is in screen\r\n     */\r\n    protected isInScreen() : boolean {\r\n        // TODO: Get mainCamera.containsNode(this.node)\r\n        return true;\r\n    }\r\n\r\n    public tick(deltaTime: number): void {\r\n        if (!this._isActive) {\r\n            return;\r\n        }\r\n\r\n        this._statusElapsedTime += deltaTime;\r\n        this._totalElapsedTime += deltaTime;\r\n\r\n        switch (this._status)\r\n        {\r\n            case eEmitterStatus.None:\r\n                this.updateStatusNone();\r\n                break;\r\n            case eEmitterStatus.Prewarm:\r\n                this.updateStatusPrewarm();\r\n                break;\r\n            case eEmitterStatus.Emitting:\r\n                this.updateStatusEmitting(deltaTime);\r\n                break;\r\n            case eEmitterStatus.LoopEndReached:\r\n                this.updateStatusLoopEndReached();\r\n                break;\r\n            case eEmitterStatus.Completed:\r\n                this.updateStatusCompleted();\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n    }\r\n\r\n    protected updateStatusNone() {\r\n        if (this._statusElapsedTime >= this.initialDelay) {\r\n            this.changeStatus(eEmitterStatus.Prewarm);\r\n        }\r\n    }\r\n\r\n    protected updateStatusPrewarm() {\r\n        if (!this.isPreWarm)\r\n            this.changeStatus(eEmitterStatus.Emitting);\r\n        else {\r\n            if (this._statusElapsedTime >= this.preWarmDuration) {\r\n                this.changeStatus(eEmitterStatus.Emitting);\r\n            }\r\n        }\r\n    }\r\n\r\n    protected updateStatusEmitting(deltaTime: number) {\r\n        if (this._statusElapsedTime > this.emitDuration) {\r\n            this.stopEmitting();\r\n            if (this.isLoop) \r\n                this.changeStatus(eEmitterStatus.LoopEndReached);\r\n            else \r\n                this.changeStatus(eEmitterStatus.Completed);\r\n            return;\r\n        }\r\n        \r\n        // Start emitting if not already started\r\n        if (!this._isEmitting) {\r\n            this.startEmitting();\r\n        }\r\n        else if (this._isEmitting && this._statusElapsedTime >= this._nextEmitTime) {\r\n            // Check if it's time for the next emit\r\n            this.tryEmit();\r\n            this.scheduleNextEmit();\r\n        }\r\n    }\r\n\r\n    protected updateStatusLoopEndReached() {\r\n        if (this._statusElapsedTime >= this.loopInterval) {\r\n            this.changeStatus(eEmitterStatus.Emitting);\r\n        }\r\n    }\r\n\r\n    protected updateStatusCompleted() {\r\n        // Do nothing or cleanup if needed\r\n    }\r\n}\r\n"]}