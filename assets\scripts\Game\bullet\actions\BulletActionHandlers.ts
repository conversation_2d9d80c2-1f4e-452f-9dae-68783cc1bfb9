import { _decorator, Color, Vec3 } from 'cc';
import { BaseActionHandler } from './IActionHandler';
import { eBulletActionType } from '../../data/EventActionData';
import { Bullet } from '../Bullet';
const { ccclass } = _decorator;

/**
 * Handler for Bullet Duration
 */
export class BulletDurationHandler extends BaseActionHandler<number> {
    constructor() {
        super(eBulletActionType.Bullet_Duration, 'number', 'Duration');
    }
    
    getValue(bullet: Bullet): number {
        return bullet.duration;
    }
    
    setValue(bullet: Bullet, value: number): void {
        bullet.duration = value;
    }
    
    canInterpolate(): boolean {
        return true;
    }
    
    isValidTarget(target: any): boolean {
        return target instanceof Bullet;
    }
}

/**
 * Handler for Bullet Elapsed Time
 */
export class BulletElapsedTimeHandler extends BaseActionHandler<number> {
    constructor() {
        super(eBulletActionType.Bullet_ElapsedTime, 'number', 'Elapsed Time');
    }
    
    getValue(bullet: Bullet): number {
        return bullet.elapsedTime;
    }
    
    setValue(bullet: Bullet, value: number): void {
        bullet.elapsedTime = value;
    }
    
    canInterpolate(): boolean {
        return true;
    }
    
    isValidTarget(target: any): boolean {
        return target instanceof Bullet;
    }
}

/**
 * Handler for Bullet Position X
 */
export class BulletPosXHandler extends BaseActionHandler<number> {
    constructor() {
        super(eBulletActionType.Bullet_PosX, 'number', 'Position X');
    }
    
    getValue(bullet: Bullet): number {
        return bullet.node.getPosition().x;
    }
    
    setValue(bullet: Bullet, value: number): void {
        const pos = bullet.node.getPosition();
        bullet.node.setPosition(value, pos.y, pos.z);
    }
    
    canInterpolate(): boolean {
        return true;
    }
    
    isValidTarget(target: any): boolean {
        return target instanceof Bullet;
    }
}

/**
 * Handler for Bullet Position Y
 */
export class BulletPosYHandler extends BaseActionHandler<number> {
    constructor() {
        super(eBulletActionType.Bullet_PosY, 'number', 'Position Y');
    }
    
    getValue(bullet: Bullet): number {
        return bullet.node.getPosition().y;
    }
    
    setValue(bullet: Bullet, value: number): void {
        const pos = bullet.node.getPosition();
        bullet.node.setPosition(pos.x, value, pos.z);
    }
    
    canInterpolate(): boolean {
        return true;
    }
    
    isValidTarget(target: any): boolean {
        return target instanceof Bullet;
    }
}

/**
 * Handler for Bullet Damage
 */
export class BulletDamageHandler extends BaseActionHandler<number> {
    constructor() {
        super(eBulletActionType.Bullet_Damage, 'number', 'Damage');
    }
    
    getValue(bullet: Bullet): number {
        return bullet.damage;
    }
    
    setValue(bullet: Bullet, value: number): void {
        bullet.damage = value;
    }
    
    canInterpolate(): boolean {
        return true;
    }
    
    isValidTarget(target: any): boolean {
        return target instanceof Bullet;
    }
}

/**
 * Handler for Bullet Speed
 */
export class BulletSpeedHandler extends BaseActionHandler<number> {
    constructor() {
        super(eBulletActionType.Bullet_Speed, 'number', 'Speed');
    }
    
    getValue(bullet: Bullet): number {
        return bullet.mover.speed;
    }
    
    setValue(bullet: Bullet, value: number): void {
        bullet.mover.speed = value;
    }
    
    canInterpolate(): boolean {
        return true;
    }
    
    isValidTarget(target: any): boolean {
        return target instanceof Bullet;
    }
}

/**
 * Handler for Bullet Speed Angle
 */
export class BulletSpeedAngleHandler extends BaseActionHandler<number> {
    constructor() {
        super(eBulletActionType.Bullet_SpeedAngle, 'number', 'Speed Angle');
    }
    
    getValue(bullet: Bullet): number {
        return bullet.mover.speedAngle;
    }
    
    setValue(bullet: Bullet, value: number): void {
        bullet.mover.speedAngle = value;
    }
    
    canInterpolate(): boolean {
        return true;
    }
    
    isValidTarget(target: any): boolean {
        return target instanceof Bullet;
    }
}

/**
 * Handler for Bullet Acceleration
 */
export class BulletAccelerationHandler extends BaseActionHandler<number> {
    constructor() {
        super(eBulletActionType.Bullet_Acceleration, 'number', 'Acceleration');
    }
    
    getValue(bullet: Bullet): number {
        return bullet.mover.acceleration;
    }
    
    setValue(bullet: Bullet, value: number): void {
        bullet.mover.acceleration = value;
    }
    
    canInterpolate(): boolean {
        return true;
    }
    
    isValidTarget(target: any): boolean {
        return target instanceof Bullet;
    }
}

/**
 * Handler for Bullet Acceleration Angle
 */
export class BulletAccelerationAngleHandler extends BaseActionHandler<number> {
    constructor() {
        super(eBulletActionType.Bullet_AccelerationAngle, 'number', 'Acceleration Angle');
    }
    
    getValue(bullet: Bullet): number {
        return bullet.mover.accelerationAngle;
    }
    
    setValue(bullet: Bullet, value: number): void {
        bullet.mover.accelerationAngle = value;
    }
    
    canInterpolate(): boolean {
        return true;
    }
    
    isValidTarget(target: any): boolean {
        return target instanceof Bullet;
    }
}

/**
 * Handler for Bullet Scale
 */
export class BulletScaleHandler extends BaseActionHandler<number> {
    constructor() {
        super(eBulletActionType.Bullet_Scale, 'number', 'Scale');
    }

    getValue(bullet: Bullet): number {
        return bullet.node.getScale().x;
    }

    setValue(bullet: Bullet, value: number): void {
        bullet.node.setScale(value, value, value);
    }

    canInterpolate(): boolean {
        return true;
    }

    isValidTarget(target: any): boolean {
        return target instanceof Bullet;
    }
}

/**
 * Handler for Bullet Color Red
 */
export class BulletColorRHandler extends BaseActionHandler<number> {
    constructor() {
        super(eBulletActionType.Bullet_ColorR, 'number', 'Color Red');
    }

    getValue(bullet: Bullet): number {
        return bullet.bulletSprite.color.r;
    }

    setValue(bullet: Bullet, value: number): void {
        const color = bullet.bulletSprite.color;
        bullet.bulletSprite.color = new Color(value, color.g, color.b, color.a);
    }

    canInterpolate(): boolean {
        return true;
    }

    isValidTarget(target: any): boolean {
        return target instanceof Bullet;
    }
}

/**
 * Handler for Bullet Color Green
 */
export class BulletColorGHandler extends BaseActionHandler<number> {
    constructor() {
        super(eBulletActionType.Bullet_ColorG, 'number', 'Color Green');
    }

    getValue(bullet: Bullet): number {
        return bullet.bulletSprite.color.g;
    }

    setValue(bullet: Bullet, value: number): void {
        const color = bullet.bulletSprite.color;
        bullet.bulletSprite.color = new Color(color.r, value, color.b, color.a);
    }

    canInterpolate(): boolean {
        return true;
    }

    isValidTarget(target: any): boolean {
        return target instanceof Bullet;
    }
}

/**
 * Handler for Bullet Color Blue
 */
export class BulletColorBHandler extends BaseActionHandler<number> {
    constructor() {
        super(eBulletActionType.Bullet_ColorB, 'number', 'Color Blue');
    }

    getValue(bullet: Bullet): number {
        return bullet.bulletSprite.color.b;
    }

    setValue(bullet: Bullet, value: number): void {
        const color = bullet.bulletSprite.color;
        bullet.bulletSprite.color = new Color(color.r, color.g, value, color.a);
    }

    canInterpolate(): boolean {
        return true;
    }

    isValidTarget(target: any): boolean {
        return target instanceof Bullet;
    }
}

/**
 * Handler for Bullet Color Alpha
 */
export class BulletColorAHandler extends BaseActionHandler<number> {
    constructor() {
        super(eBulletActionType.Bullet_ColorA, 'number', 'Color Alpha');
    }

    getValue(bullet: Bullet): number {
        return bullet.bulletSprite.color.a;
    }

    setValue(bullet: Bullet, value: number): void {
        const color = bullet.bulletSprite.color;
        bullet.bulletSprite.color = new Color(color.r, color.g, color.b, value);
    }

    canInterpolate(): boolean {
        return true;
    }

    isValidTarget(target: any): boolean {
        return target instanceof Bullet;
    }
}

/**
 * Handler for Bullet Face Moving Direction
 */
export class BulletFaceMovingDirHandler extends BaseActionHandler<boolean> {
    constructor() {
        super(eBulletActionType.Bullet_FaceMovingDir, 'boolean', 'Face Moving Direction');
    }

    getValue(bullet: Bullet): boolean {
        return bullet.mover.isFacingMoveDir;
    }

    setValue(bullet: Bullet, value: boolean): void {
        bullet.mover.isFacingMoveDir = value;
    }

    canInterpolate(): boolean {
        return false;
    }

    isValidTarget(target: any): boolean {
        return target instanceof Bullet;
    }
}

/**
 * Handler for Bullet Tracking Target
 */
export class BulletTrackingTargetHandler extends BaseActionHandler<boolean> {
    constructor() {
        super(eBulletActionType.Bullet_TrackingTarget, 'boolean', 'Tracking Target');
    }

    getValue(bullet: Bullet): boolean {
        return bullet.mover.isTrackingTarget;
    }

    setValue(bullet: Bullet, value: boolean): void {
        bullet.mover.isTrackingTarget = value;
    }

    canInterpolate(): boolean {
        return false;
    }

    isValidTarget(target: any): boolean {
        return target instanceof Bullet;
    }
}

/**
 * Handler for Bullet Destructive
 */
export class BulletDestructiveHandler extends BaseActionHandler<boolean> {
    constructor() {
        super(eBulletActionType.Bullet_Destructive, 'boolean', 'Destructive');
    }

    getValue(bullet: Bullet): boolean {
        return bullet.isDestructive;
    }

    setValue(bullet: Bullet, value: boolean): void {
        bullet.isDestructive = value;
    }

    canInterpolate(): boolean {
        return false;
    }

    isValidTarget(target: any): boolean {
        return target instanceof Bullet;
    }
}

/**
 * Handler for Bullet Destructive On Hit
 */
export class BulletDestructiveOnHitHandler extends BaseActionHandler<boolean> {
    constructor() {
        super(eBulletActionType.Bullet_DestructiveOnHit, 'boolean', 'Destructive On Hit');
    }

    getValue(bullet: Bullet): boolean {
        return bullet.isDestructiveOnHit;
    }

    setValue(bullet: Bullet, value: boolean): void {
        bullet.isDestructiveOnHit = value;
    }

    canInterpolate(): boolean {
        return false;
    }

    isValidTarget(target: any): boolean {
        return target instanceof Bullet;
    }
}
