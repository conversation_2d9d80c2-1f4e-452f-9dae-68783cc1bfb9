import { _decorator } from 'cc';
import { ActionValue, IValueInterpolator, InterpolatorFactory } from './ActionValue';
import { IActionHandler } from './IActionHandler';
import { ActionRegistry } from './ActionRegistry';
import { eEasing } from '../../move/IMovable';
const { ccclass } = _decorator;

/**
 * Generic action runner that can handle any action type through the registry system
 */
export class GenericActionRunner {
    private startValue: ActionValue = 0;
    private targetValue: ActionValue = 0;
    private elapsedTime: number = 0;
    private isRunning: boolean = false;
    private _isCompleted: boolean = false;
    private handler: IActionHandler;
    private interpolator: IValueInterpolator<ActionValue>;
    
    get isCompleted(): boolean {
        return this._isCompleted;
    }
    
    get actionType(): number {
        return this.handler?.actionType || -1;
    }
    
    get valueType(): string {
        return this.handler?.valueType || 'unknown';
    }
    
    constructor(
        private owner: any,
        private actionTypeId: number,
        private duration: number,
        private easing: eEasing,
        private targetVal: ActionValue,
        private isRandom: boolean = false,
        private minValue?: ActionValue,
        private maxValue?: ActionValue
    ) {
        this.handler = ActionRegistry.getHandler(actionTypeId);
        if (!this.handler) {
            throw new Error(`No handler found for action type: ${actionTypeId}`);
        }
        
        // Validate target compatibility
        if (this.handler.isValidTarget && !this.handler.isValidTarget(owner)) {
            throw new Error(`Target object is not compatible with action type: ${actionTypeId}`);
        }
        
        this.interpolator = InterpolatorFactory.create(this.handler.valueType);
    }
    
    /**
     * Start the action execution
     */
    start() {
        try {
            this.startValue = this.handler.getValue(this.owner);
            this.targetValue = this.calculateTargetValue();
            this.elapsedTime = 0;
            this.isRunning = true;
            this._isCompleted = false;
            
            console.log(`Started action ${this.handler.getDisplayName?.()}: ${this.startValue} -> ${this.targetValue}`);
        } catch (error) {
            console.error(`Failed to start action ${this.actionTypeId}:`, error);
            this._isCompleted = true;
            this.isRunning = false;
        }
    }
    
    /**
     * Calculate the target value based on configuration
     */
    private calculateTargetValue(): ActionValue {
        if (this.isRandom && this.minValue !== undefined && this.maxValue !== undefined) {
            return this.interpolator.getRandomValue(this.minValue, this.maxValue);
        }
        return this.targetVal;
    }
    
    /**
     * Update the action (called each frame)
     */
    tick(dt: number) {
        if (!this.isRunning || this._isCompleted) {
            return;
        }
        
        this.elapsedTime += dt;
        
        if (this.elapsedTime >= this.duration) {
            // Action completed
            this.isRunning = false;
            this._isCompleted = true;
            this.apply(this.targetValue);
        } else if (this.handler.canInterpolate() && this.interpolator.canInterpolate()) {
            // Interpolate between start and target values
            const progress = Math.min(1.0, this.elapsedTime / this.duration);
            const easedProgress = this.applyEasing(this.easing, progress);
            const currentValue = this.interpolator.interpolate(this.startValue, this.targetValue, easedProgress);
            this.apply(currentValue);
        } else if (this.duration === 0) {
            // Instant action
            this.isRunning = false;
            this._isCompleted = true;
            this.apply(this.targetValue);
        }
    }
    
    /**
     * Apply the current value to the target object
     */
    private apply(value: ActionValue) {
        try {
            this.handler.setValue(this.owner, value);
        } catch (error) {
            console.error(`Failed to apply action ${this.actionTypeId} with value ${value}:`, error);
            this._isCompleted = true;
            this.isRunning = false;
        }
    }
    
    /**
     * Apply easing function to progress
     */
    private applyEasing(easing: eEasing, t: number): number {
        switch (easing) {
            case eEasing.Linear:
                return t;

            case eEasing.InSine:
                return 1 - Math.cos(t * Math.PI / 2);

            case eEasing.OutSine:
                return Math.sin(t * Math.PI / 2);

            case eEasing.InOutSine:
                return -(Math.cos(Math.PI * t) - 1) / 2;

            case eEasing.InQuad:
                return t * t;

            case eEasing.OutQuad:
                return 1 - (1 - t) * (1 - t);

            case eEasing.InOutQuad:
                return t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2;

            default:
                return t;
        }
    }
    
    /**
     * Force complete the action immediately
     */
    forceComplete() {
        if (!this._isCompleted) {
            this.apply(this.targetValue);
            this._isCompleted = true;
            this.isRunning = false;
        }
    }
    
    /**
     * Cancel the action without applying the target value
     */
    cancel() {
        this._isCompleted = true;
        this.isRunning = false;
    }
    
    /**
     * Get debug information about this action runner
     */
    getDebugInfo(): string {
        return `GenericActionRunner {
            actionType: ${this.actionTypeId} (${this.handler?.getDisplayName?.()}),
            valueType: ${this.valueType},
            startValue: ${this.startValue},
            targetValue: ${this.targetValue},
            elapsedTime: ${this.elapsedTime.toFixed(3)}s,
            duration: ${this.duration}s,
            isRunning: ${this.isRunning},
            isCompleted: ${this._isCompleted}
        }`;
    }
}
