{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/actions/EmitterActionHandlers.ts"], "names": ["EmitterActiveHandler", "EmitterInitialDelayHandler", "Emitter<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EmitterPrewarmDurationHandler", "EmitterDurationHandler", "EmitterElapsedTimeHandler", "Emitter<PERSON><PERSON><PERSON><PERSON><PERSON>", "EmitterLoopIntervalHandler", "EmitterPerEmitIntervalHandler", "EmitterPerEmitCountHandler", "EmitterPerEmitOffsetXHandler", "Emitter<PERSON><PERSON><PERSON><PERSON><PERSON>", "EmitterCount<PERSON>andler", "_decorator", "BaseActionHandler", "eEmitterActionType", "Emitter", "eEmitterStatus", "ccclass", "constructor", "Emitter_Active", "getValue", "emitter", "status", "None", "setValue", "value", "isActive", "canInterpolate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "target", "Emitter_InitialDelay", "initialDelay", "Emitter_Prewarm", "isPreWarm", "Emitter_PrewarmDuration", "preWarmDuration", "Emitter_Duration", "emitDuration", "Emitter_ElapsedTime", "totalElapsedTime", "Emitter_Loop", "isLoop", "Emitter_LoopInterval", "loopInterval", "Emitter_PerEmitInterval", "perEmitInterval", "Emitter_PerEmitCount", "perEmitCount", "Emitter_PerEmitOffsetX", "perEmitOffsetX", "Emitter_Angle", "angle", "Emitter_Count", "count"], "mappings": ";;;2JASaA,oB,EAyBAC,0B,EAyBAC,qB,EAyBAC,6B,EAyBAC,sB,EAyBAC,yB,EA0BAC,kB,EAyBAC,0B,EAyBAC,6B,EAyBAC,0B,EAyBAC,4B,EAyBAC,mB,EAyBAC,mB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAtTJC,MAAAA,U,OAAAA,U;;AACAC,MAAAA,iB,iBAAAA,iB;;AACAC,MAAAA,kB,iBAAAA,kB;;AACAC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,c,iBAAAA,c;;;;;;;;;OACZ;AAAEC,QAAAA;AAAF,O,GAAcL,U;AAEpB;AACA;AACA;;sCACab,oB,GAAN,MAAMA,oBAAN;AAAA;AAAA,kDAA8D;AACjEmB,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,wDAAmBC,cAAzB,EAAyC,SAAzC,EAAoD,gBAApD;AACH;;AAEDC,QAAAA,QAAQ,CAACC,OAAD,EAA4B;AAChC,iBAAOA,OAAO,CAACC,MAAR,KAAmB;AAAA;AAAA,gDAAeC,IAAzC;AACH;;AAEDC,QAAAA,QAAQ,CAACH,OAAD,EAAmBI,KAAnB,EAAyC;AAC7CJ,UAAAA,OAAO,CAACK,QAAR,GAAmBD,KAAnB;AACH;;AAEDE,QAAAA,cAAc,GAAY;AACtB,iBAAO,KAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,iCAAb;AACH;;AAnBgE,O;AAsBrE;AACA;AACA;;;4CACa7B,0B,GAAN,MAAMA,0BAAN;AAAA;AAAA,kDAAmE;AACtEkB,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,wDAAmBY,oBAAzB,EAA+C,QAA/C,EAAyD,eAAzD;AACH;;AAEDV,QAAAA,QAAQ,CAACC,OAAD,EAA2B;AAC/B,iBAAOA,OAAO,CAACU,YAAf;AACH;;AAEDP,QAAAA,QAAQ,CAACH,OAAD,EAAmBI,KAAnB,EAAwC;AAC5CJ,UAAAA,OAAO,CAACU,YAAR,GAAuBN,KAAvB;AACH;;AAEDE,QAAAA,cAAc,GAAY;AACtB,iBAAO,IAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,iCAAb;AACH;;AAnBqE,O;AAsB1E;AACA;AACA;;;uCACa5B,qB,GAAN,MAAMA,qBAAN;AAAA;AAAA,kDAA+D;AAClEiB,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,wDAAmBc,eAAzB,EAA0C,SAA1C,EAAqD,SAArD;AACH;;AAEDZ,QAAAA,QAAQ,CAACC,OAAD,EAA4B;AAChC,iBAAOA,OAAO,CAACY,SAAf;AACH;;AAEDT,QAAAA,QAAQ,CAACH,OAAD,EAAmBI,KAAnB,EAAyC;AAC7CJ,UAAAA,OAAO,CAACY,SAAR,GAAoBR,KAApB;AACH;;AAEDE,QAAAA,cAAc,GAAY;AACtB,iBAAO,KAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,iCAAb;AACH;;AAnBiE,O;AAsBtE;AACA;AACA;;;+CACa3B,6B,GAAN,MAAMA,6BAAN;AAAA;AAAA,kDAAsE;AACzEgB,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,wDAAmBgB,uBAAzB,EAAkD,QAAlD,EAA4D,kBAA5D;AACH;;AAEDd,QAAAA,QAAQ,CAACC,OAAD,EAA2B;AAC/B,iBAAOA,OAAO,CAACc,eAAf;AACH;;AAEDX,QAAAA,QAAQ,CAACH,OAAD,EAAmBI,KAAnB,EAAwC;AAC5CJ,UAAAA,OAAO,CAACc,eAAR,GAA0BV,KAA1B;AACH;;AAEDE,QAAAA,cAAc,GAAY;AACtB,iBAAO,IAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,iCAAb;AACH;;AAnBwE,O;AAsB7E;AACA;AACA;;;wCACa1B,sB,GAAN,MAAMA,sBAAN;AAAA;AAAA,kDAA+D;AAClEe,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,wDAAmBkB,gBAAzB,EAA2C,QAA3C,EAAqD,UAArD;AACH;;AAEDhB,QAAAA,QAAQ,CAACC,OAAD,EAA2B;AAC/B,iBAAOA,OAAO,CAACgB,YAAf;AACH;;AAEDb,QAAAA,QAAQ,CAACH,OAAD,EAAmBI,KAAnB,EAAwC;AAC5CJ,UAAAA,OAAO,CAACgB,YAAR,GAAuBZ,KAAvB;AACH;;AAEDE,QAAAA,cAAc,GAAY;AACtB,iBAAO,IAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,iCAAb;AACH;;AAnBiE,O;AAsBtE;AACA;AACA;;;2CACazB,yB,GAAN,MAAMA,yBAAN;AAAA;AAAA,kDAAkE;AACrEc,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,wDAAmBoB,mBAAzB,EAA8C,QAA9C,EAAwD,cAAxD;AACH;;AAEDlB,QAAAA,QAAQ,CAACC,OAAD,EAA2B;AAC/B,iBAAOA,OAAO,CAACkB,gBAAf;AACH;;AAEDf,QAAAA,QAAQ,CAACH,OAAD,EAAmBI,KAAnB,EAAwC;AAC5C;AACAJ,UAAAA,OAAO,CAAC,oBAAD,CAAP,GAAgCI,KAAhC;AACH;;AAEDE,QAAAA,cAAc,GAAY;AACtB,iBAAO,IAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,iCAAb;AACH;;AApBoE,O;AAuBzE;AACA;AACA;;;oCACaxB,kB,GAAN,MAAMA,kBAAN;AAAA;AAAA,kDAA4D;AAC/Da,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,wDAAmBsB,YAAzB,EAAuC,SAAvC,EAAkD,MAAlD;AACH;;AAEDpB,QAAAA,QAAQ,CAACC,OAAD,EAA4B;AAChC,iBAAOA,OAAO,CAACoB,MAAf;AACH;;AAEDjB,QAAAA,QAAQ,CAACH,OAAD,EAAmBI,KAAnB,EAAyC;AAC7CJ,UAAAA,OAAO,CAACoB,MAAR,GAAiBhB,KAAjB;AACH;;AAEDE,QAAAA,cAAc,GAAY;AACtB,iBAAO,KAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,iCAAb;AACH;;AAnB8D,O;AAsBnE;AACA;AACA;;;4CACavB,0B,GAAN,MAAMA,0BAAN;AAAA;AAAA,kDAAmE;AACtEY,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,wDAAmBwB,oBAAzB,EAA+C,QAA/C,EAAyD,eAAzD;AACH;;AAEDtB,QAAAA,QAAQ,CAACC,OAAD,EAA2B;AAC/B,iBAAOA,OAAO,CAACsB,YAAf;AACH;;AAEDnB,QAAAA,QAAQ,CAACH,OAAD,EAAmBI,KAAnB,EAAwC;AAC5CJ,UAAAA,OAAO,CAACsB,YAAR,GAAuBlB,KAAvB;AACH;;AAEDE,QAAAA,cAAc,GAAY;AACtB,iBAAO,IAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,iCAAb;AACH;;AAnBqE,O;AAsB1E;AACA;AACA;;;+CACatB,6B,GAAN,MAAMA,6BAAN;AAAA;AAAA,kDAAsE;AACzEW,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,wDAAmB0B,uBAAzB,EAAkD,QAAlD,EAA4D,mBAA5D;AACH;;AAEDxB,QAAAA,QAAQ,CAACC,OAAD,EAA2B;AAC/B,iBAAOA,OAAO,CAACwB,eAAf;AACH;;AAEDrB,QAAAA,QAAQ,CAACH,OAAD,EAAmBI,KAAnB,EAAwC;AAC5CJ,UAAAA,OAAO,CAACwB,eAAR,GAA0BpB,KAA1B;AACH;;AAEDE,QAAAA,cAAc,GAAY;AACtB,iBAAO,IAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,iCAAb;AACH;;AAnBwE,O;AAsB7E;AACA;AACA;;;4CACarB,0B,GAAN,MAAMA,0BAAN;AAAA;AAAA,kDAAmE;AACtEU,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,wDAAmB4B,oBAAzB,EAA+C,QAA/C,EAAyD,gBAAzD;AACH;;AAED1B,QAAAA,QAAQ,CAACC,OAAD,EAA2B;AAC/B,iBAAOA,OAAO,CAAC0B,YAAf;AACH;;AAEDvB,QAAAA,QAAQ,CAACH,OAAD,EAAmBI,KAAnB,EAAwC;AAC5CJ,UAAAA,OAAO,CAAC0B,YAAR,GAAuBtB,KAAvB;AACH;;AAEDE,QAAAA,cAAc,GAAY;AACtB,iBAAO,IAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,iCAAb;AACH;;AAnBqE,O;AAsB1E;AACA;AACA;;;8CACapB,4B,GAAN,MAAMA,4BAAN;AAAA;AAAA,kDAAqE;AACxES,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,wDAAmB8B,sBAAzB,EAAiD,QAAjD,EAA2D,mBAA3D;AACH;;AAED5B,QAAAA,QAAQ,CAACC,OAAD,EAA2B;AAC/B,iBAAOA,OAAO,CAAC4B,cAAf;AACH;;AAEDzB,QAAAA,QAAQ,CAACH,OAAD,EAAmBI,KAAnB,EAAwC;AAC5CJ,UAAAA,OAAO,CAAC4B,cAAR,GAAyBxB,KAAzB;AACH;;AAEDE,QAAAA,cAAc,GAAY;AACtB,iBAAO,IAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,iCAAb;AACH;;AAnBuE,O;AAsB5E;AACA;AACA;;;qCACanB,mB,GAAN,MAAMA,mBAAN;AAAA;AAAA,kDAA4D;AAC/DQ,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,wDAAmBgC,aAAzB,EAAwC,QAAxC,EAAkD,OAAlD;AACH;;AAED9B,QAAAA,QAAQ,CAACC,OAAD,EAA2B;AAC/B,iBAAOA,OAAO,CAAC8B,KAAf;AACH;;AAED3B,QAAAA,QAAQ,CAACH,OAAD,EAAmBI,KAAnB,EAAwC;AAC5CJ,UAAAA,OAAO,CAAC8B,KAAR,GAAgB1B,KAAhB;AACH;;AAEDE,QAAAA,cAAc,GAAY;AACtB,iBAAO,IAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,iCAAb;AACH;;AAnB8D,O;AAsBnE;AACA;AACA;;;qCACalB,mB,GAAN,MAAMA,mBAAN;AAAA;AAAA,kDAA4D;AAC/DO,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,wDAAmBkC,aAAzB,EAAwC,QAAxC,EAAkD,OAAlD;AACH;;AAEDhC,QAAAA,QAAQ,CAACC,OAAD,EAA2B;AAC/B,iBAAOA,OAAO,CAACgC,KAAf;AACH;;AAED7B,QAAAA,QAAQ,CAACH,OAAD,EAAmBI,KAAnB,EAAwC;AAC5CJ,UAAAA,OAAO,CAACgC,KAAR,GAAgB5B,KAAhB;AACH;;AAEDE,QAAAA,cAAc,GAAY;AACtB,iBAAO,IAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,iCAAb;AACH;;AAnB8D,O", "sourcesContent": ["import { _decorator } from 'cc';\nimport { BaseActionHandler } from './IActionHandler';\nimport { eEmitterActionType } from '../../data/EventActionData';\nimport { Emitter, eEmitterStatus } from '../Emitter';\nconst { ccclass } = _decorator;\n\n/**\n * Handler for Emitter Active state\n */\nexport class EmitterActiveHandler extends BaseActionHandler<boolean> {\n    constructor() {\n        super(eEmitterActionType.Emitter_Active, 'boolean', 'Emitter Active');\n    }\n    \n    getValue(emitter: Emitter): boolean {\n        return emitter.status !== eEmitterStatus.None;\n    }\n    \n    setValue(emitter: Emitter, value: boolean): void {\n        emitter.isActive = value;\n    }\n    \n    canInterpolate(): boolean {\n        return false;\n    }\n    \n    isValidTarget(target: any): boolean {\n        return target instanceof Emitter;\n    }\n}\n\n/**\n * Handler for Emitter Initial Delay\n */\nexport class EmitterInitialDelayHandler extends BaseActionHandler<number> {\n    constructor() {\n        super(eEmitterActionType.Emitter_InitialDelay, 'number', 'Initial Delay');\n    }\n    \n    getValue(emitter: Emitter): number {\n        return emitter.initialDelay;\n    }\n    \n    setValue(emitter: Emitter, value: number): void {\n        emitter.initialDelay = value;\n    }\n    \n    canInterpolate(): boolean {\n        return true;\n    }\n    \n    isValidTarget(target: any): boolean {\n        return target instanceof Emitter;\n    }\n}\n\n/**\n * Handler for Emitter Prewarm state\n */\nexport class EmitterPrewarmHandler extends BaseActionHandler<boolean> {\n    constructor() {\n        super(eEmitterActionType.Emitter_Prewarm, 'boolean', 'Prewarm');\n    }\n    \n    getValue(emitter: Emitter): boolean {\n        return emitter.isPreWarm;\n    }\n    \n    setValue(emitter: Emitter, value: boolean): void {\n        emitter.isPreWarm = value;\n    }\n    \n    canInterpolate(): boolean {\n        return false;\n    }\n    \n    isValidTarget(target: any): boolean {\n        return target instanceof Emitter;\n    }\n}\n\n/**\n * Handler for Emitter Prewarm Duration\n */\nexport class EmitterPrewarmDurationHandler extends BaseActionHandler<number> {\n    constructor() {\n        super(eEmitterActionType.Emitter_PrewarmDuration, 'number', 'Prewarm Duration');\n    }\n    \n    getValue(emitter: Emitter): number {\n        return emitter.preWarmDuration;\n    }\n    \n    setValue(emitter: Emitter, value: number): void {\n        emitter.preWarmDuration = value;\n    }\n    \n    canInterpolate(): boolean {\n        return true;\n    }\n    \n    isValidTarget(target: any): boolean {\n        return target instanceof Emitter;\n    }\n}\n\n/**\n * Handler for Emitter Duration\n */\nexport class EmitterDurationHandler extends BaseActionHandler<number> {\n    constructor() {\n        super(eEmitterActionType.Emitter_Duration, 'number', 'Duration');\n    }\n    \n    getValue(emitter: Emitter): number {\n        return emitter.emitDuration;\n    }\n    \n    setValue(emitter: Emitter, value: number): void {\n        emitter.emitDuration = value;\n    }\n    \n    canInterpolate(): boolean {\n        return true;\n    }\n    \n    isValidTarget(target: any): boolean {\n        return target instanceof Emitter;\n    }\n}\n\n/**\n * Handler for Emitter Elapsed Time\n */\nexport class EmitterElapsedTimeHandler extends BaseActionHandler<number> {\n    constructor() {\n        super(eEmitterActionType.Emitter_ElapsedTime, 'number', 'Elapsed Time');\n    }\n    \n    getValue(emitter: Emitter): number {\n        return emitter.totalElapsedTime;\n    }\n    \n    setValue(emitter: Emitter, value: number): void {\n        // Note: This sets the internal status elapsed time, not total elapsed time\n        emitter['_statusElapsedTime'] = value;\n    }\n    \n    canInterpolate(): boolean {\n        return true;\n    }\n    \n    isValidTarget(target: any): boolean {\n        return target instanceof Emitter;\n    }\n}\n\n/**\n * Handler for Emitter Loop state\n */\nexport class EmitterLoopHandler extends BaseActionHandler<boolean> {\n    constructor() {\n        super(eEmitterActionType.Emitter_Loop, 'boolean', 'Loop');\n    }\n    \n    getValue(emitter: Emitter): boolean {\n        return emitter.isLoop;\n    }\n    \n    setValue(emitter: Emitter, value: boolean): void {\n        emitter.isLoop = value;\n    }\n    \n    canInterpolate(): boolean {\n        return false;\n    }\n    \n    isValidTarget(target: any): boolean {\n        return target instanceof Emitter;\n    }\n}\n\n/**\n * Handler for Emitter Loop Interval\n */\nexport class EmitterLoopIntervalHandler extends BaseActionHandler<number> {\n    constructor() {\n        super(eEmitterActionType.Emitter_LoopInterval, 'number', 'Loop Interval');\n    }\n\n    getValue(emitter: Emitter): number {\n        return emitter.loopInterval;\n    }\n\n    setValue(emitter: Emitter, value: number): void {\n        emitter.loopInterval = value;\n    }\n\n    canInterpolate(): boolean {\n        return true;\n    }\n\n    isValidTarget(target: any): boolean {\n        return target instanceof Emitter;\n    }\n}\n\n/**\n * Handler for Emitter Per Emit Interval\n */\nexport class EmitterPerEmitIntervalHandler extends BaseActionHandler<number> {\n    constructor() {\n        super(eEmitterActionType.Emitter_PerEmitInterval, 'number', 'Per Emit Interval');\n    }\n\n    getValue(emitter: Emitter): number {\n        return emitter.perEmitInterval;\n    }\n\n    setValue(emitter: Emitter, value: number): void {\n        emitter.perEmitInterval = value;\n    }\n\n    canInterpolate(): boolean {\n        return true;\n    }\n\n    isValidTarget(target: any): boolean {\n        return target instanceof Emitter;\n    }\n}\n\n/**\n * Handler for Emitter Per Emit Count\n */\nexport class EmitterPerEmitCountHandler extends BaseActionHandler<number> {\n    constructor() {\n        super(eEmitterActionType.Emitter_PerEmitCount, 'number', 'Per Emit Count');\n    }\n\n    getValue(emitter: Emitter): number {\n        return emitter.perEmitCount;\n    }\n\n    setValue(emitter: Emitter, value: number): void {\n        emitter.perEmitCount = value;\n    }\n\n    canInterpolate(): boolean {\n        return true;\n    }\n\n    isValidTarget(target: any): boolean {\n        return target instanceof Emitter;\n    }\n}\n\n/**\n * Handler for Emitter Per Emit Offset X\n */\nexport class EmitterPerEmitOffsetXHandler extends BaseActionHandler<number> {\n    constructor() {\n        super(eEmitterActionType.Emitter_PerEmitOffsetX, 'number', 'Per Emit Offset X');\n    }\n\n    getValue(emitter: Emitter): number {\n        return emitter.perEmitOffsetX;\n    }\n\n    setValue(emitter: Emitter, value: number): void {\n        emitter.perEmitOffsetX = value;\n    }\n\n    canInterpolate(): boolean {\n        return true;\n    }\n\n    isValidTarget(target: any): boolean {\n        return target instanceof Emitter;\n    }\n}\n\n/**\n * Handler for Emitter Angle\n */\nexport class EmitterAngleHandler extends BaseActionHandler<number> {\n    constructor() {\n        super(eEmitterActionType.Emitter_Angle, 'number', 'Angle');\n    }\n\n    getValue(emitter: Emitter): number {\n        return emitter.angle;\n    }\n\n    setValue(emitter: Emitter, value: number): void {\n        emitter.angle = value;\n    }\n\n    canInterpolate(): boolean {\n        return true;\n    }\n\n    isValidTarget(target: any): boolean {\n        return target instanceof Emitter;\n    }\n}\n\n/**\n * Handler for Emitter Count\n */\nexport class EmitterCountHandler extends BaseActionHandler<number> {\n    constructor() {\n        super(eEmitterActionType.Emitter_Count, 'number', 'Count');\n    }\n\n    getValue(emitter: Emitter): number {\n        return emitter.count;\n    }\n\n    setValue(emitter: Emitter, value: number): void {\n        emitter.count = value;\n    }\n\n    canInterpolate(): boolean {\n        return true;\n    }\n\n    isValidTarget(target: any): boolean {\n        return target instanceof Emitter;\n    }\n}\n"]}