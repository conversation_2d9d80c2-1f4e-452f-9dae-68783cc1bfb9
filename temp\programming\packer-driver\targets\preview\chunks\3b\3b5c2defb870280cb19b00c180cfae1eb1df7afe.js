System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, NumberInterpolator, BooleanInterpolator, StringInterpolator, RPNInterpolator, InterpolatorFactory, _crd, ccclass;

  _export({
    NumberInterpolator: void 0,
    BooleanInterpolator: void 0,
    StringInterpolator: void 0,
    RPNInterpolator: void 0,
    InterpolatorFactory: void 0
  });

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "38a6dJjJDhGmKPTUkVBADj7", "ActionValue", undefined);

      __checkObsolete__(['_decorator']);

      ({
        ccclass
      } = _decorator);
      /**
       * Generic value type that can hold different types
       */

      /**
       * Interface for value interpolation
       */

      /**
       * Number interpolator - supports smooth interpolation between numeric values
       */
      _export("NumberInterpolator", NumberInterpolator = class NumberInterpolator {
        canInterpolate() {
          return true;
        }

        interpolate(start, target, progress) {
          return start + (target - start) * progress;
        }

        getRandomValue(min, max) {
          return Math.random() * (max - min) + min;
        }

      });
      /**
       * Boolean interpolator - no smooth interpolation, switches at threshold
       */


      _export("BooleanInterpolator", BooleanInterpolator = class BooleanInterpolator {
        canInterpolate() {
          return false;
        }

        interpolate(start, target, progress) {
          // Switch at 50% progress, or immediately if no duration
          return progress >= 0.5 ? target : start;
        }

        getRandomValue(min, max) {
          return Math.random() > 0.5;
        }

      });
      /**
       * String interpolator - no interpolation, switches at completion
       */


      _export("StringInterpolator", StringInterpolator = class StringInterpolator {
        canInterpolate() {
          return false;
        }

        interpolate(start, target, progress) {
          return progress >= 1.0 ? target : start;
        }

        getRandomValue(min, max) {
          // For strings, randomly pick between min and max
          return Math.random() > 0.5 ? max : min;
        }

      });
      /**
       * RPN (Reverse Polish Notation) interpolator - for future expression support
       */


      _export("RPNInterpolator", RPNInterpolator = class RPNInterpolator {
        canInterpolate() {
          return false; // RPN expressions are evaluated, not interpolated
        }

        interpolate(start, target, progress) {
          // For RPN, we would evaluate the expression at the current progress
          // This is a placeholder for future implementation
          return progress >= 1.0 ? target : start;
        }

        getRandomValue(min, max) {
          // For RPN expressions, this would need special handling
          return Math.random() > 0.5 ? max : min;
        }

      });
      /**
       * Factory for creating appropriate interpolators based on value type
       */


      _export("InterpolatorFactory", InterpolatorFactory = class InterpolatorFactory {
        static create(valueType) {
          switch (valueType) {
            case 'number':
              return new NumberInterpolator();

            case 'boolean':
              return new BooleanInterpolator();

            case 'string':
              return new StringInterpolator();

            case 'rpn':
              return new RPNInterpolator();

            default:
              console.warn("Unknown value type: " + valueType + ", defaulting to number interpolator");
              return new NumberInterpolator();
          }
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=3b5c2defb870280cb19b00c180cfae1eb1df7afe.js.map