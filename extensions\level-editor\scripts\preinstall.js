const readFileSync=require("fs")["readFileSync"],join=require("path")["join"],spawnSync=require("child_process")["spawnSync"],PATH={packageJSON:join(__dirname,"../package.json")};function checkCreatorTypesVersion(e){var o="win32"===process.platform?"npm.cmd":"npm";let n=spawnSync(o,["view","@cocos/creator-types","versions"], {shell:true}).stdout.toString();try{n=JSON.parse(listString)}catch(e){}return!!n.includes(e)}try{const e=readFileSync(PATH.packageJSON,"utf8"),f=JSON.parse(e),g=f.devDependencies["@cocos/creator-types"].replace(/^[^\d]+/,"");checkCreatorTypesVersion(g)||(console.log("[33mWarning:[0m"),console.log("  @en"),console.log("    Version check of @cocos/creator-types failed."),console.log(`    The definition of ${g} has not been released yet. Please export the definition to the ./node_modules directory by selecting "Developer -> Export Interface Definition" in the menu of the Creator editor.`),console.log("    The definition of the corresponding version will be released on npm after the editor is officially released."),console.log("  @zh"),console.log("    @cocos/creator-types 版本检查失败。"),console.log(`    ${g} 定义还未发布，请先通过 Creator 编辑器菜单 "开发者 -> 导出接口定义"，导出定义到 ./node_modules 目录。`),console.log("    对应版本的定义会在编辑器正式发布后同步发布到 npm 上。"))}catch(e){console.error(e)}