import { CompilerOptions } from '@vue/compiler-dom';
import { RenderFunction } from '@vue/runtime-dom';
export * from '@vue/runtime-dom';

export declare function compileToFunction(template: string | HTMLElement, options?: CompilerOptions): RenderFunction;

export { compileToFunction as compile };
// this is appended to the end of ../dist/vue.d.ts during build.
// imports the global JSX namespace registration for compat.
// TODO: remove in 3.4
import '../jsx'
