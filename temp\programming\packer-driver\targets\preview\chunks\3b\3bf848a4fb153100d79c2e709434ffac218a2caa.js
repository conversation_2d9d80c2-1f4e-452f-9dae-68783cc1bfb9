System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Label, EventMgr, ButtonPlus, BaseUI, UILayer, UIMgr, PlaneUIEvent, mockPlaneParts, OpenEquipInfoUISource, _dec, _dec2, _dec3, _dec4, _dec5, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _crd, ccclass, property, PlaneEquipInfoUI;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "../../../event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "../../common/components/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "../../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "../../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "../../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIOpt(extras) {
    _reporterNs.report("UIOpt", "../../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneUIEvent(extras) {
    _reporterNs.report("PlaneUIEvent", "./PlaneEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfmockPlaneEquipInfo(extras) {
    _reporterNs.report("mockPlaneEquipInfo", "./PlaneTypes", _context.meta, extras);
  }

  function _reportPossibleCrUseOfmockPlaneParts(extras) {
    _reporterNs.report("mockPlaneParts", "./PlaneTypes", _context.meta, extras);
  }

  function _reportPossibleCrUseOfOpenEquipInfoUISource(extras) {
    _reporterNs.report("OpenEquipInfoUISource", "./PlaneTypes", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Label = _cc.Label;
    }, function (_unresolved_2) {
      EventMgr = _unresolved_2.EventMgr;
    }, function (_unresolved_3) {
      ButtonPlus = _unresolved_3.ButtonPlus;
    }, function (_unresolved_4) {
      BaseUI = _unresolved_4.BaseUI;
      UILayer = _unresolved_4.UILayer;
      UIMgr = _unresolved_4.UIMgr;
    }, function (_unresolved_5) {
      PlaneUIEvent = _unresolved_5.PlaneUIEvent;
    }, function (_unresolved_6) {
      mockPlaneParts = _unresolved_6.mockPlaneParts;
      OpenEquipInfoUISource = _unresolved_6.OpenEquipInfoUISource;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "23572zRk9xH5ZLM8/NuQXO1", "PlaneEquipInfoUI", undefined);

      __checkObsolete__(['_decorator', 'Label']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("PlaneEquipInfoUI", PlaneEquipInfoUI = (_dec = ccclass('PlaneEquipInfoUI'), _dec2 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec3 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec4 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec5 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec(_class = (_class2 = class PlaneEquipInfoUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor() {
          super(...arguments);
          this._planeEquipInfo = null;

          _initializerDefineProperty(this, "replaceEquipBtn", _descriptor, this);

          _initializerDefineProperty(this, "unEquipBtn", _descriptor2, this);

          _initializerDefineProperty(this, "levelUpEquipBtn", _descriptor3, this);

          _initializerDefineProperty(this, "multiLevelUpEquipBtn", _descriptor4, this);
        }

        static getUrl() {
          return "ui/main/plane/PlaneEquipInfoUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).PopUp;
        }

        static getUIOption() {
          return {
            isClickBgCloseUI: true
          };
        }

        onLoad() {
          this.levelUpEquipBtn.addClick(this.onClickLevelUpEquip, this);
          this.multiLevelUpEquipBtn.addClick(this.onClickMultiLevelUpEquip, this);
          this.unEquipBtn.addClick(this.onClickUnEquip, this);
          this.replaceEquipBtn.addClick(this.onClickReplaceEquip, this);
        }

        onShow(planeEquipInfo, source) {
          var _this = this;

          return _asyncToGenerator(function* () {
            if (source == (_crd && OpenEquipInfoUISource === void 0 ? (_reportPossibleCrUseOfOpenEquipInfoUISource({
              error: Error()
            }), OpenEquipInfoUISource) : OpenEquipInfoUISource).DisPlay) {
              _this.replaceEquipBtn.node.active = false;
              _this.unEquipBtn.node.parent.active = true;
            } else {
              _this.replaceEquipBtn.node.active = true;
              _this.unEquipBtn.node.parent.active = false;
            }

            _this.getComponentInChildren(Label).string = planeEquipInfo.name;
            _this._planeEquipInfo = planeEquipInfo;
          })();
        }

        onClickReplaceEquip() {
          //!todo 替换请求
          var equip = (_crd && mockPlaneParts === void 0 ? (_reportPossibleCrUseOfmockPlaneParts({
            error: Error()
          }), mockPlaneParts) : mockPlaneParts).find(v => v.isEquip && v.part == this._planeEquipInfo.part);

          if (equip) {
            equip.isEquip = false;
          }

          this._planeEquipInfo.isEquip = true;
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && PlaneUIEvent === void 0 ? (_reportPossibleCrUseOfPlaneUIEvent({
            error: Error()
          }), PlaneUIEvent) : PlaneUIEvent).EquipDisPlayRefresh, this._planeEquipInfo);
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).closeUI(PlaneEquipInfoUI);
        }

        onClickUnEquip() {
          //!todo 卸下请求
          (_crd && mockPlaneParts === void 0 ? (_reportPossibleCrUseOfmockPlaneParts({
            error: Error()
          }), mockPlaneParts) : mockPlaneParts).find(v => v.id == this._planeEquipInfo.id).isEquip = false;
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && PlaneUIEvent === void 0 ? (_reportPossibleCrUseOfPlaneUIEvent({
            error: Error()
          }), PlaneUIEvent) : PlaneUIEvent).EquipDisPlayRefresh, this._planeEquipInfo);
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).closeUI(PlaneEquipInfoUI);
        }

        onClickLevelUpEquip() {//EventMgr.emit(PlaneUIEvent.LevelUpEquip)
        }

        onClickMultiLevelUpEquip() {//EventMgr.emit(PlaneUIEvent.MultiLevelUpEquip)
        }

        onHide() {
          return _asyncToGenerator(function* () {})();
        }

        onClose() {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
              error: Error()
            }), EventMgr) : EventMgr).targetOff(_this2);
          })();
        }

        update(dt) {}

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "replaceEquipBtn", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "unEquipBtn", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "levelUpEquipBtn", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "multiLevelUpEquipBtn", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=3bf848a4fb153100d79c2e709434ffac218a2caa.js.map