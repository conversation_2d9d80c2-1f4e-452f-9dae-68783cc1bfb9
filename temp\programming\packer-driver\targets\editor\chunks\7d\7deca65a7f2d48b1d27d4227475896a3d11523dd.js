System.register(["__unresolved_0", "cc"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, BaseActionHandler, _crd, ccclass;

  function _reportPossibleCrUseOfActionValue(extras) {
    _reporterNs.report("ActionValue", "./ActionValue", _context.meta, extras);
  }

  _export("BaseActionHandler", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "64f11kPzpJPi4LapaZpf10m", "IActionHandler", undefined);

      __checkObsolete__(['_decorator']);

      ({
        ccclass
      } = _decorator);
      /**
       * Interface for handling specific action types
       */

      /**
       * Base class for action handlers providing common functionality
       */
      _export("BaseActionHandler", BaseActionHandler = class BaseActionHandler {
        constructor(actionType, valueType, displayName) {
          this.actionType = actionType;
          this.valueType = valueType;
          this.displayName = displayName;
        }

        getDisplayName() {
          return this.displayName || `Action_${this.actionType}`;
        }

        isValidTarget(target) {
          // Default implementation - can be overridden by specific handlers
          return target != null;
        }
        /**
         * Helper method to safely get a property from target
         */


        safeGetProperty(target, propertyPath) {
          try {
            const parts = propertyPath.split('.');
            let current = target;

            for (const part of parts) {
              if (current == null) return undefined;
              current = current[part];
            }

            return current;
          } catch (error) {
            console.warn(`Failed to get property ${propertyPath} from target:`, error);
            return undefined;
          }
        }
        /**
         * Helper method to safely set a property on target
         */


        safeSetProperty(target, propertyPath, value) {
          try {
            const parts = propertyPath.split('.');
            let current = target; // Navigate to the parent object

            for (let i = 0; i < parts.length - 1; i++) {
              if (current == null) return false;
              current = current[parts[i]];
            }

            if (current == null) return false; // Set the final property

            const finalProperty = parts[parts.length - 1];
            current[finalProperty] = value;
            return true;
          } catch (error) {
            console.warn(`Failed to set property ${propertyPath} on target:`, error);
            return false;
          }
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=7deca65a7f2d48b1d27d4227475896a3d11523dd.js.map