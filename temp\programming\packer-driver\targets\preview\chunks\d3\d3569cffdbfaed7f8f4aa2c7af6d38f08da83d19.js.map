{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/BulletSystem.ts"], "names": ["BulletSystem", "_decorator", "find", "EventActionRunner", "ActionRegistry", "GenericActionRunner", "eEasing", "ccclass", "initialize", "console", "log", "enableNewActionSystem", "getHandlerCount", "useNewActionSystem", "disableNewActionSystem", "tick", "dt", "tickEmitters", "tickBullets", "tickActionRunners", "tickGenericActionRunners", "emitter", "allEmitters", "bullet", "allBullets", "i", "allActionRunners", "length", "runner", "isCompleted", "splice", "allGenericActionRunners", "onCreateEmitter", "push", "bulletParent", "<PERSON><PERSON><PERSON><PERSON>", "bulletParentPath", "warn", "node", "onDestroyEmitter", "filter", "e", "onCreateBullet", "onCreate", "setParent", "onDestroyBullet", "b", "destroyAllBullets", "destroySelf", "getConditionValue", "conditionType", "allConditions", "get", "setConditionValue", "value", "oldValue", "set", "emit", "on", "listener", "conditionListeners", "has", "off", "listeners", "l", "args", "createActionRunner", "owner", "data", "start", "createGenericActionRunner", "actionType", "targetValue", "duration", "easing", "isRandom", "minValue", "maxValue", "Linear", "error", "createActionRunnerAuto", "boolValue", "getActionRunnersDebugInfo", "legacyCount", "newCount", "systemInUse", "Map"], "mappings": ";;;kKAaaA,Y;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAbJC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;;AAKZC,MAAAA,iB,iBAAAA,iB;;AACAC,MAAAA,c,iBAAAA,c;AAAgBC,MAAAA,mB,iBAAAA,mB;AAAkCC,MAAAA,O,iBAAAA,O;;;;;;;;;OACrD;AAAEC,QAAAA;AAAF,O,GAAcN,U;AAEpB;AACA;AACA;AACA;;8BACaD,Y,GAAN,MAAMA,YAAN,CAAmB;AAiCtB;AACJ;AACA;AAC4B,eAAVQ,UAAU,GAAG;AACvB;AACA;AAAA;AAAA,gDAAeA,UAAf;AACAC,UAAAA,OAAO,CAACC,GAAR,CAAY,yDAAZ;AACH;AAED;AACJ;AACA;;;AACuC,eAArBC,qBAAqB,GAAG;AAClC,cAAI,CAAC;AAAA;AAAA,gDAAeC,eAAf,EAAL,EAAuC;AACnC;AAAA;AAAA,kDAAeJ,UAAf;AACH;;AACD,eAAKK,kBAAL,GAA0B,IAA1B;AACAJ,UAAAA,OAAO,CAACC,GAAR,CAAY,2BAAZ;AACH;AAED;AACJ;AACA;;;AACwC,eAAtBI,sBAAsB,GAAG;AACnC,eAAKD,kBAAL,GAA0B,KAA1B;AACAJ,UAAAA,OAAO,CAACC,GAAR,CAAY,kCAAZ;AACH;AAED;AACJ;AACA;;;AACsB,eAAJK,IAAI,CAACC,EAAD,EAAa;AAC3B,eAAKC,YAAL,CAAkBD,EAAlB;AACA,eAAKE,WAAL,CAAiBF,EAAjB;AACA,eAAKG,iBAAL,CAAuBH,EAAvB;AACA,eAAKI,wBAAL,CAA8BJ,EAA9B;AACH;;AAEyB,eAAZC,YAAY,CAACD,EAAD,EAAY;AAClC,eAAK,IAAMK,OAAX,IAAsB,KAAKC,WAA3B,EAAwC;AACpCD,YAAAA,OAAO,CAACN,IAAR,CAAaC,EAAb;AACH;AACJ;;AAEwB,eAAXE,WAAW,CAACF,EAAD,EAAY;AACjC,eAAK,IAAMO,MAAX,IAAqB,KAAKC,UAA1B,EAAsC;AAClCD,YAAAA,MAAM,CAACR,IAAP,CAAYC,EAAZ;AACH;AACJ;;AAE8B,eAAjBG,iBAAiB,CAACH,EAAD,EAAa;AACxC,eAAK,IAAIS,CAAC,GAAG,KAAKC,gBAAL,CAAsBC,MAAtB,GAA+B,CAA5C,EAA+CF,CAAC,IAAI,CAApD,EAAuDA,CAAC,EAAxD,EAA4D;AACxD,gBAAMG,MAAM,GAAG,KAAKF,gBAAL,CAAsBD,CAAtB,CAAf;AACAG,YAAAA,MAAM,CAACb,IAAP,CAAYC,EAAZ;;AACA,gBAAIY,MAAM,CAACC,WAAX,EAAwB;AACpB,mBAAKH,gBAAL,CAAsBI,MAAtB,CAA6BL,CAA7B,EAAgC,CAAhC;AACH;AACJ;AACJ;;AAEqC,eAAxBL,wBAAwB,CAACJ,EAAD,EAAa;AAC/C,eAAK,IAAIS,CAAC,GAAG,KAAKM,uBAAL,CAA6BJ,MAA7B,GAAsC,CAAnD,EAAsDF,CAAC,IAAI,CAA3D,EAA8DA,CAAC,EAA/D,EAAmE;AAC/D,gBAAMG,MAAM,GAAG,KAAKG,uBAAL,CAA6BN,CAA7B,CAAf;AACAG,YAAAA,MAAM,CAACb,IAAP,CAAYC,EAAZ;;AACA,gBAAIY,MAAM,CAACC,WAAX,EAAwB;AACpB,mBAAKE,uBAAL,CAA6BD,MAA7B,CAAoCL,CAApC,EAAuC,CAAvC;AACH;AACJ;AACJ;;AAE4B,eAAfO,eAAe,CAACX,OAAD,EAAkB;AAC3C,eAAK,IAAII,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKH,WAAL,CAAiBK,MAArC,EAA6CF,CAAC,EAA9C,EAAkD;AAC9C,gBAAI,KAAKH,WAAL,CAAiBG,CAAjB,MAAwBJ,OAA5B,EAAqC;AACjC;AACH;AACJ;;AAED,eAAKC,WAAL,CAAiBW,IAAjB,CAAsBZ,OAAtB;;AAEA,cAAI,CAAC,KAAKa,YAAN,IAAsB,CAAC,KAAKA,YAAL,CAAkBC,OAA7C,EAAsD;AAClD,gBAAI,KAAKC,gBAAL,CAAsBT,MAAtB,GAA+B,CAAnC,EAAsC;AAClC,mBAAKO,YAAL,GAAoBhC,IAAI,CAAC,KAAKkC,gBAAN,CAAxB;;AACA,kBAAI,CAAC,KAAKF,YAAV,EAAwB;AACpBzB,gBAAAA,OAAO,CAAC4B,IAAR,CAAa,oBAAoB,KAAKD,gBAAtC;AACA,qBAAKF,YAAL,GAAoBb,OAAO,CAACiB,IAA5B;AACH;AACJ;AACJ;AACJ;;AAE6B,eAAhBC,gBAAgB,CAAClB,OAAD,EAAkB;AAC5C,eAAKC,WAAL,GAAmB,KAAKA,WAAL,CAAiBkB,MAAjB,CAAwBC,CAAC,IAAIA,CAAC,KAAKpB,OAAnC,CAAnB;AACH;;AAE2B,eAAdqB,cAAc,CAACnB,MAAD,EAAiB;AACzC,eAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKD,UAAL,CAAgBG,MAApC,EAA4CF,CAAC,EAA7C,EAAiD;AAC7C,gBAAI,KAAKD,UAAL,CAAgBC,CAAhB,MAAuBF,MAA3B,EAAmC;AAC/B;AACH;AACJ;;AAEDA,UAAAA,MAAM,CAACoB,QAAP;AACA,eAAKnB,UAAL,CAAgBS,IAAhB,CAAqBV,MAArB;AACAA,UAAAA,MAAM,CAACe,IAAP,CAAYM,SAAZ,CAAsB,KAAKV,YAA3B,EAAyC,IAAzC;AACH;;AAE4B,eAAfW,eAAe,CAACtB,MAAD,EAAiB;AAC1C,eAAKC,UAAL,GAAkB,KAAKA,UAAL,CAAgBgB,MAAhB,CAAuBM,CAAC,IAAIA,CAAC,KAAKvB,MAAlC,CAAlB;AACH;;AAE8B,eAAjBwB,iBAAiB,GAAG;AAC9B,eAAK,IAAMxB,MAAX,IAAqB,KAAKC,UAA1B,EAAsC;AAClCD,YAAAA,MAAM,CAACyB,WAAP;AACH;;AACD,eAAKxB,UAAL,GAAkB,EAAlB;AACH;AAED;AACJ;AACA;AAEI;;;AAK+B,eAAjByB,iBAAiB,CAACC,aAAD,EAA6C;AACxE,iBAAO,KAAKC,aAAL,CAAmBC,GAAnB,CAAuBF,aAAvB,KAAyC,CAAhD;AACH;AAED;AACJ;AACA;;;AACmC,eAAjBG,iBAAiB,CAACH,aAAD,EAAqCI,KAArC,EAAoD;AAC/E;AACA,cAAMC,QAAQ,GAAG,KAAKJ,aAAL,CAAmBC,GAAnB,CAAuBF,aAAvB,CAAjB;;AACA,cAAIK,QAAQ,KAAKD,KAAjB,EAAwB;AACpB,iBAAKH,aAAL,CAAmBK,GAAnB,CAAuBN,aAAvB,EAAsCI,KAAtC;AACA,iBAAKG,IAAL,CAAUP,aAAV,EAAyBI,KAAzB;AACH;AACJ;;AAEe,eAAFI,EAAE,CAACR,aAAD,EAAqCS,QAArC,EAAyD;AACrE,cAAI,CAAC,KAAKC,kBAAL,CAAwBC,GAAxB,CAA4BX,aAA5B,CAAL,EAAiD;AAC7C,iBAAKU,kBAAL,CAAwBJ,GAAxB,CAA4BN,aAA5B,EAA2C,EAA3C;AACH;;AACD,eAAKU,kBAAL,CAAwBR,GAAxB,CAA4BF,aAA5B,EAA2CjB,IAA3C,CAAgD0B,QAAhD;AACH;;AAEgB,eAAHG,GAAG,CAACZ,aAAD,EAAqCS,QAArC,EAAyD;AACtE,cAAI,KAAKC,kBAAL,CAAwBC,GAAxB,CAA4BX,aAA5B,CAAJ,EAAgD;AAC5C,gBAAMa,SAAS,GAAG,KAAKH,kBAAL,CAAwBR,GAAxB,CAA4BF,aAA5B,CAAlB;AACA,iBAAKU,kBAAL,CAAwBJ,GAAxB,CAA4BN,aAA5B,EAA2Ca,SAAS,CAACvB,MAAV,CAAiBwB,CAAC,IAAIA,CAAC,KAAKL,QAA5B,CAA3C;AACH;AACJ;;AAEiB,eAAJF,IAAI,CAACP,aAAD,EAAqD;AACnE,cAAI,KAAKU,kBAAL,CAAwBC,GAAxB,CAA4BX,aAA5B,CAAJ,EAAgD;AAAA,8CADMe,IACN;AADMA,cAAAA,IACN;AAAA;;AAC5C,iBAAK,IAAMN,QAAX,IAAuB,KAAKC,kBAAL,CAAwBR,GAAxB,CAA4BF,aAA5B,CAAvB,EAAmE;AAC/DS,cAAAA,QAAQ,CAAC,GAAGM,IAAJ,CAAR;AACH;AACJ;AACJ;;AAE+B,eAAlBC,kBAAkB,CAACC,KAAD,EAA0BC,IAA1B,EAAoE;AAChG,cAAMxC,MAAM,GAAG;AAAA;AAAA,sDAAsBuC,KAAtB,EAA6BC,IAA7B,CAAf;AACAxC,UAAAA,MAAM,CAACyC,KAAP;AACA,eAAK3C,gBAAL,CAAsBO,IAAtB,CAA2BL,MAA3B;AACA,iBAAOA,MAAP;AACH;AAED;AACJ;AACA;;;AAC2C,eAAzB0C,yBAAyB,CACnCH,KADmC,EAEnCI,UAFmC,EAGnCC,WAHmC,EAInCC,QAJmC,EAKnCC,MALmC,EAMnCC,QANmC,EAOnCC,QAPmC,EAQnCC,QARmC,EAST;AAAA,cAL1BJ,QAK0B;AAL1BA,YAAAA,QAK0B,GALP,CAKO;AAAA;;AAAA,cAJ1BC,MAI0B;AAJ1BA,YAAAA,MAI0B,GAJR;AAAA;AAAA,oCAAQI,MAIA;AAAA;;AAAA,cAH1BH,QAG0B;AAH1BA,YAAAA,QAG0B,GAHN,KAGM;AAAA;;AAC1B,cAAI;AACA,gBAAM/C,MAAM,GAAG;AAAA;AAAA,4DACXuC,KADW,EAEXI,UAFW,EAGXE,QAHW,EAIXC,MAJW,EAKXF,WALW,EAMXG,QANW,EAOXC,QAPW,EAQXC,QARW,CAAf;AAUAjD,YAAAA,MAAM,CAACyC,KAAP;AACA,iBAAKtC,uBAAL,CAA6BE,IAA7B,CAAkCL,MAAlC;AACA,mBAAOA,MAAP;AACH,WAdD,CAcE,OAAOmD,KAAP,EAAc;AACZtE,YAAAA,OAAO,CAACsE,KAAR,CAAc,yCAAd,EAAyDA,KAAzD;AACA,mBAAO,IAAP;AACH;AACJ;AAED;AACJ;AACA;;;AACwC,eAAtBC,sBAAsB,CAACb,KAAD,EAA0BC,IAA1B,EAAiG;AACjI,cAAI,KAAKvD,kBAAT,EAA6B;AACzB;AACA,gBAAM2D,WAAW,GAAGJ,IAAI,CAACO,QAAL,GAChB,CAACP,IAAI,CAACQ,QAAL,GAAgBR,IAAI,CAACS,QAAtB,IAAkC,CADlB,GACsB;AACrCT,YAAAA,IAAI,CAACa,SAAL,GAAiB,CAAjB,GAAqBb,IAAI,CAACQ,QAF/B,CAFyB,CAIiB;;AAE1C,mBAAO,KAAKN,yBAAL,CACHH,KADG,EAEHC,IAAI,CAACG,UAFF,EAGHC,WAHG,EAIHJ,IAAI,CAACK,QAJF,EAKHL,IAAI,CAACM,MALF,EAMHN,IAAI,CAACO,QANF,EAOHP,IAAI,CAACQ,QAPF,EAQHR,IAAI,CAACS,QARF,CAAP;AAUH,WAhBD,MAgBO;AACH;AACA,mBAAO,KAAKX,kBAAL,CAAwBC,KAAxB,EAA+BC,IAA/B,CAAP;AACH;AACJ;AAED;AACJ;AACA;;;AAC2C,eAAzBc,yBAAyB,GAAW;AAC9C,cAAMC,WAAW,GAAG,KAAKzD,gBAAL,CAAsBC,MAA1C;AACA,cAAMyD,QAAQ,GAAG,KAAKrD,uBAAL,CAA6BJ,MAA9C;AACA,cAAM0D,WAAW,GAAG,KAAKxE,kBAAL,GAA0B,KAA1B,GAAkC,QAAtD;AAEA,iEACSwE,WADT,0BAEUF,WAFV,2BAGWC,QAHX,yBAIQD,WAAW,GAAGC,QAJtB;AAKH;;AApRqB,O;;AAEtB;AACJ;AACA;AAJapF,MAAAA,Y,CAKKwB,U,GAAuB,E;;AAErC;AACJ;AACA;AATaxB,MAAAA,Y,CAUKsB,W,GAAyB,E;;AAEvC;AACJ;AACA;AAdatB,MAAAA,Y,CAeK0B,gB,GAAwC,E;;AAEtD;AACJ;AACA;AAnBa1B,MAAAA,Y,CAoBK+B,uB,GAAiD,E;;AAE/D;AACJ;AACA;AACA;AAzBa/B,MAAAA,Y,CA0BKa,kB,GAA8B,K;AA1BnCb,MAAAA,Y,CA4BKoC,gB,GAA2B,2B;AACzC;AACA;AA9BSpC,MAAAA,Y,CA+BKkC,Y;AA/BLlC,MAAAA,Y,CA2JFmD,a,GAAkD,IAAImC,GAAJ,E;AACzD;AA5JStF,MAAAA,Y,CA6JF4D,kB,GAA2D,IAAI0B,GAAJ,E", "sourcesContent": ["import { _decorator, find, Vec3, Node } from \"cc\";\nimport { Bullet } from \"./Bullet\";\nimport { Emitter } from \"./Emitter\";\nimport { eEventConditionType } from \"../data/EventConditionData\";\nimport { EventActionData, EmitterActionData, BulletActionData } from \"../data/EventActionData\";\nimport { EventActionRunner } from \"./EventRunner\";\nimport { ActionRegistry, GenericActionRunner, ActionValue, eEasing } from \"./actions\";\nconst { ccclass } = _decorator;\n\n/**\n * BulletSystem - manages all bullets in the game world\n * Handles bullet creation, movement, collision, and cleanup\n */\nexport class BulletSystem {\n\n    /**\n     * All active bullets\n     */\n    public static allBullets: Bullet[] = [];\n\n    /**\n     * All active emitters\n     */\n    public static allEmitters: Emitter[] = [];\n\n    /**\n     * All active action runners (legacy)\n     */\n    public static allActionRunners: EventActionRunner[] = [];\n\n    /**\n     * All active generic action runners (new system)\n     */\n    public static allGenericActionRunners: GenericActionRunner[] = [];\n\n    /**\n     * Flag to control which action system to use\n     * true = use new generic system, false = use legacy system\n     */\n    public static useNewActionSystem: boolean = false;\n\n    public static bulletParentPath: string = 'Canvas/GameUI/bullet_root';\n    // public static isEmitterEnabled: boolean = true;\n    // public static isBulletEnabled: boolean = true;\n    public static bulletParent: Node;\n\n    /**\n     * Initialize the bullet system\n     */\n    public static initialize() {\n        // Initialize the new action system\n        ActionRegistry.initialize();\n        console.log('BulletSystem initialized with new action system support');\n    }\n\n    /**\n     * Enable the new action system\n     */\n    public static enableNewActionSystem() {\n        if (!ActionRegistry.getHandlerCount()) {\n            ActionRegistry.initialize();\n        }\n        this.useNewActionSystem = true;\n        console.log('New action system enabled');\n    }\n\n    /**\n     * Disable the new action system (fallback to legacy)\n     */\n    public static disableNewActionSystem() {\n        this.useNewActionSystem = false;\n        console.log('Fallback to legacy action system');\n    }\n\n    /**\n     * Main update loop\n     */\n    public static tick(dt: number) {\n        this.tickEmitters(dt);\n        this.tickBullets(dt);\n        this.tickActionRunners(dt);\n        this.tickGenericActionRunners(dt);\n    }\n\n    public static tickEmitters(dt:number) {\n        for (const emitter of this.allEmitters) {\n            emitter.tick(dt);\n        }\n    }\n\n    public static tickBullets(dt:number) {\n        for (const bullet of this.allBullets) {\n            bullet.tick(dt);\n        }\n    }\n\n    public static tickActionRunners(dt: number) {\n        for (let i = this.allActionRunners.length - 1; i >= 0; i--) {\n            const runner = this.allActionRunners[i];\n            runner.tick(dt);\n            if (runner.isCompleted) {\n                this.allActionRunners.splice(i, 1);\n            }\n        }\n    }\n\n    public static tickGenericActionRunners(dt: number) {\n        for (let i = this.allGenericActionRunners.length - 1; i >= 0; i--) {\n            const runner = this.allGenericActionRunners[i];\n            runner.tick(dt);\n            if (runner.isCompleted) {\n                this.allGenericActionRunners.splice(i, 1);\n            }\n        }\n    }\n\n    public static onCreateEmitter(emitter:Emitter) {\n        for (let i = 0; i < this.allEmitters.length; i++) {\n            if (this.allEmitters[i] === emitter) {\n                return;\n            }\n        }\n\n        this.allEmitters.push(emitter);\n\n        if (!this.bulletParent || !this.bulletParent.isValid) {\n            if (this.bulletParentPath.length > 0) {\n                this.bulletParent = find(this.bulletParentPath);\n                if (!this.bulletParent) {\n                    console.warn('没有找到子弹父节点请检查路径:' + this.bulletParentPath);\n                    this.bulletParent = emitter.node;\n                }\n            }\n        }\n    }\n\n    public static onDestroyEmitter(emitter:Emitter) {\n        this.allEmitters = this.allEmitters.filter(e => e !== emitter);\n    }\n\n    public static onCreateBullet(bullet: Bullet) {\n        for (let i = 0; i < this.allBullets.length; i++) {\n            if (this.allBullets[i] === bullet) {\n                return;\n            }\n        }\n\n        bullet.onCreate();\n        this.allBullets.push(bullet);\n        bullet.node.setParent(this.bulletParent, true);\n    }\n\n    public static onDestroyBullet(bullet: Bullet) {\n        this.allBullets = this.allBullets.filter(b => b !== bullet);\n    }\n\n    public static destroyAllBullets() {\n        for (const bullet of this.allBullets) {\n            bullet.destroySelf();\n        }\n        this.allBullets = [];\n    }\n\n    /**\n     * EventCondition & EventAction 系统\n     */\n\n    // 记录条件状态\n    static allConditions: Map<eEventConditionType, number> = new Map();\n    // 条件监听者\n    static conditionListeners: Map<eEventConditionType, Function[]> = new Map();\n\n    public static getConditionValue(conditionType: eEventConditionType): number {\n        return this.allConditions.get(conditionType) || 0;\n    }\n\n    /**\n     * 这个函数需要在游戏各个阶段去调用设置\n     */\n    public static setConditionValue(conditionType: eEventConditionType, value: number) {\n        // check value changed\n        const oldValue = this.allConditions.get(conditionType);\n        if (oldValue !== value) {\n            this.allConditions.set(conditionType, value);\n            this.emit(conditionType, value);\n        }\n    }\n\n    public static on(conditionType: eEventConditionType, listener: Function) {\n        if (!this.conditionListeners.has(conditionType)) {\n            this.conditionListeners.set(conditionType, []);\n        }\n        this.conditionListeners.get(conditionType).push(listener);\n    }\n\n    public static off(conditionType: eEventConditionType, listener: Function) {\n        if (this.conditionListeners.has(conditionType)) {\n            const listeners = this.conditionListeners.get(conditionType);\n            this.conditionListeners.set(conditionType, listeners.filter(l => l !== listener));\n        }\n    }\n\n    public static emit(conditionType: eEventConditionType, ...args: any[]) {\n        if (this.conditionListeners.has(conditionType)) {\n            for (const listener of this.conditionListeners.get(conditionType)) {\n                listener(...args);\n            }\n        }\n    }\n\n    public static createActionRunner(owner: Emitter | Bullet, data: EventActionData): EventActionRunner {\n        const runner = new EventActionRunner(owner, data);\n        runner.start();\n        this.allActionRunners.push(runner);\n        return runner;\n    }\n\n    /**\n     * Create a new generic action runner (new system)\n     */\n    public static createGenericActionRunner(\n        owner: Emitter | Bullet,\n        actionType: number,\n        targetValue: ActionValue,\n        duration: number = 0,\n        easing: eEasing = eEasing.Linear,\n        isRandom: boolean = false,\n        minValue?: ActionValue,\n        maxValue?: ActionValue\n    ): GenericActionRunner | null {\n        try {\n            const runner = new GenericActionRunner(\n                owner,\n                actionType,\n                duration,\n                easing,\n                targetValue,\n                isRandom,\n                minValue,\n                maxValue\n            );\n            runner.start();\n            this.allGenericActionRunners.push(runner);\n            return runner;\n        } catch (error) {\n            console.error('Failed to create generic action runner:', error);\n            return null;\n        }\n    }\n\n    /**\n     * Create action runner using the appropriate system based on configuration\n     */\n    public static createActionRunnerAuto(owner: Emitter | Bullet, data: EventActionData): EventActionRunner | GenericActionRunner | null {\n        if (this.useNewActionSystem) {\n            // Use new system\n            const targetValue = data.isRandom ?\n                (data.minValue + data.maxValue) / 2 : // Use average for random values\n                (data.boolValue ? 1 : data.minValue); // Use boolValue or minValue\n\n            return this.createGenericActionRunner(\n                owner,\n                data.actionType,\n                targetValue,\n                data.duration,\n                data.easing,\n                data.isRandom,\n                data.minValue,\n                data.maxValue\n            );\n        } else {\n            // Use legacy system\n            return this.createActionRunner(owner, data);\n        }\n    }\n\n    /**\n     * Get debug information about active action runners\n     */\n    public static getActionRunnersDebugInfo(): string {\n        const legacyCount = this.allActionRunners.length;\n        const newCount = this.allGenericActionRunners.length;\n        const systemInUse = this.useNewActionSystem ? 'New' : 'Legacy';\n\n        return `Action Runners Debug Info:\nSystem in use: ${systemInUse}\nLegacy runners: ${legacyCount}\nGeneric runners: ${newCount}\nTotal active: ${legacyCount + newCount}`;\n    }\n}"]}