function e(e,t){const n=Object.create(null),r=e.split(",");for(let o=0;o<r.length;o++)n[r[o]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}const t={},n=[],r=()=>{},o=()=>!1,s=/^on[^a-z]/,l=e=>s.test(e),i=e=>e.startsWith("onUpdate:"),c=Object.assign,u=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},a=Object.prototype.hasOwnProperty,f=(e,t)=>a.call(e,t),p=Array.isArray,d=e=>"[object Map]"===w(e),h=e=>"[object Set]"===w(e),g=e=>"[object Date]"===w(e),m=e=>"function"==typeof e,v=e=>"string"==typeof e,y=e=>"symbol"==typeof e,_=e=>null!==e&&"object"==typeof e,b=e=>_(e)&&m(e.then)&&m(e.catch),x=Object.prototype.toString,w=e=>x.call(e),S=e=>"[object Object]"===w(e),C=e=>v(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,k=e(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),O=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},P=/-(\w)/g,T=O((e=>e.replace(P,((e,t)=>t?t.toUpperCase():"")))),E=/\B([A-Z])/g,R=O((e=>e.replace(E,"-$1").toLowerCase())),F=O((e=>e.charAt(0).toUpperCase()+e.slice(1))),$=O((e=>e?`on${F(e)}`:"")),M=(e,t)=>!Object.is(e,t),A=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},j=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},I=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let N;const V=()=>N||(N="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{});function U(e){if(p(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],o=v(r)?D(r):U(r);if(o)for(const e in o)t[e]=o[e]}return t}return v(e)||_(e)?e:void 0}const B=/;(?![^(]*\))/g,L=/:([^]+)/,W=/\/\*[^]*?\*\//g;function D(e){const t={};return e.replace(W,"").split(B).forEach((e=>{if(e){const n=e.split(L);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function H(e){let t="";if(v(e))t=e;else if(p(e))for(let n=0;n<e.length;n++){const r=H(e[n]);r&&(t+=r+" ")}else if(_(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const z=e("svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view"),q=e("area,base,br,col,embed,hr,img,input,link,meta,param,source,track,wbr"),G="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",K=e(G),J=e(G+",async,autofocus,autoplay,controls,default,defer,disabled,hidden,inert,loop,open,required,reversed,scoped,seamless,checked,muted,multiple,selected");function X(e){return!!e||""===e}const Z=/[>/="'\u0009\u000a\u000c\u0020]/,Q={};const Y={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv"},ee=/["'&<>]/;function te(e){const t=""+e,n=ee.exec(t);if(!n)return t;let r,o,s="",l=0;for(o=n.index;o<t.length;o++){switch(t.charCodeAt(o)){case 34:r="&quot;";break;case 38:r="&amp;";break;case 39:r="&#39;";break;case 60:r="&lt;";break;case 62:r="&gt;";break;default:continue}l!==o&&(s+=t.slice(l,o)),l=o+1,s+=r}return l!==o?s+t.slice(l,o):s}const ne=/^-?>|<!--|-->|--!>|<!-$/g;function re(e,t){if(e===t)return!0;let n=g(e),r=g(t);if(n||r)return!(!n||!r)&&e.getTime()===t.getTime();if(n=y(e),r=y(t),n||r)return e===t;if(n=p(e),r=p(t),n||r)return!(!n||!r)&&function(e,t){if(e.length!==t.length)return!1;let n=!0;for(let r=0;n&&r<e.length;r++)n=re(e[r],t[r]);return n}(e,t);if(n=_(e),r=_(t),n||r){if(!n||!r)return!1;if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e){const r=e.hasOwnProperty(n),o=t.hasOwnProperty(n);if(r&&!o||!r&&o||!re(e[n],t[n]))return!1}}return String(e)===String(t)}const oe=(e,t)=>t&&t.__v_isRef?oe(e,t.value):d(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n])=>(e[`${t} =>`]=n,e)),{})}:h(t)?{[`Set(${t.size})`]:[...t.values()]}:!_(t)||p(t)||S(t)?t:String(t);let se;class le{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=se,!e&&se&&(this.index=(se.scopes||(se.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=se;try{return se=this,e()}finally{se=t}}}on(){se=this}off(){se=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}const ie=e=>{const t=new Set(e);return t.w=0,t.n=0,t},ce=e=>(e.w&pe)>0,ue=e=>(e.n&pe)>0,ae=new WeakMap;let fe=0,pe=1;let de;const he=Symbol(""),ge=Symbol("");class me{constructor(e,t=null,n){this.fn=e,this.scheduler=t,this.active=!0,this.deps=[],this.parent=void 0,function(e,t=se){t&&t.active&&t.effects.push(e)}(this,n)}run(){if(!this.active)return this.fn();let e=de,t=ye;for(;e;){if(e===this)return;e=e.parent}try{return this.parent=de,de=this,ye=!0,pe=1<<++fe,fe<=30?(({deps:e})=>{if(e.length)for(let t=0;t<e.length;t++)e[t].w|=pe})(this):ve(this),this.fn()}finally{fe<=30&&(e=>{const{deps:t}=e;if(t.length){let n=0;for(let r=0;r<t.length;r++){const o=t[r];ce(o)&&!ue(o)?o.delete(e):t[n++]=o,o.w&=~pe,o.n&=~pe}t.length=n}})(this),pe=1<<--fe,de=this.parent,ye=t,this.parent=void 0,this.deferStop&&this.stop()}}stop(){de===this?this.deferStop=!0:this.active&&(ve(this),this.onStop&&this.onStop(),this.active=!1)}}function ve(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}let ye=!0;const _e=[];function be(){_e.push(ye),ye=!1}function xe(){const e=_e.pop();ye=void 0===e||e}function we(e,t,n){if(ye&&de){let t=ae.get(e);t||ae.set(e,t=new Map);let r=t.get(n);r||t.set(n,r=ie()),Se(r)}}function Se(e,t){let n=!1;fe<=30?ue(e)||(e.n|=pe,n=!ce(e)):n=!e.has(de),n&&(e.add(de),de.deps.push(e))}function Ce(e,t,n,r,o,s){const l=ae.get(e);if(!l)return;let i=[];if("clear"===t)i=[...l.values()];else if("length"===n&&p(e)){const e=Number(r);l.forEach(((t,n)=>{("length"===n||n>=e)&&i.push(t)}))}else switch(void 0!==n&&i.push(l.get(n)),t){case"add":p(e)?C(n)&&i.push(l.get("length")):(i.push(l.get(he)),d(e)&&i.push(l.get(ge)));break;case"delete":p(e)||(i.push(l.get(he)),d(e)&&i.push(l.get(ge)));break;case"set":d(e)&&i.push(l.get(he))}if(1===i.length)i[0]&&ke(i[0]);else{const e=[];for(const t of i)t&&e.push(...t);ke(ie(e))}}function ke(e,t){const n=p(e)?e:[...e];for(const r of n)r.computed&&Oe(r);for(const r of n)r.computed||Oe(r)}function Oe(e,t){(e!==de||e.allowRecurse)&&(e.scheduler?e.scheduler():e.run())}const Pe=e("__proto__,__v_isRef,__isVue"),Te=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(y)),Ee=je(),Re=je(!1,!0),Fe=je(!0),$e=Me();function Me(){const e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=_t(this);for(let t=0,o=this.length;t<o;t++)we(n,0,t+"");const r=n[t](...e);return-1===r||!1===r?n[t](...e.map(_t)):r}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){be();const n=_t(this)[t].apply(this,e);return xe(),n}})),e}function Ae(e){const t=_t(this);return we(t,0,e),t.hasOwnProperty(e)}function je(e=!1,t=!1){return function(n,r,o){if("__v_isReactive"===r)return!e;if("__v_isReadonly"===r)return e;if("__v_isShallow"===r)return t;if("__v_raw"===r&&o===(e?t?at:ut:t?ct:it).get(n))return n;const s=p(n);if(!e){if(s&&f($e,r))return Reflect.get($e,r,o);if("hasOwnProperty"===r)return Ae}const l=Reflect.get(n,r,o);return(y(r)?Te.has(r):Pe(r))?l:(e||we(n,0,r),t?l:St(l)?s&&C(r)?l:l.value:_(l)?e?dt(l):pt(l):l)}}function Ie(e=!1){return function(t,n,r,o){let s=t[n];if(mt(s)&&St(s)&&!St(r))return!1;if(!e&&(vt(r)||mt(r)||(s=_t(s),r=_t(r)),!p(t)&&St(s)&&!St(r)))return s.value=r,!0;const l=p(t)&&C(n)?Number(n)<t.length:f(t,n),i=Reflect.set(t,n,r,o);return t===_t(o)&&(l?M(r,s)&&Ce(t,"set",n,r):Ce(t,"add",n,r)),i}}const Ne={get:Ee,set:Ie(),deleteProperty:function(e,t){const n=f(e,t),r=Reflect.deleteProperty(e,t);return r&&n&&Ce(e,"delete",t,void 0),r},has:function(e,t){const n=Reflect.has(e,t);return y(t)&&Te.has(t)||we(e,0,t),n},ownKeys:function(e){return we(e,0,p(e)?"length":he),Reflect.ownKeys(e)}},Ve={get:Fe,set:(e,t)=>!0,deleteProperty:(e,t)=>!0},Ue=c({},Ne,{get:Re,set:Ie(!0)}),Be=e=>e,Le=e=>Reflect.getPrototypeOf(e);function We(e,t,n=!1,r=!1){const o=_t(e=e.__v_raw),s=_t(t);n||(t!==s&&we(o,0,t),we(o,0,s));const{has:l}=Le(o),i=r?Be:n?wt:xt;return l.call(o,t)?i(e.get(t)):l.call(o,s)?i(e.get(s)):void(e!==o&&e.get(t))}function De(e,t=!1){const n=this.__v_raw,r=_t(n),o=_t(e);return t||(e!==o&&we(r,0,e),we(r,0,o)),e===o?n.has(e):n.has(e)||n.has(o)}function He(e,t=!1){return e=e.__v_raw,!t&&we(_t(e),0,he),Reflect.get(e,"size",e)}function ze(e){e=_t(e);const t=_t(this);return Le(t).has.call(t,e)||(t.add(e),Ce(t,"add",e,e)),this}function qe(e,t){t=_t(t);const n=_t(this),{has:r,get:o}=Le(n);let s=r.call(n,e);s||(e=_t(e),s=r.call(n,e));const l=o.call(n,e);return n.set(e,t),s?M(t,l)&&Ce(n,"set",e,t):Ce(n,"add",e,t),this}function Ge(e){const t=_t(this),{has:n,get:r}=Le(t);let o=n.call(t,e);o||(e=_t(e),o=n.call(t,e)),r&&r.call(t,e);const s=t.delete(e);return o&&Ce(t,"delete",e,void 0),s}function Ke(){const e=_t(this),t=0!==e.size,n=e.clear();return t&&Ce(e,"clear",void 0,void 0),n}function Je(e,t){return function(n,r){const o=this,s=o.__v_raw,l=_t(s),i=t?Be:e?wt:xt;return!e&&we(l,0,he),s.forEach(((e,t)=>n.call(r,i(e),i(t),o)))}}function Xe(e,t,n){return function(...r){const o=this.__v_raw,s=_t(o),l=d(s),i="entries"===e||e===Symbol.iterator&&l,c="keys"===e&&l,u=o[e](...r),a=n?Be:t?wt:xt;return!t&&we(s,0,c?ge:he),{next(){const{value:e,done:t}=u.next();return t?{value:e,done:t}:{value:i?[a(e[0]),a(e[1])]:a(e),done:t}},[Symbol.iterator](){return this}}}}function Ze(e){return function(...t){return"delete"!==e&&this}}function Qe(){const e={get(e){return We(this,e)},get size(){return He(this)},has:De,add:ze,set:qe,delete:Ge,clear:Ke,forEach:Je(!1,!1)},t={get(e){return We(this,e,!1,!0)},get size(){return He(this)},has:De,add:ze,set:qe,delete:Ge,clear:Ke,forEach:Je(!1,!0)},n={get(e){return We(this,e,!0)},get size(){return He(this,!0)},has(e){return De.call(this,e,!0)},add:Ze("add"),set:Ze("set"),delete:Ze("delete"),clear:Ze("clear"),forEach:Je(!0,!1)},r={get(e){return We(this,e,!0,!0)},get size(){return He(this,!0)},has(e){return De.call(this,e,!0)},add:Ze("add"),set:Ze("set"),delete:Ze("delete"),clear:Ze("clear"),forEach:Je(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((o=>{e[o]=Xe(o,!1,!1),n[o]=Xe(o,!0,!1),t[o]=Xe(o,!1,!0),r[o]=Xe(o,!0,!0)})),[e,n,t,r]}const[Ye,et,tt,nt]=Qe();function rt(e,t){const n=t?e?nt:tt:e?et:Ye;return(t,r,o)=>"__v_isReactive"===r?!e:"__v_isReadonly"===r?e:"__v_raw"===r?t:Reflect.get(f(n,r)&&r in t?n:t,r,o)}const ot={get:rt(!1,!1)},st={get:rt(!1,!0)},lt={get:rt(!0,!1)},it=new WeakMap,ct=new WeakMap,ut=new WeakMap,at=new WeakMap;function ft(e){return e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((e=>w(e).slice(8,-1))(e))}function pt(e){return mt(e)?e:ht(e,!1,Ne,ot,it)}function dt(e){return ht(e,!0,Ve,lt,ut)}function ht(e,t,n,r,o){if(!_(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const s=o.get(e);if(s)return s;const l=ft(e);if(0===l)return e;const i=new Proxy(e,2===l?r:n);return o.set(e,i),i}function gt(e){return mt(e)?gt(e.__v_raw):!(!e||!e.__v_isReactive)}function mt(e){return!(!e||!e.__v_isReadonly)}function vt(e){return!(!e||!e.__v_isShallow)}function yt(e){return gt(e)||mt(e)}function _t(e){const t=e&&e.__v_raw;return t?_t(t):e}function bt(e){return j(e,"__v_skip",!0),e}const xt=e=>_(e)?pt(e):e,wt=e=>_(e)?dt(e):e;function St(e){return!(!e||!0!==e.__v_isRef)}const Ct={get:(e,t,n)=>{return St(r=Reflect.get(e,t,n))?r.value:r;var r},set:(e,t,n,r)=>{const o=e[t];return St(o)&&!St(n)?(o.value=n,!0):Reflect.set(e,t,n,r)}};function kt(e){return gt(e)?e:new Proxy(e,Ct)}class Ot{constructor(e,t,n,r){this._setter=t,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this._dirty=!0,this.effect=new me(e,(()=>{this._dirty||(this._dirty=!0,function(e,t){const n=(e=_t(e)).dep;n&&ke(n)}(this))})),this.effect.computed=this,this.effect.active=this._cacheable=!r,this.__v_isReadonly=n}get value(){const e=_t(this);var t;return t=e,ye&&de&&Se((t=_t(t)).dep||(t.dep=ie())),!e._dirty&&e._cacheable||(e._dirty=!1,e._value=e.effect.run()),e._value}set value(e){this._setter(e)}}function Pt(e,t,n,r){let o;try{o=r?e(...r):e()}catch(s){Et(s,t,n)}return o}function Tt(e,t,n,r){if(m(e)){const o=Pt(e,t,n,r);return o&&b(o)&&o.catch((e=>{Et(e,t,n)})),o}const o=[];for(let s=0;s<e.length;s++)o.push(Tt(e[s],t,n,r));return o}function Et(e,t,n,r=!0){if(t){let r=t.parent;const o=t.proxy,s=n;for(;r;){const t=r.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,o,s))return;r=r.parent}const l=t.appContext.config.errorHandler;if(l)return void Pt(l,null,10,[e,o,s])}!function(e,t,n,r=!0){console.error(e)}(e,0,0,r)}let Rt=!1,Ft=!1;const $t=[];let Mt=0;const At=[];let jt=null,It=0;const Nt=Promise.resolve();let Vt=null;function Ut(e){const t=Vt||Nt;return e?t.then(this?e.bind(this):e):t}function Bt(e){$t.length&&$t.includes(e,Rt&&e.allowRecurse?Mt+1:Mt)||(null==e.id?$t.push(e):$t.splice(function(e){let t=Mt+1,n=$t.length;for(;t<n;){const r=t+n>>>1;Ht($t[r])<e?t=r+1:n=r}return t}(e.id),0,e),Lt())}function Lt(){Rt||Ft||(Ft=!0,Vt=Nt.then(qt))}function Wt(e,t=(Rt?Mt+1:0)){for(;t<$t.length;t++){const e=$t[t];e&&e.pre&&($t.splice(t,1),t--,e())}}function Dt(e){if(At.length){const e=[...new Set(At)];if(At.length=0,jt)return void jt.push(...e);for(jt=e,jt.sort(((e,t)=>Ht(e)-Ht(t))),It=0;It<jt.length;It++)jt[It]();jt=null,It=0}}const Ht=e=>null==e.id?1/0:e.id,zt=(e,t)=>{const n=Ht(e)-Ht(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function qt(e){Ft=!1,Rt=!0,$t.sort(zt);try{for(Mt=0;Mt<$t.length;Mt++){const e=$t[Mt];e&&!1!==e.active&&Pt(e,null,14)}}finally{Mt=0,$t.length=0,Dt(),Rt=!1,Vt=null,($t.length||At.length)&&qt()}}function Gt(e,n,...r){if(e.isUnmounted)return;const o=e.vnode.props||t;let s=r;const l=n.startsWith("update:"),i=l&&n.slice(7);if(i&&i in o){const e=`${"modelValue"===i?"model":i}Modifiers`,{number:n,trim:l}=o[e]||t;l&&(s=r.map((e=>v(e)?e.trim():e))),n&&(s=r.map(I))}let c,u=o[c=$(n)]||o[c=$(T(n))];!u&&l&&(u=o[c=$(R(n))]),u&&Tt(u,e,6,s);const a=o[c+"Once"];if(a){if(e.emitted){if(e.emitted[c])return}else e.emitted={};e.emitted[c]=!0,Tt(a,e,6,s)}}function Kt(e,t,n=!1){const r=t.emitsCache,o=r.get(e);if(void 0!==o)return o;const s=e.emits;let l={},i=!1;if(!m(e)){const r=e=>{const n=Kt(e,t,!0);n&&(i=!0,c(l,n))};!n&&t.mixins.length&&t.mixins.forEach(r),e.extends&&r(e.extends),e.mixins&&e.mixins.forEach(r)}return s||i?(p(s)?s.forEach((e=>l[e]=null)):c(l,s),_(e)&&r.set(e,l),l):(_(e)&&r.set(e,null),null)}function Jt(e,t){return!(!e||!l(t))&&(t=t.slice(2).replace(/Once$/,""),f(e,t[0].toLowerCase()+t.slice(1))||f(e,R(t))||f(e,t))}let Xt=null,Zt=null;function Qt(e){const t=Xt;return Xt=e,Zt=e&&e.type.__scopeId||null,t}function Yt(e){const{type:t,vnode:n,proxy:r,withProxy:o,props:s,propsOptions:[l],slots:c,attrs:u,emit:a,render:f,renderCache:p,data:d,setupState:h,ctx:g,inheritAttrs:m}=e;let v,y;const _=Qt(e);try{if(4&n.shapeFlag){const e=o||r;v=Rr(f.call(e,e,p,s,h,d,g)),y=u}else{const e=t;0,v=Rr(e(s,e.length>1?{attrs:u,slots:c,emit:a}:null)),y=t.props?u:en(u)}}catch(x){Et(x,e,1),v=Pr(yr)}let b=v;if(y&&!1!==m){const e=Object.keys(y),{shapeFlag:t}=b;e.length&&7&t&&(l&&e.some(i)&&(y=tn(y,l)),b=Tr(b,y))}return n.dirs&&(b=Tr(b),b.dirs=b.dirs?b.dirs.concat(n.dirs):n.dirs),n.transition&&(b.transition=n.transition),v=b,Qt(_),v}const en=e=>{let t;for(const n in e)("class"===n||"style"===n||l(n))&&((t||(t={}))[n]=e[n]);return t},tn=(e,t)=>{const n={};for(const r in e)i(r)&&r.slice(9)in t||(n[r]=e[r]);return n};function nn(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let o=0;o<r.length;o++){const s=r[o];if(t[s]!==e[s]&&!Jt(n,s))return!0}return!1}const rn={};function on(e,t,n){return sn(e,t,n)}function sn(e,n,{immediate:o,deep:s,flush:l}=t){var i;const c=se===(null==(i=Br)?void 0:i.scope)?Br:null;let a,f,d=!1,h=!1;if(St(e)?(a=()=>e.value,d=vt(e)):gt(e)?(a=()=>e,s=!0):p(e)?(h=!0,d=e.some((e=>gt(e)||vt(e))),a=()=>e.map((e=>St(e)?e.value:gt(e)?un(e):m(e)?Pt(e,c,2):void 0))):a=m(e)?n?()=>Pt(e,c,2):()=>{if(!c||!c.isUnmounted)return f&&f(),Tt(e,c,3,[v])}:r,n&&s){const e=a;a=()=>un(e())}let g,v=e=>{f=x.onStop=()=>{Pt(e,c,4)}};if(zr){if(v=r,n?o&&Tt(n,c,3,[a(),h?[]:void 0,v]):a(),"sync"!==l)return r;{const e=Qr();g=e.__watcherHandles||(e.__watcherHandles=[])}}let y=h?new Array(e.length).fill(rn):rn;const _=()=>{if(x.active)if(n){const e=x.run();(s||d||(h?e.some(((e,t)=>M(e,y[t]))):M(e,y)))&&(f&&f(),Tt(n,c,3,[e,y===rn?void 0:h&&y[0]===rn?[]:y,v]),y=e)}else x.run()};let b;_.allowRecurse=!!n,"sync"===l?b=_:"post"===l?b=()=>pr(_,c&&c.suspense):(_.pre=!0,c&&(_.id=c.uid),b=()=>Bt(_));const x=new me(a,b);n?o?_():y=x.run():"post"===l?pr(x.run.bind(x),c&&c.suspense):x.run();const w=()=>{x.stop(),c&&c.scope&&u(c.scope.effects,x)};return g&&g.push(w),w}function ln(e,t,n){const r=this.proxy,o=v(e)?e.includes(".")?cn(r,e):()=>r[e]:e.bind(r,r);let s;m(t)?s=t:(s=t.handler,n=t);const l=Br;Wr(this);const i=sn(o,s.bind(r),n);return l?Wr(l):Dr(),i}function cn(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function un(e,t){if(!_(e)||e.__v_skip)return e;if((t=t||new Set).has(e))return e;if(t.add(e),St(e))un(e.value,t);else if(p(e))for(let n=0;n<e.length;n++)un(e[n],t);else if(h(e)||d(e))e.forEach((e=>{un(e,t)}));else if(S(e))for(const n in e)un(e[n],t);return e}function an(e,t,n,r){const o=e.dirs,s=t&&t.dirs;for(let l=0;l<o.length;l++){const i=o[l];s&&(i.oldValue=s[l].value);let c=i.dir[r];c&&(be(),Tt(c,n,8,[e.el,i,e,t]),xe())}}const fn=e=>!!e.type.__asyncLoader,pn=e=>e.type.__isKeepAlive;function dn(e,t){gn(e,"a",t)}function hn(e,t){gn(e,"da",t)}function gn(e,t,n=Br){const r=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(vn(t,r,n),n){let e=n.parent;for(;e&&e.parent;)pn(e.parent.vnode)&&mn(r,t,n,e),e=e.parent}}function mn(e,t,n,r){const o=vn(t,e,r,!0);Cn((()=>{u(r[t],o)}),n)}function vn(e,t,n=Br,r=!1){if(n){const o=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...r)=>{if(n.isUnmounted)return;be(),Wr(n);const o=Tt(t,n,e,r);return Dr(),xe(),o});return r?o.unshift(s):o.push(s),s}}const yn=e=>(t,n=Br)=>(!zr||"sp"===e)&&vn(e,((...e)=>t(...e)),n),_n=yn("bm"),bn=yn("m"),xn=yn("bu"),wn=yn("u"),Sn=yn("bum"),Cn=yn("um"),kn=yn("sp"),On=yn("rtg"),Pn=yn("rtc");function Tn(e,t=Br){vn("ec",e,t)}const En=Symbol.for("v-ndc"),Rn=e=>e?Hr(e)?Jr(e)||e.proxy:Rn(e.parent):null,Fn=c(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Rn(e.parent),$root:e=>Rn(e.root),$emit:e=>e.emit,$options:e=>Un(e),$forceUpdate:e=>e.f||(e.f=()=>Bt(e.update)),$nextTick:e=>e.n||(e.n=Ut.bind(e.proxy)),$watch:e=>ln.bind(e)}),$n=(e,n)=>e!==t&&!e.__isScriptSetup&&f(e,n),Mn={get({_:e},n){const{ctx:r,setupState:o,data:s,props:l,accessCache:i,type:c,appContext:u}=e;let a;if("$"!==n[0]){const c=i[n];if(void 0!==c)switch(c){case 1:return o[n];case 2:return s[n];case 4:return r[n];case 3:return l[n]}else{if($n(o,n))return i[n]=1,o[n];if(s!==t&&f(s,n))return i[n]=2,s[n];if((a=e.propsOptions[0])&&f(a,n))return i[n]=3,l[n];if(r!==t&&f(r,n))return i[n]=4,r[n];jn&&(i[n]=0)}}const p=Fn[n];let d,h;return p?("$attrs"===n&&we(e,0,n),p(e)):(d=c.__cssModules)&&(d=d[n])?d:r!==t&&f(r,n)?(i[n]=4,r[n]):(h=u.config.globalProperties,f(h,n)?h[n]:void 0)},set({_:e},n,r){const{data:o,setupState:s,ctx:l}=e;return $n(s,n)?(s[n]=r,!0):o!==t&&f(o,n)?(o[n]=r,!0):!f(e.props,n)&&(("$"!==n[0]||!(n.slice(1)in e))&&(l[n]=r,!0))},has({_:{data:e,setupState:n,accessCache:r,ctx:o,appContext:s,propsOptions:l}},i){let c;return!!r[i]||e!==t&&f(e,i)||$n(n,i)||(c=l[0])&&f(c,i)||f(o,i)||f(Fn,i)||f(s.config.globalProperties,i)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:f(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function An(e){return p(e)?e.reduce(((e,t)=>(e[t]=null,e)),{}):e}let jn=!0;function In(e){const t=Un(e),n=e.proxy,o=e.ctx;jn=!1,t.beforeCreate&&Nn(t.beforeCreate,e,"bc");const{data:s,computed:l,methods:i,watch:c,provide:u,inject:a,created:f,beforeMount:d,mounted:h,beforeUpdate:g,updated:v,activated:y,deactivated:b,beforeUnmount:x,unmounted:w,render:S,renderTracked:C,renderTriggered:k,errorCaptured:O,serverPrefetch:P,expose:T,inheritAttrs:E,components:R,directives:F}=t;if(a&&function(e,t,n=r){p(e)&&(e=Dn(e));for(const r in e){const n=e[r];let o;o=_(n)?"default"in n?Zn(n.from||r,n.default,!0):Zn(n.from||r):Zn(n),St(o)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>o.value,set:e=>o.value=e}):t[r]=o}}(a,o,null),i)for(const r in i){const e=i[r];m(e)&&(o[r]=e.bind(n))}if(s){const t=s.call(n,n);_(t)&&(e.data=pt(t))}if(jn=!0,l)for(const p in l){const e=l[p],t=m(e)?e.bind(n,n):m(e.get)?e.get.bind(n,n):r,s=!m(e)&&m(e.set)?e.set.bind(n):r,i=Xr({get:t,set:s});Object.defineProperty(o,p,{enumerable:!0,configurable:!0,get:()=>i.value,set:e=>i.value=e})}if(c)for(const r in c)Vn(c[r],o,n,r);if(u){const e=m(u)?u.call(n):u;Reflect.ownKeys(e).forEach((t=>{!function(e,t){if(Br){let n=Br.provides;const r=Br.parent&&Br.parent.provides;r===n&&(n=Br.provides=Object.create(r)),n[e]=t}else;}(t,e[t])}))}function $(e,t){p(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(f&&Nn(f,e,"c"),$(_n,d),$(bn,h),$(xn,g),$(wn,v),$(dn,y),$(hn,b),$(Tn,O),$(Pn,C),$(On,k),$(Sn,x),$(Cn,w),$(kn,P),p(T))if(T.length){const t=e.exposed||(e.exposed={});T.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});S&&e.render===r&&(e.render=S),null!=E&&(e.inheritAttrs=E),R&&(e.components=R),F&&(e.directives=F)}function Nn(e,t,n){Tt(p(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function Vn(e,t,n,r){const o=r.includes(".")?cn(n,r):()=>n[r];if(v(e)){const n=t[e];m(n)&&on(o,n)}else if(m(e))on(o,e.bind(n));else if(_(e))if(p(e))e.forEach((e=>Vn(e,t,n,r)));else{const r=m(e.handler)?e.handler.bind(n):t[e.handler];m(r)&&on(o,r,e)}}function Un(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:o,optionsCache:s,config:{optionMergeStrategies:l}}=e.appContext,i=s.get(t);let c;return i?c=i:o.length||n||r?(c={},o.length&&o.forEach((e=>Bn(c,e,l,!0))),Bn(c,t,l)):c=t,_(t)&&s.set(t,c),c}function Bn(e,t,n,r=!1){const{mixins:o,extends:s}=t;s&&Bn(e,s,n,!0),o&&o.forEach((t=>Bn(e,t,n,!0)));for(const l in t)if(r&&"expose"===l);else{const r=Ln[l]||n&&n[l];e[l]=r?r(e[l],t[l]):t[l]}return e}const Ln={data:Wn,props:qn,emits:qn,methods:zn,computed:zn,beforeCreate:Hn,created:Hn,beforeMount:Hn,mounted:Hn,beforeUpdate:Hn,updated:Hn,beforeDestroy:Hn,beforeUnmount:Hn,destroyed:Hn,unmounted:Hn,activated:Hn,deactivated:Hn,errorCaptured:Hn,serverPrefetch:Hn,components:zn,directives:zn,watch:function(e,t){if(!e)return t;if(!t)return e;const n=c(Object.create(null),e);for(const r in t)n[r]=Hn(e[r],t[r]);return n},provide:Wn,inject:function(e,t){return zn(Dn(e),Dn(t))}};function Wn(e,t){return t?e?function(){return c(m(e)?e.call(this,this):e,m(t)?t.call(this,this):t)}:t:e}function Dn(e){if(p(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Hn(e,t){return e?[...new Set([].concat(e,t))]:t}function zn(e,t){return e?c(Object.create(null),e,t):t}function qn(e,t){return e?p(e)&&p(t)?[...new Set([...e,...t])]:c(Object.create(null),An(e),An(null!=t?t:{})):t}function Gn(){return{app:null,config:{isNativeTag:o,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Kn=0;function Jn(e,t){return function(n,r=null){m(n)||(n=c({},n)),null==r||_(r)||(r=null);const o=Gn(),s=new Set;let l=!1;const i=o.app={_uid:Kn++,_component:n,_props:r,_container:null,_context:o,_instance:null,version:Yr,get config(){return o.config},set config(e){},use:(e,...t)=>(s.has(e)||(e&&m(e.install)?(s.add(e),e.install(i,...t)):m(e)&&(s.add(e),e(i,...t))),i),mixin:e=>(o.mixins.includes(e)||o.mixins.push(e),i),component:(e,t)=>t?(o.components[e]=t,i):o.components[e],directive:(e,t)=>t?(o.directives[e]=t,i):o.directives[e],mount(s,c,u){if(!l){const a=Pr(n,r);return a.appContext=o,c&&t?t(a,s):e(a,s,u),l=!0,i._container=s,s.__vue_app__=i,Jr(a.component)||a.component.proxy}},unmount(){l&&(e(null,i._container),delete i._container.__vue_app__)},provide:(e,t)=>(o.provides[e]=t,i),runWithContext(e){Xn=i;try{return e()}finally{Xn=null}}};return i}}let Xn=null;function Zn(e,t,n=!1){const r=Br||Xt;if(r||Xn){const o=r?null==r.parent?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:Xn._context.provides;if(o&&e in o)return o[e];if(arguments.length>1)return n&&m(t)?t.call(r&&r.proxy):t}}function Qn(e,t,n,r=!1){const o={},s={};j(s,Cr,1),e.propsDefaults=Object.create(null),Yn(e,t,o,s);for(const l in e.propsOptions[0])l in o||(o[l]=void 0);e.props=n?r?o:ht(o,!1,Ue,st,ct):e.type.props?o:s,e.attrs=s}function Yn(e,n,r,o){const[s,l]=e.propsOptions;let i,c=!1;if(n)for(let t in n){if(k(t))continue;const u=n[t];let a;s&&f(s,a=T(t))?l&&l.includes(a)?(i||(i={}))[a]=u:r[a]=u:Jt(e.emitsOptions,t)||t in o&&u===o[t]||(o[t]=u,c=!0)}if(l){const n=_t(r),o=i||t;for(let t=0;t<l.length;t++){const i=l[t];r[i]=er(s,n,i,o[i],e,!f(o,i))}}return c}function er(e,t,n,r,o,s){const l=e[n];if(null!=l){const e=f(l,"default");if(e&&void 0===r){const e=l.default;if(l.type!==Function&&!l.skipFactory&&m(e)){const{propsDefaults:s}=o;n in s?r=s[n]:(Wr(o),r=s[n]=e.call(null,t),Dr())}else r=e}l[0]&&(s&&!e?r=!1:!l[1]||""!==r&&r!==R(n)||(r=!0))}return r}function tr(e,r,o=!1){const s=r.propsCache,l=s.get(e);if(l)return l;const i=e.props,u={},a=[];let d=!1;if(!m(e)){const t=e=>{d=!0;const[t,n]=tr(e,r,!0);c(u,t),n&&a.push(...n)};!o&&r.mixins.length&&r.mixins.forEach(t),e.extends&&t(e.extends),e.mixins&&e.mixins.forEach(t)}if(!i&&!d)return _(e)&&s.set(e,n),n;if(p(i))for(let n=0;n<i.length;n++){const e=T(i[n]);nr(e)&&(u[e]=t)}else if(i)for(const t in i){const e=T(t);if(nr(e)){const n=i[t],r=u[e]=p(n)||m(n)?{type:n}:c({},n);if(r){const t=sr(Boolean,r.type),n=sr(String,r.type);r[0]=t>-1,r[1]=n<0||t<n,(t>-1||f(r,"default"))&&a.push(e)}}}const h=[u,a];return _(e)&&s.set(e,h),h}function nr(e){return"$"!==e[0]}function rr(e){const t=e&&e.toString().match(/^\s*(function|class) (\w+)/);return t?t[2]:null===e?"null":""}function or(e,t){return rr(e)===rr(t)}function sr(e,t){return p(t)?t.findIndex((t=>or(t,e))):m(t)&&or(t,e)?0:-1}const lr=e=>"_"===e[0]||"$stable"===e,ir=e=>p(e)?e.map(Rr):[Rr(e)],cr=(e,t,n)=>{if(t._n)return t;const r=function(e,t=Xt,n){if(!t)return e;if(e._n)return e;const r=(...n)=>{r._d&&xr(-1);const o=Qt(t);let s;try{s=e(...n)}finally{Qt(o),r._d&&xr(1)}return s};return r._n=!0,r._c=!0,r._d=!0,r}(((...e)=>ir(t(...e))),n);return r._c=!1,r},ur=(e,t,n)=>{const r=e._ctx;for(const o in e){if(lr(o))continue;const n=e[o];if(m(n))t[o]=cr(0,n,r);else if(null!=n){const e=ir(n);t[o]=()=>e}}},ar=(e,t)=>{const n=ir(t);e.slots.default=()=>n};function fr(e,n,r,o,s=!1){if(p(e))return void e.forEach(((e,t)=>fr(e,n&&(p(n)?n[t]:n),r,o,s)));if(fn(o)&&!s)return;const l=4&o.shapeFlag?Jr(o.component)||o.component.proxy:o.el,i=s?null:l,{i:c,r:a}=e,d=n&&n.r,h=c.refs===t?c.refs={}:c.refs,g=c.setupState;if(null!=d&&d!==a&&(v(d)?(h[d]=null,f(g,d)&&(g[d]=null)):St(d)&&(d.value=null)),m(a))Pt(a,c,12,[i,h]);else{const t=v(a),n=St(a);if(t||n){const o=()=>{if(e.f){const n=t?f(g,a)?g[a]:h[a]:a.value;s?p(n)&&u(n,l):p(n)?n.includes(l)||n.push(l):t?(h[a]=[l],f(g,a)&&(g[a]=h[a])):(a.value=[l],e.k&&(h[e.k]=a.value))}else t?(h[a]=i,f(g,a)&&(g[a]=i)):n&&(a.value=i,e.k&&(h[e.k]=i))};i?(o.id=-1,pr(o,r)):o()}}}const pr=function(e,t){var n;t&&t.pendingBranch?p(e)?t.effects.push(...e):t.effects.push(e):(p(n=e)?At.push(...n):jt&&jt.includes(n,n.allowRecurse?It+1:It)||At.push(n),Lt())};function dr(e){return function(e,o){V().__VUE__=!0;const{insert:s,remove:l,patchProp:i,createElement:u,createText:a,createComment:p,setText:d,setElementText:h,parentNode:g,nextSibling:m,setScopeId:v=r,insertStaticContent:y}=e,_=(e,t,n,r=null,o=null,s=null,l=!1,i=null,c=!!t.dynamicChildren)=>{if(e===t)return;e&&!Sr(e,t)&&(r=Q(e),G(e,o,s,!0),e=null),-2===t.patchFlag&&(c=!1,t.dynamicChildren=null);const{type:u,ref:a,shapeFlag:f}=t;switch(u){case vr:b(e,t,n,r);break;case yr:x(e,t,n,r);break;case _r:null==e&&w(t,n,r,l);break;case mr:I(e,t,n,r,o,s,l,i,c);break;default:1&f?O(e,t,n,r,o,s,l,i,c):6&f?N(e,t,n,r,o,s,l,i,c):(64&f||128&f)&&u.process(e,t,n,r,o,s,l,i,c,ee)}null!=a&&o&&fr(a,e&&e.ref,s,t||e,!t)},b=(e,t,n,r)=>{if(null==e)s(t.el=a(t.children),n,r);else{const n=t.el=e.el;t.children!==e.children&&d(n,t.children)}},x=(e,t,n,r)=>{null==e?s(t.el=p(t.children||""),n,r):t.el=e.el},w=(e,t,n,r)=>{[e.el,e.anchor]=y(e.children,t,n,r,e.el,e.anchor)},S=({el:e,anchor:t},n,r)=>{let o;for(;e&&e!==t;)o=m(e),s(e,n,r),e=o;s(t,n,r)},C=({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=m(e),l(e),e=n;l(t)},O=(e,t,n,r,o,s,l,i,c)=>{l=l||"svg"===t.type,null==e?P(t,n,r,o,s,l,i,c):$(e,t,o,s,l,i,c)},P=(e,t,n,r,o,l,c,a)=>{let f,p;const{type:d,props:g,shapeFlag:m,transition:v,dirs:y}=e;if(f=e.el=u(e.type,l,g&&g.is,g),8&m?h(f,e.children):16&m&&F(e.children,f,null,r,o,l&&"foreignObject"!==d,c,a),y&&an(e,null,r,"created"),E(f,e,e.scopeId,c,r),g){for(const t in g)"value"===t||k(t)||i(f,t,null,g[t],l,e.children,r,o,Z);"value"in g&&i(f,"value",null,g.value),(p=g.onVnodeBeforeMount)&&Ar(p,r,e)}y&&an(e,null,r,"beforeMount");const _=(!o||o&&!o.pendingBranch)&&v&&!v.persisted;_&&v.beforeEnter(f),s(f,t,n),((p=g&&g.onVnodeMounted)||_||y)&&pr((()=>{p&&Ar(p,r,e),_&&v.enter(f),y&&an(e,null,r,"mounted")}),o)},E=(e,t,n,r,o)=>{if(n&&v(e,n),r)for(let s=0;s<r.length;s++)v(e,r[s]);if(o){if(t===o.subTree){const t=o.vnode;E(e,t,t.scopeId,t.slotScopeIds,o.parent)}}},F=(e,t,n,r,o,s,l,i,c=0)=>{for(let u=c;u<e.length;u++){const c=e[u]=i?Fr(e[u]):Rr(e[u]);_(null,c,t,n,r,o,s,l,i)}},$=(e,n,r,o,s,l,c)=>{const u=n.el=e.el;let{patchFlag:a,dynamicChildren:f,dirs:p}=n;a|=16&e.patchFlag;const d=e.props||t,g=n.props||t;let m;r&&hr(r,!1),(m=g.onVnodeBeforeUpdate)&&Ar(m,r,n,e),p&&an(n,e,r,"beforeUpdate"),r&&hr(r,!0);const v=s&&"foreignObject"!==n.type;if(f?M(e.dynamicChildren,f,u,r,o,v,l):c||D(e,n,u,null,r,o,v,l,!1),a>0){if(16&a)j(u,n,d,g,r,o,s);else if(2&a&&d.class!==g.class&&i(u,"class",null,g.class,s),4&a&&i(u,"style",d.style,g.style,s),8&a){const t=n.dynamicProps;for(let n=0;n<t.length;n++){const l=t[n],c=d[l],a=g[l];a===c&&"value"!==l||i(u,l,c,a,s,e.children,r,o,Z)}}1&a&&e.children!==n.children&&h(u,n.children)}else c||null!=f||j(u,n,d,g,r,o,s);((m=g.onVnodeUpdated)||p)&&pr((()=>{m&&Ar(m,r,n,e),p&&an(n,e,r,"updated")}),o)},M=(e,t,n,r,o,s,l)=>{for(let i=0;i<t.length;i++){const c=e[i],u=t[i],a=c.el&&(c.type===mr||!Sr(c,u)||70&c.shapeFlag)?g(c.el):n;_(c,u,a,null,r,o,s,l,!0)}},j=(e,n,r,o,s,l,c)=>{if(r!==o){if(r!==t)for(const t in r)k(t)||t in o||i(e,t,r[t],null,c,n.children,s,l,Z);for(const t in o){if(k(t))continue;const u=o[t],a=r[t];u!==a&&"value"!==t&&i(e,t,a,u,c,n.children,s,l,Z)}"value"in o&&i(e,"value",r.value,o.value)}},I=(e,t,n,r,o,l,i,c,u)=>{const f=t.el=e?e.el:a(""),p=t.anchor=e?e.anchor:a("");let{patchFlag:d,dynamicChildren:h,slotScopeIds:g}=t;g&&(c=c?c.concat(g):g),null==e?(s(f,n,r),s(p,n,r),F(t.children,n,p,o,l,i,c,u)):d>0&&64&d&&h&&e.dynamicChildren?(M(e.dynamicChildren,h,n,o,l,i,c),(null!=t.key||o&&t===o.subTree)&&gr(e,t,!0)):D(e,t,n,p,o,l,i,c,u)},N=(e,t,n,r,o,s,l,i,c)=>{t.slotScopeIds=i,null==e?512&t.shapeFlag?o.ctx.activate(t,n,r,l,c):U(t,n,r,o,s,l,c):B(e,t,c)},U=(e,t,n,r,o,s,l)=>{const i=e.component=Nr(e,r,o);if(pn(e)&&(i.ctx.renderer=ee),qr(i),i.asyncDep){if(o&&o.registerDep(i,L),!e.el){const e=i.subTree=Pr(yr);x(null,e,t,n)}}else L(i,e,t,n,o,s,l)},B=(e,t,n)=>{const r=t.component=e.component;if(function(e,t,n){const{props:r,children:o,component:s}=e,{props:l,children:i,patchFlag:c}=t,u=s.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&c>=0))return!(!o&&!i||i&&i.$stable)||r!==l&&(r?!l||nn(r,l,u):!!l);if(1024&c)return!0;if(16&c)return r?nn(r,l,u):!!l;if(8&c){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(l[n]!==r[n]&&!Jt(u,n))return!0}}return!1}(e,t,n)){if(r.asyncDep&&!r.asyncResolved)return void W(r,t,n);r.next=t,function(e){const t=$t.indexOf(e);t>Mt&&$t.splice(t,1)}(r.update),r.update()}else t.el=e.el,r.vnode=t},L=(e,t,n,r,o,s,l)=>{const i=()=>{if(e.isMounted){let t,{next:n,bu:r,u:i,parent:c,vnode:u}=e,a=n;hr(e,!1),n?(n.el=u.el,W(e,n,l)):n=u,r&&A(r),(t=n.props&&n.props.onVnodeBeforeUpdate)&&Ar(t,c,n,u),hr(e,!0);const f=Yt(e),p=e.subTree;e.subTree=f,_(p,f,g(p.el),Q(p),e,o,s),n.el=f.el,null===a&&function({vnode:e,parent:t},n){for(;t&&t.subTree===e;)(e=t.vnode).el=n,t=t.parent}(e,f.el),i&&pr(i,o),(t=n.props&&n.props.onVnodeUpdated)&&pr((()=>Ar(t,c,n,u)),o)}else{let l;const{el:i,props:c}=t,{bm:u,m:a,parent:f}=e,p=fn(t);if(hr(e,!1),u&&A(u),!p&&(l=c&&c.onVnodeBeforeMount)&&Ar(l,f,t),hr(e,!0),i&&ne){const n=()=>{e.subTree=Yt(e),ne(i,e.subTree,e,o,null)};p?t.type.__asyncLoader().then((()=>!e.isUnmounted&&n())):n()}else{const l=e.subTree=Yt(e);_(null,l,n,r,e,o,s),t.el=l.el}if(a&&pr(a,o),!p&&(l=c&&c.onVnodeMounted)){const e=t;pr((()=>Ar(l,f,e)),o)}(256&t.shapeFlag||f&&fn(f.vnode)&&256&f.vnode.shapeFlag)&&e.a&&pr(e.a,o),e.isMounted=!0,t=n=r=null}},c=e.effect=new me(i,(()=>Bt(u)),e.scope),u=e.update=()=>c.run();u.id=e.uid,hr(e,!0),u()},W=(e,n,r)=>{n.component=e;const o=e.vnode.props;e.vnode=n,e.next=null,function(e,t,n,r){const{props:o,attrs:s,vnode:{patchFlag:l}}=e,i=_t(o),[c]=e.propsOptions;let u=!1;if(!(r||l>0)||16&l){let r;Yn(e,t,o,s)&&(u=!0);for(const s in i)t&&(f(t,s)||(r=R(s))!==s&&f(t,r))||(c?!n||void 0===n[s]&&void 0===n[r]||(o[s]=er(c,i,s,void 0,e,!0)):delete o[s]);if(s!==i)for(const e in s)t&&f(t,e)||(delete s[e],u=!0)}else if(8&l){const n=e.vnode.dynamicProps;for(let r=0;r<n.length;r++){let l=n[r];if(Jt(e.emitsOptions,l))continue;const a=t[l];if(c)if(f(s,l))a!==s[l]&&(s[l]=a,u=!0);else{const t=T(l);o[t]=er(c,i,t,a,e,!1)}else a!==s[l]&&(s[l]=a,u=!0)}}u&&Ce(e,"set","$attrs")}(e,n.props,o,r),((e,n,r)=>{const{vnode:o,slots:s}=e;let l=!0,i=t;if(32&o.shapeFlag){const e=n._;e?r&&1===e?l=!1:(c(s,n),r||1!==e||delete s._):(l=!n.$stable,ur(n,s)),i=n}else n&&(ar(e,n),i={default:1});if(l)for(const t in s)lr(t)||t in i||delete s[t]})(e,n.children,r),be(),Wt(),xe()},D=(e,t,n,r,o,s,l,i,c=!1)=>{const u=e&&e.children,a=e?e.shapeFlag:0,f=t.children,{patchFlag:p,shapeFlag:d}=t;if(p>0){if(128&p)return void z(u,f,n,r,o,s,l,i,c);if(256&p)return void H(u,f,n,r,o,s,l,i,c)}8&d?(16&a&&Z(u,o,s),f!==u&&h(n,f)):16&a?16&d?z(u,f,n,r,o,s,l,i,c):Z(u,o,s,!0):(8&a&&h(n,""),16&d&&F(f,n,r,o,s,l,i,c))},H=(e,t,r,o,s,l,i,c,u)=>{const a=(e=e||n).length,f=(t=t||n).length,p=Math.min(a,f);let d;for(d=0;d<p;d++){const n=t[d]=u?Fr(t[d]):Rr(t[d]);_(e[d],n,r,null,s,l,i,c,u)}a>f?Z(e,s,l,!0,!1,p):F(t,r,o,s,l,i,c,u,p)},z=(e,t,r,o,s,l,i,c,u)=>{let a=0;const f=t.length;let p=e.length-1,d=f-1;for(;a<=p&&a<=d;){const n=e[a],o=t[a]=u?Fr(t[a]):Rr(t[a]);if(!Sr(n,o))break;_(n,o,r,null,s,l,i,c,u),a++}for(;a<=p&&a<=d;){const n=e[p],o=t[d]=u?Fr(t[d]):Rr(t[d]);if(!Sr(n,o))break;_(n,o,r,null,s,l,i,c,u),p--,d--}if(a>p){if(a<=d){const e=d+1,n=e<f?t[e].el:o;for(;a<=d;)_(null,t[a]=u?Fr(t[a]):Rr(t[a]),r,n,s,l,i,c,u),a++}}else if(a>d)for(;a<=p;)G(e[a],s,l,!0),a++;else{const h=a,g=a,m=new Map;for(a=g;a<=d;a++){const e=t[a]=u?Fr(t[a]):Rr(t[a]);null!=e.key&&m.set(e.key,a)}let v,y=0;const b=d-g+1;let x=!1,w=0;const S=new Array(b);for(a=0;a<b;a++)S[a]=0;for(a=h;a<=p;a++){const n=e[a];if(y>=b){G(n,s,l,!0);continue}let o;if(null!=n.key)o=m.get(n.key);else for(v=g;v<=d;v++)if(0===S[v-g]&&Sr(n,t[v])){o=v;break}void 0===o?G(n,s,l,!0):(S[o-g]=a+1,o>=w?w=o:x=!0,_(n,t[o],r,null,s,l,i,c,u),y++)}const C=x?function(e){const t=e.slice(),n=[0];let r,o,s,l,i;const c=e.length;for(r=0;r<c;r++){const c=e[r];if(0!==c){if(o=n[n.length-1],e[o]<c){t[r]=o,n.push(r);continue}for(s=0,l=n.length-1;s<l;)i=s+l>>1,e[n[i]]<c?s=i+1:l=i;c<e[n[s]]&&(s>0&&(t[r]=n[s-1]),n[s]=r)}}s=n.length,l=n[s-1];for(;s-- >0;)n[s]=l,l=t[l];return n}(S):n;for(v=C.length-1,a=b-1;a>=0;a--){const e=g+a,n=t[e],p=e+1<f?t[e+1].el:o;0===S[a]?_(null,n,r,p,s,l,i,c,u):x&&(v<0||a!==C[v]?q(n,r,p,2):v--)}}},q=(e,t,n,r,o=null)=>{const{el:l,type:i,transition:c,children:u,shapeFlag:a}=e;if(6&a)return void q(e.component.subTree,t,n,r);if(128&a)return void e.suspense.move(t,n,r);if(64&a)return void i.move(e,t,n,ee);if(i===mr){s(l,t,n);for(let e=0;e<u.length;e++)q(u[e],t,n,r);return void s(e.anchor,t,n)}if(i===_r)return void S(e,t,n);if(2!==r&&1&a&&c)if(0===r)c.beforeEnter(l),s(l,t,n),pr((()=>c.enter(l)),o);else{const{leave:e,delayLeave:r,afterLeave:o}=c,i=()=>s(l,t,n),u=()=>{e(l,(()=>{i(),o&&o()}))};r?r(l,i,u):u()}else s(l,t,n)},G=(e,t,n,r=!1,o=!1)=>{const{type:s,props:l,ref:i,children:c,dynamicChildren:u,shapeFlag:a,patchFlag:f,dirs:p}=e;if(null!=i&&fr(i,null,n,e,!0),256&a)return void t.ctx.deactivate(e);const d=1&a&&p,h=!fn(e);let g;if(h&&(g=l&&l.onVnodeBeforeUnmount)&&Ar(g,t,e),6&a)X(e.component,n,r);else{if(128&a)return void e.suspense.unmount(n,r);d&&an(e,null,t,"beforeUnmount"),64&a?e.type.remove(e,t,n,o,ee,r):u&&(s!==mr||f>0&&64&f)?Z(u,t,n,!1,!0):(s===mr&&384&f||!o&&16&a)&&Z(c,t,n),r&&K(e)}(h&&(g=l&&l.onVnodeUnmounted)||d)&&pr((()=>{g&&Ar(g,t,e),d&&an(e,null,t,"unmounted")}),n)},K=e=>{const{type:t,el:n,anchor:r,transition:o}=e;if(t===mr)return void J(n,r);if(t===_r)return void C(e);const s=()=>{l(n),o&&!o.persisted&&o.afterLeave&&o.afterLeave()};if(1&e.shapeFlag&&o&&!o.persisted){const{leave:t,delayLeave:r}=o,l=()=>t(n,s);r?r(e.el,s,l):l()}else s()},J=(e,t)=>{let n;for(;e!==t;)n=m(e),l(e),e=n;l(t)},X=(e,t,n)=>{const{bum:r,scope:o,update:s,subTree:l,um:i}=e;r&&A(r),o.stop(),s&&(s.active=!1,G(l,e,t,n)),i&&pr(i,t),pr((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},Z=(e,t,n,r=!1,o=!1,s=0)=>{for(let l=s;l<e.length;l++)G(e[l],t,n,r,o)},Q=e=>6&e.shapeFlag?Q(e.component.subTree):128&e.shapeFlag?e.suspense.next():m(e.anchor||e.el),Y=(e,t,n)=>{null==e?t._vnode&&G(t._vnode,null,null,!0):_(t._vnode||null,e,t,null,null,null,n),Wt(),Dt(),t._vnode=e},ee={p:_,um:G,m:q,r:K,mt:U,mc:F,pc:D,pbc:M,n:Q,o:e};let te,ne;o&&([te,ne]=o(ee));return{render:Y,hydrate:te,createApp:Jn(Y,te)}}(e)}function hr({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function gr(e,t,n=!1){const r=e.children,o=t.children;if(p(r)&&p(o))for(let s=0;s<r.length;s++){const e=r[s];let t=o[s];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=o[s]=Fr(o[s]),t.el=e.el),n||gr(e,t)),t.type===vr&&(t.el=e.el)}}const mr=Symbol.for("v-fgt"),vr=Symbol.for("v-txt"),yr=Symbol.for("v-cmt"),_r=Symbol.for("v-stc");let br=1;function xr(e){br+=e}function wr(e){return!!e&&!0===e.__v_isVNode}function Sr(e,t){return e.type===t.type&&e.key===t.key}const Cr="__vInternal",kr=({key:e})=>null!=e?e:null,Or=({ref:e,ref_key:t,ref_for:n})=>("number"==typeof e&&(e=""+e),null!=e?v(e)||St(e)||m(e)?{i:Xt,r:e,k:t,f:!!n}:e:null);const Pr=function(e,t=null,n=null,r=0,o=null,s=!1){e&&e!==En||(e=yr);if(wr(e)){const r=Tr(e,t,!0);return n&&$r(r,n),r.patchFlag|=-2,r}l=e,m(l)&&"__vccOpts"in l&&(e=e.__vccOpts);var l;if(t){t=function(e){return e?yt(e)||Cr in e?c({},e):e:null}(t);let{class:e,style:n}=t;e&&!v(e)&&(t.class=H(e)),_(n)&&(yt(n)&&!p(n)&&(n=c({},n)),t.style=U(n))}const i=v(e)?1:(e=>e.__isSuspense)(e)?128:(e=>e.__isTeleport)(e)?64:_(e)?4:m(e)?2:0;return function(e,t=null,n=null,r=0,o=null,s=(e===mr?0:1),l=!1,i=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&kr(t),ref:t&&Or(t),scopeId:Zt,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:s,patchFlag:r,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:Xt};return i?($r(c,n),128&s&&e.normalize(c)):n&&(c.shapeFlag|=v(n)?8:16),c}(e,t,n,r,o,i,s,!0)};function Tr(e,t,n=!1){const{props:r,ref:o,patchFlag:s,children:l}=e,i=t?Mr(r||{},t):r;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:i,key:i&&kr(i),ref:t&&t.ref?n&&o?p(o)?o.concat(Or(t)):[o,Or(t)]:Or(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==mr?-1===s?16:16|s:s,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Tr(e.ssContent),ssFallback:e.ssFallback&&Tr(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function Er(e=" ",t=0){return Pr(vr,null,e,t)}function Rr(e){return null==e||"boolean"==typeof e?Pr(yr):p(e)?Pr(mr,null,e.slice()):"object"==typeof e?Fr(e):Pr(vr,null,String(e))}function Fr(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:Tr(e)}function $r(e,t){let n=0;const{shapeFlag:r}=e;if(null==t)t=null;else if(p(t))n=16;else if("object"==typeof t){if(65&r){const n=t.default;return void(n&&(n._c&&(n._d=!1),$r(e,n()),n._c&&(n._d=!0)))}{n=32;const r=t._;r||Cr in t?3===r&&Xt&&(1===Xt.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=Xt}}else m(t)?(t={default:t,_ctx:Xt},n=32):(t=String(t),64&r?(n=16,t=[Er(t)]):n=8);e.children=t,e.shapeFlag|=n}function Mr(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const e in r)if("class"===e)t.class!==r.class&&(t.class=H([t.class,r.class]));else if("style"===e)t.style=U([t.style,r.style]);else if(l(e)){const n=t[e],o=r[e];!o||n===o||p(n)&&n.includes(o)||(t[e]=n?[].concat(n,o):o)}else""!==e&&(t[e]=r[e])}return t}function Ar(e,t,n,r=null){Tt(e,t,7,[n,r])}const jr=Gn();let Ir=0;function Nr(e,n,r){const o=e.type,s=(n?n.appContext:e.appContext)||jr,l={uid:Ir++,vnode:e,type:o,parent:n,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,scope:new le(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:n?n.provides:Object.create(s.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:tr(o,s),emitsOptions:Kt(o,s),emit:null,emitted:null,propsDefaults:t,inheritAttrs:o.inheritAttrs,ctx:t,data:t,props:t,attrs:t,slots:t,refs:t,setupState:t,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:r,suspenseId:r?r.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return l.ctx={_:l},l.root=n?n.root:l,l.emit=Gt.bind(null,l),e.ce&&e.ce(l),l}let Vr,Ur,Br=null,Lr="__VUE_INSTANCE_SETTERS__";(Ur=V()[Lr])||(Ur=V()[Lr]=[]),Ur.push((e=>Br=e)),Vr=e=>{Ur.length>1?Ur.forEach((t=>t(e))):Ur[0](e)};const Wr=e=>{Vr(e),e.scope.on()},Dr=()=>{Br&&Br.scope.off(),Vr(null)};function Hr(e){return 4&e.vnode.shapeFlag}let zr=!1;function qr(e,t=!1){zr=t;const{props:n,children:r}=e.vnode,o=Hr(e);Qn(e,n,o,t),((e,t)=>{if(32&e.vnode.shapeFlag){const n=t._;n?(e.slots=_t(t),j(t,"_",n)):ur(t,e.slots={})}else e.slots={},t&&ar(e,t);j(e.slots,Cr,1)})(e,r);const s=o?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=bt(new Proxy(e.ctx,Mn));const{setup:r}=n;if(r){const n=e.setupContext=r.length>1?function(e){const t=t=>{e.exposed=t||{}};return{get attrs(){return function(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get:(t,n)=>(we(e,0,"$attrs"),t[n])}))}(e)},slots:e.slots,emit:e.emit,expose:t}}(e):null;Wr(e),be();const o=Pt(r,e,0,[e.props,n]);if(xe(),Dr(),b(o)){if(o.then(Dr,Dr),t)return o.then((n=>{Gr(e,n,t)})).catch((t=>{Et(t,e,0)}));e.asyncDep=o}else Gr(e,o,t)}else Kr(e,t)}(e,t):void 0;return zr=!1,s}function Gr(e,t,n){m(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:_(t)&&(e.setupState=kt(t)),Kr(e,n)}function Kr(e,t,n){const o=e.type;e.render||(e.render=o.render||r),Wr(e),be(),In(e),xe(),Dr()}function Jr(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(kt(bt(e.exposed)),{get:(t,n)=>n in t?t[n]:n in Fn?Fn[n](e):void 0,has:(e,t)=>t in e||t in Fn}))}const Xr=(e,t)=>function(e,t,n=!1){let o,s;const l=m(e);return l?(o=e,s=r):(o=e.get,s=e.set),new Ot(o,s,l||!s,n)}(e,0,zr),Zr=Symbol.for("v-scx"),Qr=()=>Zn(Zr),Yr="3.3.4",eo={createComponentInstance:Nr,setupComponent:qr,renderComponentRoot:Yt,setCurrentRenderingInstance:Qt,isVNode:wr,normalizeVNode:Rr},to="undefined"!=typeof document?document:null,no=to&&to.createElement("template"),ro={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const o=t?to.createElementNS("http://www.w3.org/2000/svg",e):to.createElement(e,n?{is:n}:void 0);return"select"===e&&r&&null!=r.multiple&&o.setAttribute("multiple",r.multiple),o},createText:e=>to.createTextNode(e),createComment:e=>to.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>to.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,o,s){const l=n?n.previousSibling:t.lastChild;if(o&&(o===s||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),n),o!==s&&(o=o.nextSibling););else{no.innerHTML=r?`<svg>${e}</svg>`:e;const o=no.content;if(r){const e=o.firstChild;for(;e.firstChild;)o.appendChild(e.firstChild);o.removeChild(e)}t.insertBefore(o,n)}return[l?l.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}};const oo=/\s*!important$/;function so(e,t,n){if(p(n))n.forEach((n=>so(e,t,n)));else if(null==n&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=function(e,t){const n=io[t];if(n)return n;let r=T(t);if("filter"!==r&&r in e)return io[t]=r;r=F(r);for(let o=0;o<lo.length;o++){const n=lo[o]+r;if(n in e)return io[t]=n}return t}(e,t);oo.test(n)?e.setProperty(R(r),n.replace(oo,""),"important"):e[r]=n}}const lo=["Webkit","Moz","ms"],io={};const co="http://www.w3.org/1999/xlink";function uo(e,t,n,r){e.addEventListener(t,n,r)}function ao(e,t,n,r,o=null){const s=e._vei||(e._vei={}),l=s[t];if(r&&l)l.value=r;else{const[n,i]=function(e){let t;if(fo.test(e)){let n;for(t={};n=e.match(fo);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}const n=":"===e[2]?e.slice(3):R(e.slice(2));return[n,t]}(t);if(r){const l=s[t]=function(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();Tt(function(e,t){if(p(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>t=>!t._stopped&&e&&e(t)))}return t}(e,n.value),t,5,[e])};return n.value=e,n.attached=(()=>po||(ho.then((()=>po=0)),po=Date.now()))(),n}(r,o);uo(e,n,l,i)}else l&&(!function(e,t,n,r){e.removeEventListener(t,n,r)}(e,n,l,i),s[t]=void 0)}}const fo=/(?:Once|Passive|Capture)$/;let po=0;const ho=Promise.resolve();const go=/^on[a-z]/;const mo=e=>{const t=e.props["onUpdate:modelValue"]||!1;return p(t)?e=>A(t,e):t};function vo(e){e.target.composing=!0}function yo(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const _o={created(e,{modifiers:{lazy:t,trim:n,number:r}},o){e._assign=mo(o);const s=r||o.props&&"number"===o.props.type;uo(e,t?"change":"input",(t=>{if(t.target.composing)return;let r=e.value;n&&(r=r.trim()),s&&(r=I(r)),e._assign(r)})),n&&uo(e,"change",(()=>{e.value=e.value.trim()})),t||(uo(e,"compositionstart",vo),uo(e,"compositionend",yo),uo(e,"change",yo))},mounted(e,{value:t}){e.value=null==t?"":t},beforeUpdate(e,{value:t,modifiers:{lazy:n,trim:r,number:o}},s){if(e._assign=mo(s),e.composing)return;if(document.activeElement===e&&"range"!==e.type){if(n)return;if(r&&e.value.trim()===t)return;if((o||"number"===e.type)&&I(e.value)===t)return}const l=null==t?"":t;e.value!==l&&(e.value=l)}};const bo={beforeMount(e,{value:t},{transition:n}){e._vod="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):xo(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:r}){!t!=!n&&(r?t?(r.beforeEnter(e),xo(e,!0),r.enter(e)):r.leave(e,(()=>{xo(e,!1)})):xo(e,t))},beforeUnmount(e,{value:t}){xo(e,t)}};function xo(e,t){e.style.display=t?e._vod:"none"}const wo=c({patchProp:(e,t,n,r,o=!1,s,c,u,a)=>{"class"===t?function(e,t,n){const r=e._vtc;r&&(t=(t?[t,...r]:[...r]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,r,o):"style"===t?function(e,t,n){const r=e.style,o=v(n);if(n&&!o){if(t&&!v(t))for(const e in t)null==n[e]&&so(r,e,"");for(const e in n)so(r,e,n[e])}else{const s=r.display;o?t!==n&&(r.cssText=n):t&&e.removeAttribute("style"),"_vod"in e&&(r.display=s)}}(e,n,r):l(t)?i(t)||ao(e,t,0,r,c):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,r){if(r)return"innerHTML"===t||"textContent"===t||!!(t in e&&go.test(t)&&m(n));if("spellcheck"===t||"draggable"===t||"translate"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if(go.test(t)&&v(n))return!1;return t in e}(e,t,r,o))?function(e,t,n,r,o,s,l){if("innerHTML"===t||"textContent"===t)return r&&l(r,o,s),void(e[t]=null==n?"":n);const i=e.tagName;if("value"===t&&"PROGRESS"!==i&&!i.includes("-")){e._value=n;const r=null==n?"":n;return("OPTION"===i?e.getAttribute("value"):e.value)!==r&&(e.value=r),void(null==n&&e.removeAttribute(t))}let c=!1;if(""===n||null==n){const r=typeof e[t];"boolean"===r?n=X(n):null==n&&"string"===r?(n="",c=!0):"number"===r&&(n=0,c=!0)}try{e[t]=n}catch(u){}c&&e.removeAttribute(t)}(e,t,r,s,c,u,a):("true-value"===t?e._trueValue=r:"false-value"===t&&(e._falseValue=r),function(e,t,n,r,o){if(r&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(co,t.slice(6,t.length)):e.setAttributeNS(co,t,n);else{const r=K(t);null==n||r&&!X(n)?e.removeAttribute(t):e.setAttribute(t,r?"":n)}}(e,t,r,o))}},ro);let So;const Co=(...e)=>{const t=(So||(So=dr(wo))).createApp(...e),{mount:n}=t;return t.mount=e=>{const r=function(e){if(v(e)){return document.querySelector(e)}return e}(e);if(!r)return;const o=t._component;m(o)||o.render||o.template||(o.template=r.innerHTML),r.innerHTML="";const s=n(r,!1,r instanceof SVGElement);return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),s},t};let ko=!1;const Oo=e(",key,ref,innerHTML,textContent,ref_key,ref_for");function Po(e,t){let n="";for(const r in e){if(Oo(r)||l(r)||"textarea"===t&&"value"===r)continue;const o=e[r];n+="class"===r?` class="${Fo(o)}"`:"style"===r?` style="${$o(o)}"`:To(r,o,t)}return n}function To(e,t,n){if(!Ro(t))return"";const r=n&&(n.indexOf("-")>0||z(n))?e:Y[e]||e.toLowerCase();return J(r)?X(t)?` ${r}`:"":function(e){if(Q.hasOwnProperty(e))return Q[e];const t=Z.test(e);return t&&console.error(`unsafe attribute name: ${e}`),Q[e]=!t}(r)?""===t?` ${r}`:` ${r}="${te(t)}"`:(console.warn(`[@vue/server-renderer] Skipped rendering unsafe attribute name: ${r}`),"")}function Eo(e,t){return Ro(t)?` ${e}="${te(t)}"`:""}function Ro(e){if(null==e)return!1;const t=typeof e;return"string"===t||"number"===t||"boolean"===t}function Fo(e){return te(H(e))}function $o(e){if(!e)return"";if(v(e))return te(e);return te(function(e){let t="";if(!e||v(e))return t;for(const n in e){const r=e[n],o=n.startsWith("--")?n:R(n);(v(r)||"number"==typeof r)&&(t+=`${o}:${r};`)}return t}(U(e)))}function Mo(e,t=null,n=null,r=null,o){return es(Pr(e,t,n),r,o)}function Ao(e,t,n,r,o,s,l){o("\x3c!--[--\x3e"),jo(e,t,n,r,o,s,l),o("\x3c!--]--\x3e")}function jo(e,t,n,r,o,s,l,i){const c=e[t];if(c){const e=[],t=c(n,(t=>{e.push(t)}),s,l?" "+l:"");if(p(t))rs(o,t,s,l);else{let t=!0;if(i)t=!1;else for(let n=0;n<e.length;n++)if(!Vo(e[n])){t=!1;break}if(t)r&&r();else for(let n=0;n<e.length;n++)o(e[n])}}else r&&r()}const Io=/^<!--.*-->$/s,No=/<!--[^]*?-->/gm;function Vo(e){return!("string"!=typeof e||!Io.test(e))&&(e.length<=8||!e.replace(No,"").trim())}function Uo(e,t,n,r,o){e("\x3c!--teleport start--\x3e");const s=o.appContext.provides[Zr],l=s.__teleportBuffers||(s.__teleportBuffers={}),i=l[n]||(l[n]=[]),c=i.length;let u;if(r)t(e),u="\x3c!--teleport anchor--\x3e";else{const{getBuffer:e,push:n}=Yo();t(n),n("\x3c!--teleport anchor--\x3e"),u=e()}i.splice(c,0,u),e("\x3c!--teleport end--\x3e")}function Bo(e){return te(v(t=e)?t:null==t?"":p(t)||_(t)&&(t.toString===x||!m(t.toString))?JSON.stringify(t,oe,2):String(t));var t}function Lo(e,t){if(p(e)||v(e))for(let n=0,r=e.length;n<r;n++)t(e[n],n);else if("number"==typeof e)for(let n=0;n<e;n++)t(n+1,n);else if(_(e))if(e[Symbol.iterator]){const n=Array.from(e);for(let e=0,r=n.length;e<r;e++)t(n[e],e)}else{const n=Object.keys(e);for(let r=0,o=n.length;r<o;r++){const o=n[r];t(e[o],o,r)}}}async function Wo(e,{default:t}){t?t():e("\x3c!----\x3e")}function Do(e,t,n,r,o={}){return"function"!=typeof t&&t.getSSRProps&&t.getSSRProps({dir:t,instance:e,value:n,oldValue:void 0,arg:r,modifiers:o},null)||{}}const Ho=re;function zo(e,t){return function(e,t){return e.findIndex((e=>re(e,t)))}(e,t)>-1}function qo(e,t,n){switch(e){case"radio":return re(t,n)?" checked":"";case"checkbox":return(p(t)?zo(t,n):t)?" checked":"";default:return Eo("value",t)}}function Go(e={},t){const{type:n,value:r}=e;switch(n){case"radio":return re(t,r)?{checked:!0}:null;case"checkbox":return(p(t)?zo(t,r):t)?{checked:!0}:null;default:return{value:t}}}const{createComponentInstance:Ko,setCurrentRenderingInstance:Jo,setupComponent:Xo,renderComponentRoot:Zo,normalizeVNode:Qo}=eo;function Yo(){let e=!1;const t=[];return{getBuffer:()=>t,push(n){const r=v(n);e&&r?t[t.length-1]+=n:t.push(n),e=r,(b(n)||p(n)&&n.hasAsync)&&(t.hasAsync=!0)}}}function es(e,t=null,n){const r=Ko(e,t,null),o=Xo(r,!0),s=b(o),l=r.sp;if(s||l){let e=s?o:Promise.resolve();return l&&(e=e.then((()=>Promise.all(l.map((e=>e.call(r.proxy)))))).catch((()=>{}))),e.then((()=>ts(r,n)))}return ts(r,n)}function ts(e,t){const n=e.type,{getBuffer:o,push:s}=Yo();if(m(n)){let r=Zo(e);if(!n.props)for(const t in e.attrs)t.startsWith("data-v-")&&((r.props||(r.props={}))[t]="");ns(s,e.subTree=r,e,t)}else{e.render&&e.render!==r||e.ssrRender||n.ssrRender||!v(n.template)||(n.ssrRender=function(e,t){throw new Error("On-the-fly template compilation is not supported in the ESM build of @vue/server-renderer. All templates must be pre-compiled into render functions.")}());for(const t of e.scope.effects)t.computed&&(t.computed._cacheable=!0);const o=e.ssrRender||n.ssrRender;if(o){let n=!1!==e.inheritAttrs?e.attrs:void 0,r=!1,l=e;for(;;){const e=l.vnode.scopeId;e&&(r||(n={...n},r=!0),n[e]="");const t=l.parent;if(!t||!t.subTree||t.subTree!==l.vnode)break;l=t}t&&(r||(n={...n}),n[t.trim()]="");const i=Jo(e);try{o(e.proxy,s,e,n,e.props,e.setupState,e.data,e.ctx)}finally{Jo(i)}}else e.render&&e.render!==r?ns(s,e.subTree=Zo(e),e,t):s("\x3c!----\x3e")}return o()}function ns(e,t,n,r){const{type:o,shapeFlag:s,children:l}=t;switch(o){case vr:e(te(l));break;case yr:e(l?`\x3c!--${i=l,i.replace(ne,"")}--\x3e`:"\x3c!----\x3e");break;case _r:e(l);break;case mr:t.slotScopeIds&&(r=(r?r+" ":"")+t.slotScopeIds.join(" ")),e("\x3c!--[--\x3e"),rs(e,l,n,r),e("\x3c!--]--\x3e");break;default:1&s?function(e,t,n,r){const o=t.type;let{props:s,children:l,shapeFlag:i,scopeId:c,dirs:u}=t,a=`<${o}`;u&&(s=function(e,t,n){const r=[];for(let o=0;o<n.length;o++){const t=n[o],{dir:{getSSRProps:s}}=t;if(s){const n=s(t,e);n&&r.push(n)}}return Mr(t||{},...r)}(t,s,u));s&&(a+=Po(s,o));c&&(a+=` ${c}`);let f=n,p=t;for(;f&&p===f.subTree;)p=f.vnode,p.scopeId&&(a+=` ${p.scopeId}`),f=f.parent;r&&(a+=` ${r}`);if(e(a+">"),!q(o)){let t=!1;s&&(s.innerHTML?(t=!0,e(s.innerHTML)):s.textContent?(t=!0,e(te(s.textContent))):"textarea"===o&&s.value&&(t=!0,e(te(s.value)))),t||(8&i?e(te(l)):16&i&&rs(e,l,n,r)),e(`</${o}>`)}}(e,t,n,r):6&s?e(es(t,n,r)):64&s?function(e,t,n,r){const o=t.props&&t.props.to,s=t.props&&t.props.disabled;if(!o)return[];if(!v(o))return[];Uo(e,(e=>{rs(e,t.children,n,r)}),o,s||""===s,n)}(e,t,n,r):128&s&&ns(e,t.ssContent,n,r)}var i}function rs(e,t,n,r){for(let o=0;o<t.length;o++)ns(e,Qo(t[o]),n,r)}const{isVNode:os}=eo;async function ss(e){if(e.hasAsync){let t="";for(let n=0;n<e.length;n++){let r=e[n];b(r)&&(r=await r),v(r)?t+=r:t+=await ss(r)}return t}return ls(e)}function ls(e){let t="";for(let n=0;n<e.length;n++){let r=e[n];v(r)?t+=r:t+=ls(r)}return t}async function is(e,t={}){if(os(e))return is(Co({render:()=>e}),t);const n=Pr(e._component,e._props);n.appContext=e._context,e.provide(Zr,t);const r=await es(n),o=await ss(r);if(await cs(t),t.__watcherHandles)for(const s of t.__watcherHandles)s();return o}async function cs(e){if(e.__teleportBuffers){e.teleports=e.teleports||{};for(const t in e.__teleportBuffers)e.teleports[t]=await ss(await Promise.all([e.__teleportBuffers[t]]))}}const{isVNode:us}=eo;async function as(e,t){if(e.hasAsync)for(let n=0;n<e.length;n++){let r=e[n];b(r)&&(r=await r),v(r)?t.push(r):await as(r,t)}else fs(e,t)}function fs(e,t){for(let n=0;n<e.length;n++){let r=e[n];v(r)?t.push(r):fs(r,t)}}function ps(e,t,n){if(us(e))return ps(Co({render:()=>e}),t,n);const r=Pr(e._component,e._props);return r.appContext=e._context,e.provide(Zr,t),Promise.resolve(es(r)).then((e=>as(e,n))).then((()=>cs(t))).then((()=>{if(t.__watcherHandles)for(const e of t.__watcherHandles)e()})).then((()=>n.push(null))).catch((e=>{n.destroy(e)})),n}function ds(e,t={}){return console.warn("[@vue/server-renderer] renderToStream is deprecated - use renderToNodeStream instead."),hs(e,t)}function hs(e,t={}){throw new Error("ESM build of renderToStream() does not support renderToNodeStream(). Use pipeToNodeWritable() with an existing Node.js Writable stream instance instead.")}function gs(e,t={},n){ps(e,t,{push(e){null!=e?n.write(e):n.end()},destroy(e){n.destroy(e)}})}function ms(e,t={}){if("function"!=typeof ReadableStream)throw new Error("ReadableStream constructor is not available in the global scope. If the target environment does support web streams, consider using pipeToWebWritable() with an existing WritableStream instance instead.");const n=new TextEncoder;let r=!1;return new ReadableStream({start(o){ps(e,t,{push(e){r||(null!=e?o.enqueue(n.encode(e)):o.close())},destroy(e){o.error(e)}})},cancel(){r=!0}})}function vs(e,t={},n){const r=n.getWriter(),o=new TextEncoder;let s=!1;try{s=b(r.ready)}catch(l){}ps(e,t,{push:async e=>(s&&await r.ready,null!=e?r.write(o.encode(e)):r.close()),destroy(e){console.log(e),r.close()}})}ko||(ko=!0,_o.getSSRProps=({value:e})=>({value:e}),bo.getSSRProps=({value:e})=>{if(!e)return{style:{display:"none"}}});export{gs as pipeToNodeWritable,vs as pipeToWebWritable,hs as renderToNodeStream,ps as renderToSimpleStream,ds as renderToStream,is as renderToString,ms as renderToWebStream,Do as ssrGetDirectiveProps,Go as ssrGetDynamicModelProps,X as ssrIncludeBooleanAttr,Bo as ssrInterpolate,zo as ssrLooseContain,Ho as ssrLooseEqual,Eo as ssrRenderAttr,Po as ssrRenderAttrs,Fo as ssrRenderClass,Mo as ssrRenderComponent,To as ssrRenderDynamicAttr,qo as ssrRenderDynamicModel,Lo as ssrRenderList,Ao as ssrRenderSlot,jo as ssrRenderSlotInner,$o as ssrRenderStyle,Wo as ssrRenderSuspense,Uo as ssrRenderTeleport,ns as ssrRenderVNode};
