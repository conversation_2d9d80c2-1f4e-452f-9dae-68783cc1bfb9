{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/EventRunner.ts"], "names": ["EventActionRunner", "EventGroupRunner", "eEmitterActionType", "eBulletActionType", "eConditionGroupOp", "eEventConditionOp", "eEmitterStatus", "eEasing", "BulletSystem", "constructor", "owner", "data", "startValue", "targetValue", "elapsedTime", "_isCompleted", "isCompleted", "getOwnerAsEmitter", "getOwnerAsBullet", "canLerp", "actionType", "lerpableEmitterActions", "Emitter_InitialDelay", "Emitter_PrewarmDuration", "Emitter_Duration", "Emitter_ElapsedTime", "Emitter_LoopInterval", "Emitter_PerEmitInterval", "Emitter_PerEmitCount", "Emitter_PerEmitOffsetX", "Emitter_Angle", "Emitter_Count", "lerpableBulletActions", "Bullet_Duration", "Bullet_ElapsedTime", "Bullet_PosX", "Bullet_PosY", "Bullet_Damage", "Bullet_Speed", "Bullet_SpeedAngle", "Bullet_Acceleration", "Bullet_AccelerationAngle", "Bullet_Scale", "Bullet_ColorR", "Bullet_ColorG", "Bullet_ColorB", "Bullet_ColorA", "indexOf", "getInitialValue", "emitter", "Emitter_Active", "status", "None", "initialDelay", "emitDuration", "perEmitInterval", "angle", "bullet", "duration", "mover", "speed", "node", "getPosition", "x", "y", "bulletSprite", "color", "a", "error", "console", "warn", "getTargetValue", "isRandom", "Math", "random", "maxValue", "minValue", "boolValue", "start", "tick", "dt", "apply", "currentValue", "lerp<PERSON><PERSON>ue", "progress", "min", "easedProgress", "applyEasing", "easing", "applyAction", "t", "Linear", "InSine", "cos", "PI", "OutSine", "sin", "InOutSine", "InQuad", "OutQuad", "InOutQuad", "pow", "isTriggered", "isConditionAMet", "isConditionBMet", "init", "registerListeners", "tryTrigger", "groupOp", "And", "Or", "action", "actions", "createActionRunner", "unRegisterListeners", "conditionA", "on", "eventType", "onConditionAChanged", "bind", "conditionB", "onConditionBChanged", "off", "value", "compareValue", "eventOp", "op", "b", "Equal", "NotEqual", "Greater", "Less", "GreaterEqual", "LessEqual"], "mappings": ";;;mMAaaA,iB,EA8LAC,gB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA1MJC,MAAAA,kB,iBAAAA,kB;AAAoBC,MAAAA,iB,iBAAAA,iB;;AACpBC,MAAAA,iB,iBAAAA,iB;;AACAC,MAAAA,iB,iBAAAA,iB;;AAEAC,MAAAA,c,iBAAAA,c;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Y,iBAAAA,Y;;;;;;;;;AAET;AACA;AACA;AACA;mCACaR,iB,GAAN,MAAMA,iBAAN,CAAwB;AAS3BS,QAAAA,WAAW,CAACC,KAAD,EAA0BC,IAA1B,EAAsE;AAAA,eARzEC,UAQyE,GARpD,CAQoD;AAAA,eAPzEC,WAOyE,GAPnD,CAOmD;AAAA,eANzEC,WAMyE,GANnD,CAMmD;AAAA,eALzEC,YAKyE,GALjD,KAKiD;AAAA,eAHxEJ,IAGwE;AAAA,eAFjFD,KAEiF,GAFvD,IAEuD;AAC7E,eAAKA,KAAL,GAAaA,KAAb;AACA,eAAKC,IAAL,GAAYA,IAAZ;AACH;;AAEc,YAAXK,WAAW,GAAY;AACvB,iBAAO,KAAKD,YAAZ;AACH;;AAEOE,QAAAA,iBAAiB,GAAY;AACjC,iBAAO,KAAKP,KAAZ;AACH;;AAEOQ,QAAAA,gBAAgB,GAAW;AAC/B,iBAAO,KAAKR,KAAZ;AACH;;AAEqB,eAAPS,OAAO,CAACC,UAAD,EAA8B;AAChD;AACA,cAAMC,sBAAsB,GAAG,CAC3B;AAAA;AAAA,wDAAmBC,oBADQ,EAE3B;AAAA;AAAA,wDAAmBC,uBAFQ,EAG3B;AAAA;AAAA,wDAAmBC,gBAHQ,EAI3B;AAAA;AAAA,wDAAmBC,mBAJQ,EAK3B;AAAA;AAAA,wDAAmBC,oBALQ,EAM3B;AAAA;AAAA,wDAAmBC,uBANQ,EAO3B;AAAA;AAAA,wDAAmBC,oBAPQ,EAQ3B;AAAA;AAAA,wDAAmBC,sBARQ,EAS3B;AAAA;AAAA,wDAAmBC,aATQ,EAU3B;AAAA;AAAA,wDAAmBC,aAVQ,CAA/B;AAaA,cAAMC,qBAAqB,GAAG,CAC1B;AAAA;AAAA,sDAAkBC,eADQ,EAE1B;AAAA;AAAA,sDAAkBC,kBAFQ,EAG1B;AAAA;AAAA,sDAAkBC,WAHQ,EAI1B;AAAA;AAAA,sDAAkBC,WAJQ,EAK1B;AAAA;AAAA,sDAAkBC,aALQ,EAM1B;AAAA;AAAA,sDAAkBC,YANQ,EAO1B;AAAA;AAAA,sDAAkBC,iBAPQ,EAQ1B;AAAA;AAAA,sDAAkBC,mBARQ,EAS1B;AAAA;AAAA,sDAAkBC,wBATQ,EAU1B;AAAA;AAAA,sDAAkBC,YAVQ,EAW1B;AAAA;AAAA,sDAAkBC,aAXQ,EAY1B;AAAA;AAAA,sDAAkBC,aAZQ,EAa1B;AAAA;AAAA,sDAAkBC,aAbQ,EAc1B;AAAA;AAAA,sDAAkBC,aAdQ,CAA9B;AAiBA,iBAAOzB,sBAAsB,CAAC0B,OAAvB,CAA+B3B,UAA/B,MAA+C,CAAC,CAAhD,IAAqDY,qBAAqB,CAACe,OAAtB,CAA8B3B,UAA9B,MAA8C,CAAC,CAA3G;AACH;;AAEO4B,QAAAA,eAAe,GAAW;AAC9B;AACA;AACA,cAAI;AACA,gBAAI,KAAKrC,IAAL,CAAUS,UAAV,GAAuB;AAAA;AAAA,wDAAkBa,eAA7C,EAA8D;AAC1D;AACA,kBAAMgB,OAAO,GAAG,KAAKhC,iBAAL,EAAhB;;AACA,sBAAQ,KAAKN,IAAL,CAAUS,UAAlB;AACI,qBAAK;AAAA;AAAA,8DAAmB8B,cAAxB;AACI,yBAAOD,OAAO,CAACE,MAAR,IAAkB;AAAA;AAAA,wDAAeC,IAAjC,GAAwC,CAAxC,GAA4C,CAAnD;;AACJ,qBAAK;AAAA;AAAA,8DAAmB9B,oBAAxB;AACI,yBAAO2B,OAAO,CAACI,YAAf;;AACJ,qBAAK;AAAA;AAAA,8DAAmB7B,gBAAxB;AACI,yBAAOyB,OAAO,CAACK,YAAf;;AACJ,qBAAK;AAAA;AAAA,8DAAmB3B,uBAAxB;AACI,yBAAOsB,OAAO,CAACM,eAAf;;AACJ,qBAAK;AAAA;AAAA,8DAAmBzB,aAAxB;AACI,yBAAOmB,OAAO,CAACO,KAAf;AACJ;;AACA;AACI,yBAAO,CAAP;AAbR;AAeH,aAlBD,MAkBO;AACH;AACA,kBAAMC,MAAM,GAAG,KAAKvC,gBAAL,EAAf;;AACA,sBAAQ,KAAKP,IAAL,CAAUS,UAAlB;AACI,qBAAK;AAAA;AAAA,4DAAkBa,eAAvB;AACI,yBAAOwB,MAAM,CAACC,QAAd;;AACJ,qBAAK;AAAA;AAAA,4DAAkBpB,YAAvB;AACI,yBAAOmB,MAAM,CAACE,KAAP,CAAaC,KAApB;;AACJ,qBAAK;AAAA;AAAA,4DAAkBzB,WAAvB;AACI,yBAAOsB,MAAM,CAACI,IAAP,CAAYC,WAAZ,GAA0BC,CAAjC;;AACJ,qBAAK;AAAA;AAAA,4DAAkB3B,WAAvB;AACI,yBAAOqB,MAAM,CAACI,IAAP,CAAYC,WAAZ,GAA0BE,CAAjC;;AACJ,qBAAK;AAAA;AAAA,4DAAkBlB,aAAvB;AACI,yBAAOW,MAAM,CAACQ,YAAP,CAAoBC,KAApB,CAA0BC,CAAjC;AACJ;;AACA;AACI,yBAAO,CAAP;AAbR;AAeH;AACJ,WAtCD,CAsCE,OAAOC,KAAP,EAAc;AACZC,YAAAA,OAAO,CAACC,IAAR,CAAa,gEAAb,EAA+E,KAAK3D,IAAL,CAAUS,UAAzF,EAAqGgD,KAArG;AACA,mBAAO,CAAP;AACH;AACJ;;AAEOG,QAAAA,cAAc,GAAW;AAC7B,cAAIvE,iBAAiB,CAACmB,OAAlB,CAA0B,KAAKR,IAAL,CAAUS,UAApC,CAAJ,EAAqD;AACjD,gBAAI,KAAKT,IAAL,CAAU6D,QAAd,EAAwB;AACpB,qBAAOC,IAAI,CAACC,MAAL,MAAiB,KAAK/D,IAAL,CAAUgE,QAAV,GAAqB,KAAKhE,IAAL,CAAUiE,QAAhD,IAA4D,KAAKjE,IAAL,CAAUiE,QAA7E;AACH;;AACD,mBAAO,KAAKjE,IAAL,CAAUiE,QAAjB;AACH;;AACD,iBAAO,KAAKjE,IAAL,CAAUkE,SAAV,GAAsB,CAAtB,GAA0B,CAAjC;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ,cAAI,CAAC,KAAKnE,IAAV,EAAgB;AACZ0D,YAAAA,OAAO,CAACD,KAAR,CAAc,iCAAd;AACA;AACH;;AAED,eAAKxD,UAAL,GAAkB,KAAKoC,eAAL,EAAlB;AACA,eAAKnC,WAAL,GAAmB,KAAK0D,cAAL,EAAnB;AACA,eAAKzD,WAAL,GAAmB,CAAnB;AACA,eAAKC,YAAL,GAAoB,KAApB;AACH;;AAEDgE,QAAAA,IAAI,CAACC,EAAD,EAAa;AACb,eAAKlE,WAAL,IAAoBkE,EAApB;;AAEA,cAAI,KAAKlE,WAAL,IAAoB,KAAKH,IAAL,CAAU+C,QAAlC,EAA4C;AACxC,iBAAK3C,YAAL,GAAoB,IAApB;AACA,iBAAKkE,KAAL,CAAW,KAAKpE,WAAhB;AACH,WAHD,MAGO,IAAIb,iBAAiB,CAACmB,OAAlB,CAA0B,KAAKR,IAAL,CAAUS,UAApC,CAAJ,EAAqD;AACxD,gBAAM8D,YAAY,GAAG,KAAKC,SAAL,CAAe,KAAKvE,UAApB,EAAgC,KAAKC,WAArC,CAArB;AACA,iBAAKoE,KAAL,CAAWC,YAAX;AACH;AACJ;;AAEOC,QAAAA,SAAS,CAACvE,UAAD,EAAqBC,WAArB,EAAkD;AAC/D,cAAMuE,QAAQ,GAAGX,IAAI,CAACY,GAAL,CAAS,GAAT,EAAc,KAAKvE,WAAL,GAAmB,KAAKH,IAAL,CAAU+C,QAA3C,CAAjB;AACA,cAAM4B,aAAa,GAAGtF,iBAAiB,CAACuF,WAAlB,CAA8B,KAAK5E,IAAL,CAAU6E,MAAxC,EAAgDJ,QAAhD,CAAtB;AACA,iBAAOxE,UAAU,GAAG,CAACC,WAAW,GAAGD,UAAf,IAA6B0E,aAAjD;AACH;;AAEOL,QAAAA,KAAK,CAACC,YAAD,EAAuB;AAChC,cAAI;AACA,gBAAI,KAAKvE,IAAL,CAAUS,UAAV,GAAuB;AAAA;AAAA,wDAAkBa,eAA7C,EAA8D;AAC1D,mBAAKhB,iBAAL,GAAyBwE,WAAzB,CAAqC,KAAK9E,IAAL,CAAUS,UAA/C,EAAiF8D,YAAjF;AACH,aAFD,MAEO;AACH,mBAAKhE,gBAAL,GAAwBuE,WAAxB,CAAoC,KAAK9E,IAAL,CAAUS,UAA9C,EAA+E8D,YAA/E;AACH;AACJ,WAND,CAME,OAAOd,KAAP,EAAc;AACZC,YAAAA,OAAO,CAACC,IAAR,CAAa,2CAAb,EAA0D,KAAK3D,IAAL,CAAUS,UAApE,EAAgF8D,YAAhF,EAA8Fd,KAA9F;AACA,iBAAKrD,YAAL,GAAoB,IAApB,CAFY,CAEc;AAC7B;AACJ;;AAEiB,eAAXwE,WAAW,CAACC,MAAD,EAAkBE,CAAlB,EAAqC;AACnD,kBAAQF,MAAR;AACI,iBAAK;AAAA;AAAA,oCAAQG,MAAb;AACI,qBAAOD,CAAP;;AAEJ,iBAAK;AAAA;AAAA,oCAAQE,MAAb;AACI,qBAAO,IAAInB,IAAI,CAACoB,GAAL,CAASH,CAAC,GAAGjB,IAAI,CAACqB,EAAT,GAAc,CAAvB,CAAX;;AAEJ,iBAAK;AAAA;AAAA,oCAAQC,OAAb;AACI,qBAAOtB,IAAI,CAACuB,GAAL,CAASN,CAAC,GAAGjB,IAAI,CAACqB,EAAT,GAAc,CAAvB,CAAP;;AAEJ,iBAAK;AAAA;AAAA,oCAAQG,SAAb;AACI,qBAAO,EAAExB,IAAI,CAACoB,GAAL,CAASpB,IAAI,CAACqB,EAAL,GAAUJ,CAAnB,IAAwB,CAA1B,IAA+B,CAAtC;;AAEJ,iBAAK;AAAA;AAAA,oCAAQQ,MAAb;AACI,qBAAOR,CAAC,GAAGA,CAAX;;AAEJ,iBAAK;AAAA;AAAA,oCAAQS,OAAb;AACI,qBAAO,IAAI,CAAC,IAAIT,CAAL,KAAW,IAAIA,CAAf,CAAX;;AAEJ,iBAAK;AAAA;AAAA,oCAAQU,SAAb;AACI,qBAAOV,CAAC,GAAG,GAAJ,GAAU,IAAIA,CAAJ,GAAQA,CAAlB,GAAsB,IAAIjB,IAAI,CAAC4B,GAAL,CAAS,CAAC,CAAD,GAAKX,CAAL,GAAS,CAAlB,EAAqB,CAArB,IAA0B,CAA3D;;AAEJ;AACI,qBAAOA,CAAP;AAvBR;AAyBH;;AA3L0B,O;;kCA8LlBzF,gB,GAAN,MAAMA,gBAAN,CAAuB;AAO1BQ,QAAAA,WAAW,CAACE,IAAD,EAA2C;AAAA,eAN7CA,IAM6C;AAAA,eALtDD,KAKsD,GAL5B,IAK4B;AAAA,eAJtD4F,WAIsD,GAJ/B,KAI+B;AAAA,eAHtDC,eAGsD,GAH3B,KAG2B;AAAA,eAFtDC,eAEsD,GAF3B,KAE2B;AAClD,eAAK7F,IAAL,GAAYA,IAAZ;AACH;;AAEM8F,QAAAA,IAAI,CAAC/F,KAAD,EAA0B;AACjC,eAAKA,KAAL,GAAaA,KAAb;AACA,eAAK4F,WAAL,GAAmB,KAAKC,eAAL,GAAuB,KAAKC,eAAL,GAAuB,KAAjE;AACA,eAAKE,iBAAL;AACH;;AAEOC,QAAAA,UAAU,GAAG;AACjB,kBAAQ,KAAKhG,IAAL,CAAUiG,OAAlB;AACI,iBAAK;AAAA;AAAA,wDAAkBC,GAAvB;AACI,mBAAKP,WAAL,GAAmB,KAAKC,eAAL,IAAwB,KAAKC,eAAhD;AACA;;AACJ,iBAAK;AAAA;AAAA,wDAAkBM,EAAvB;AACI,mBAAKR,WAAL,GAAmB,KAAKC,eAAL,IAAwB,KAAKC,eAAhD;AACA;AANR;;AASA,cAAI,KAAKF,WAAT,EAAsB;AAClB,iBAAK,IAAMS,MAAX,IAAqB,KAAKpG,IAAL,CAAUqG,OAA/B,EAAwC;AACpC;AAAA;AAAA,gDAAaC,kBAAb,CAAgC,KAAKvG,KAArC,EAA4CqG,MAA5C;AACH;;AACD,iBAAKG,mBAAL;AACH;AACJ;;AAEOR,QAAAA,iBAAiB,GAAG;AACxB,cAAI,KAAK/F,IAAL,CAAUwG,UAAd,EAA0B;AACtB,iBAAKZ,eAAL,GAAuB,KAAvB;AACA;AAAA;AAAA,8CAAaa,EAAb,CAAgB,KAAKzG,IAAL,CAAUwG,UAAV,CAAqBE,SAArC,EAAgD,KAAKC,mBAAL,CAAyBC,IAAzB,CAA8B,IAA9B,CAAhD;AACH,WAHD,MAIK;AACD,iBAAKhB,eAAL,GAAuB,IAAvB;AACH;;AAED,cAAI,KAAK5F,IAAL,CAAU6G,UAAd,EAA0B;AACtB,iBAAKhB,eAAL,GAAuB,KAAvB;AACA;AAAA;AAAA,8CAAaY,EAAb,CAAgB,KAAKzG,IAAL,CAAU6G,UAAV,CAAqBH,SAArC,EAAgD,KAAKI,mBAAL,CAAyBF,IAAzB,CAA8B,IAA9B,CAAhD;AACH,WAHD,MAIK;AACD,iBAAKf,eAAL,GAAuB,IAAvB;AACH;AACJ;;AAEOU,QAAAA,mBAAmB,GAAG;AAC1B,cAAI,KAAKvG,IAAL,CAAUwG,UAAd,EAA0B;AACtB;AAAA;AAAA,8CAAaO,GAAb,CAAiB,KAAK/G,IAAL,CAAUwG,UAAV,CAAqBE,SAAtC,EAAiD,KAAKC,mBAAL,CAAyBC,IAAzB,CAA8B,IAA9B,CAAjD;AACH;;AAED,cAAI,KAAK5G,IAAL,CAAU6G,UAAd,EAA0B;AACtB;AAAA;AAAA,8CAAaE,GAAb,CAAiB,KAAK/G,IAAL,CAAU6G,UAAV,CAAqBH,SAAtC,EAAiD,KAAKI,mBAAL,CAAyBF,IAAzB,CAA8B,IAA9B,CAAjD;AACH;AACJ;;AAEOD,QAAAA,mBAAmB,CAACK,KAAD,EAAgB;AACvC;AACA,eAAKpB,eAAL,GAAuBtG,gBAAgB,CAAC2H,YAAjB,CAA8B,KAAKjH,IAAL,CAAUwG,UAAV,CAAqBU,OAAnD,EAA4DF,KAA5D,EAAmE,KAAKhH,IAAL,CAAUwG,UAAV,CAAqBtG,WAAxF,CAAvB;AACA,eAAK8F,UAAL;AACH;;AAEOc,QAAAA,mBAAmB,CAACE,KAAD,EAAgB;AACvC;AACA,eAAKnB,eAAL,GAAuBvG,gBAAgB,CAAC2H,YAAjB,CAA8B,KAAKjH,IAAL,CAAU6G,UAAV,CAAqBK,OAAnD,EAA4DF,KAA5D,EAAmE,KAAKhH,IAAL,CAAU6G,UAAV,CAAqB3G,WAAxF,CAAvB;AACA,eAAK8F,UAAL;AACH;;AAGkB,eAAZiB,YAAY,CAACE,EAAD,EAAwB3D,CAAxB,EAAmC4D,CAAnC,EAAuD;AACtE,kBAAQD,EAAR;AACI,iBAAK;AAAA;AAAA,wDAAkBE,KAAvB;AACI,qBAAO7D,CAAC,KAAK4D,CAAb;;AACJ,iBAAK;AAAA;AAAA,wDAAkBE,QAAvB;AACI,qBAAO9D,CAAC,KAAK4D,CAAb;;AACJ,iBAAK;AAAA;AAAA,wDAAkBG,OAAvB;AACI,qBAAO/D,CAAC,GAAG4D,CAAX;;AACJ,iBAAK;AAAA;AAAA,wDAAkBI,IAAvB;AACI,qBAAOhE,CAAC,GAAG4D,CAAX;;AACJ,iBAAK;AAAA;AAAA,wDAAkBK,YAAvB;AACI,qBAAOjE,CAAC,IAAI4D,CAAZ;;AACJ,iBAAK;AAAA;AAAA,wDAAkBM,SAAvB;AACI,qBAAOlE,CAAC,IAAI4D,CAAZ;;AACJ;AACI,qBAAO,KAAP;AAdR;AAgBH;;AA7FyB,O", "sourcesContent": ["import { _decorator } from 'cc';\r\nimport { eEmitterActionType, eBulletActionType, EmitterActionData, BulletActionData } from '../data/EventActionData';\r\nimport { eConditionGroupOp, EmitterGroupData, BulletGroupData } from '../data/EventGroupData';\r\nimport { eEventConditionOp } from '../data/EventConditionData';\r\nimport { Bullet } from './Bullet';\r\nimport { eEmitterStatus, Emitter } from './Emitter';\r\nimport { eEasing } from '../move/IMovable';\r\nimport { BulletSystem } from './BulletSystem';\r\n\r\n/**\r\n * Legacy EventActionRunner - simplified version for backward compatibility\r\n * Use GenericActionRunner for new features\r\n */\r\nexport class EventActionRunner {\r\n    private startValue: number = 0;\r\n    private targetValue: number = 0;\r\n    private elapsedTime: number = 0;\r\n    private _isCompleted: boolean = false;\r\n\r\n    readonly data: EmitterActionData | BulletActionData;\r\n    owner: Emitter | Bullet = null;\r\n\r\n    constructor(owner: Emitter | Bullet, data: EmitterActionData | BulletActionData) {\r\n        this.owner = owner;\r\n        this.data = data;\r\n    }\r\n\r\n    get isCompleted(): boolean {\r\n        return this._isCompleted;\r\n    }\r\n\r\n    private getOwnerAsEmitter(): Emitter {\r\n        return this.owner as Emitter;\r\n    }\r\n\r\n    private getOwnerAsBullet(): Bullet {\r\n        return this.owner as Bullet;\r\n    }\r\n\r\n    private static canLerp(actionType: number): boolean {\r\n        // Numeric properties that can be interpolated\r\n        const lerpableEmitterActions = [\r\n            eEmitterActionType.Emitter_InitialDelay,\r\n            eEmitterActionType.Emitter_PrewarmDuration,\r\n            eEmitterActionType.Emitter_Duration,\r\n            eEmitterActionType.Emitter_ElapsedTime,\r\n            eEmitterActionType.Emitter_LoopInterval,\r\n            eEmitterActionType.Emitter_PerEmitInterval,\r\n            eEmitterActionType.Emitter_PerEmitCount,\r\n            eEmitterActionType.Emitter_PerEmitOffsetX,\r\n            eEmitterActionType.Emitter_Angle,\r\n            eEmitterActionType.Emitter_Count\r\n        ];\r\n\r\n        const lerpableBulletActions = [\r\n            eBulletActionType.Bullet_Duration,\r\n            eBulletActionType.Bullet_ElapsedTime,\r\n            eBulletActionType.Bullet_PosX,\r\n            eBulletActionType.Bullet_PosY,\r\n            eBulletActionType.Bullet_Damage,\r\n            eBulletActionType.Bullet_Speed,\r\n            eBulletActionType.Bullet_SpeedAngle,\r\n            eBulletActionType.Bullet_Acceleration,\r\n            eBulletActionType.Bullet_AccelerationAngle,\r\n            eBulletActionType.Bullet_Scale,\r\n            eBulletActionType.Bullet_ColorR,\r\n            eBulletActionType.Bullet_ColorG,\r\n            eBulletActionType.Bullet_ColorB,\r\n            eBulletActionType.Bullet_ColorA\r\n        ];\r\n\r\n        return lerpableEmitterActions.indexOf(actionType) !== -1 || lerpableBulletActions.indexOf(actionType) !== -1;\r\n    }\r\n\r\n    private getInitialValue(): number {\r\n        // Simplified initial value getter - uses existing property access patterns\r\n        // For new features, use GenericActionRunner which has better value handling\r\n        try {\r\n            if (this.data.actionType < eBulletActionType.Bullet_Duration) {\r\n                // Emitter actions\r\n                const emitter = this.getOwnerAsEmitter();\r\n                switch (this.data.actionType) {\r\n                    case eEmitterActionType.Emitter_Active:\r\n                        return emitter.status != eEmitterStatus.None ? 1 : 0;\r\n                    case eEmitterActionType.Emitter_InitialDelay:\r\n                        return emitter.initialDelay;\r\n                    case eEmitterActionType.Emitter_Duration:\r\n                        return emitter.emitDuration;\r\n                    case eEmitterActionType.Emitter_PerEmitInterval:\r\n                        return emitter.perEmitInterval;\r\n                    case eEmitterActionType.Emitter_Angle:\r\n                        return emitter.angle;\r\n                    // Add other commonly used emitter properties as needed\r\n                    default:\r\n                        return 0;\r\n                }\r\n            } else {\r\n                // Bullet actions\r\n                const bullet = this.getOwnerAsBullet();\r\n                switch (this.data.actionType) {\r\n                    case eBulletActionType.Bullet_Duration:\r\n                        return bullet.duration;\r\n                    case eBulletActionType.Bullet_Speed:\r\n                        return bullet.mover.speed;\r\n                    case eBulletActionType.Bullet_PosX:\r\n                        return bullet.node.getPosition().x;\r\n                    case eBulletActionType.Bullet_PosY:\r\n                        return bullet.node.getPosition().y;\r\n                    case eBulletActionType.Bullet_ColorA:\r\n                        return bullet.bulletSprite.color.a;\r\n                    // Add other commonly used bullet properties as needed\r\n                    default:\r\n                        return 0;\r\n                }\r\n            }\r\n        } catch (error) {\r\n            console.warn('EventActionRunner: Failed to get initial value for action type', this.data.actionType, error);\r\n            return 0;\r\n        }\r\n    }\r\n\r\n    private getTargetValue(): number {\r\n        if (EventActionRunner.canLerp(this.data.actionType)) {\r\n            if (this.data.isRandom) {\r\n                return Math.random() * (this.data.maxValue - this.data.minValue) + this.data.minValue;\r\n            }\r\n            return this.data.minValue;\r\n        }\r\n        return this.data.boolValue ? 1 : 0;\r\n    }\r\n\r\n    start() {\r\n        if (!this.data) {\r\n            console.error('EventActionRunner: data is null');\r\n            return;\r\n        }\r\n\r\n        this.startValue = this.getInitialValue();\r\n        this.targetValue = this.getTargetValue();\r\n        this.elapsedTime = 0;\r\n        this._isCompleted = false;\r\n    }\r\n\r\n    tick(dt: number) {\r\n        this.elapsedTime += dt;\r\n\r\n        if (this.elapsedTime >= this.data.duration) {\r\n            this._isCompleted = true;\r\n            this.apply(this.targetValue);\r\n        } else if (EventActionRunner.canLerp(this.data.actionType)) {\r\n            const currentValue = this.lerpValue(this.startValue, this.targetValue);\r\n            this.apply(currentValue);\r\n        }\r\n    }\r\n\r\n    private lerpValue(startValue: number, targetValue: number): number {\r\n        const progress = Math.min(1.0, this.elapsedTime / this.data.duration);\r\n        const easedProgress = EventActionRunner.applyEasing(this.data.easing, progress);\r\n        return startValue + (targetValue - startValue) * easedProgress;\r\n    }\r\n\r\n    private apply(currentValue: number) {\r\n        try {\r\n            if (this.data.actionType < eBulletActionType.Bullet_Duration) {\r\n                this.getOwnerAsEmitter().applyAction(this.data.actionType as eEmitterActionType, currentValue);\r\n            } else {\r\n                this.getOwnerAsBullet().applyAction(this.data.actionType as eBulletActionType, currentValue);\r\n            }\r\n        } catch (error) {\r\n            console.warn('EventActionRunner: Failed to apply action', this.data.actionType, currentValue, error);\r\n            this._isCompleted = true; // Stop on error\r\n        }\r\n    }\r\n    \r\n    static applyEasing(easing: eEasing, t: number): number {\r\n        switch (easing) {\r\n            case eEasing.Linear:\r\n                return t;\r\n\r\n            case eEasing.InSine:\r\n                return 1 - Math.cos(t * Math.PI / 2);\r\n\r\n            case eEasing.OutSine:\r\n                return Math.sin(t * Math.PI / 2);\r\n\r\n            case eEasing.InOutSine:\r\n                return -(Math.cos(Math.PI * t) - 1) / 2;\r\n\r\n            case eEasing.InQuad:\r\n                return t * t;\r\n\r\n            case eEasing.OutQuad:\r\n                return 1 - (1 - t) * (1 - t);\r\n\r\n            case eEasing.InOutQuad:\r\n                return t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2;\r\n\r\n            default:\r\n                return t;\r\n        }\r\n    }\r\n}\r\n\r\nexport class EventGroupRunner {\r\n    readonly data: EmitterGroupData | BulletGroupData;\r\n    owner: Emitter | Bullet = null;\r\n    isTriggered: boolean = false;\r\n    isConditionAMet: boolean = false;\r\n    isConditionBMet: boolean = false;\r\n\r\n    constructor(data: EmitterGroupData | BulletGroupData) {\r\n        this.data = data;\r\n    }\r\n\r\n    public init(owner: Emitter | Bullet) {\r\n        this.owner = owner;\r\n        this.isTriggered = this.isConditionAMet = this.isConditionBMet = false;\r\n        this.registerListeners();\r\n    }\r\n\r\n    private tryTrigger() {\r\n        switch (this.data.groupOp) {\r\n            case eConditionGroupOp.And:\r\n                this.isTriggered = this.isConditionAMet && this.isConditionBMet;\r\n                break;\r\n            case eConditionGroupOp.Or:\r\n                this.isTriggered = this.isConditionAMet || this.isConditionBMet;\r\n                break;\r\n        }\r\n\r\n        if (this.isTriggered) {\r\n            for (const action of this.data.actions) {\r\n                BulletSystem.createActionRunner(this.owner, action);\r\n            }\r\n            this.unRegisterListeners();\r\n        }\r\n    }\r\n\r\n    private registerListeners() {\r\n        if (this.data.conditionA) {\r\n            this.isConditionAMet = false;\r\n            BulletSystem.on(this.data.conditionA.eventType, this.onConditionAChanged.bind(this));\r\n        }\r\n        else {\r\n            this.isConditionAMet = true;\r\n        }\r\n\r\n        if (this.data.conditionB) {\r\n            this.isConditionBMet = false;\r\n            BulletSystem.on(this.data.conditionB.eventType, this.onConditionBChanged.bind(this));\r\n        }\r\n        else {\r\n            this.isConditionBMet = true;\r\n        }\r\n    }\r\n\r\n    private unRegisterListeners() {\r\n        if (this.data.conditionA) {\r\n            BulletSystem.off(this.data.conditionA.eventType, this.onConditionAChanged.bind(this));\r\n        }\r\n\r\n        if (this.data.conditionB) {\r\n            BulletSystem.off(this.data.conditionB.eventType, this.onConditionBChanged.bind(this));\r\n        }\r\n    }\r\n\r\n    private onConditionAChanged(value: number) {\r\n        // Handle condition A change\r\n        this.isConditionAMet = EventGroupRunner.compareValue(this.data.conditionA.eventOp, value, this.data.conditionA.targetValue);\r\n        this.tryTrigger();\r\n    }\r\n\r\n    private onConditionBChanged(value: number) {\r\n        // Handle condition B change\r\n        this.isConditionBMet = EventGroupRunner.compareValue(this.data.conditionB.eventOp, value, this.data.conditionB.targetValue);\r\n        this.tryTrigger();\r\n    }\r\n\r\n\r\n    static compareValue(op: eEventConditionOp, a: number, b: number): boolean {\r\n        switch (op) {\r\n            case eEventConditionOp.Equal:\r\n                return a === b;\r\n            case eEventConditionOp.NotEqual:\r\n                return a !== b;\r\n            case eEventConditionOp.Greater:\r\n                return a > b;\r\n            case eEventConditionOp.Less:\r\n                return a < b;\r\n            case eEventConditionOp.GreaterEqual:\r\n                return a >= b;\r\n            case eEventConditionOp.LessEqual:\r\n                return a <= b;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n}"]}