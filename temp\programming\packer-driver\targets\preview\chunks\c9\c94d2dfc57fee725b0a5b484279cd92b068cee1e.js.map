{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/EventRunner.ts"], "names": ["EventActionRunner", "EventGroupRunner", "eEmitterActionType", "eBulletActionType", "eConditionGroupOp", "eEventConditionOp", "eEmitterStatus", "eEasing", "BulletSystem", "isCompleted", "_isCompleted", "isRunning", "elapsedTime", "constructor", "owner", "data", "startValue", "targetValue", "getOwnerAsEmitter", "getOwnerAsBullet", "canLerp", "actionType", "Bullet_Duration", "Emitter_InitialDelay", "Emitter_PrewarmDuration", "Emitter_Duration", "Emitter_ElapsedTime", "Emitter_LoopInterval", "Emitter_PerEmitInterval", "Emitter_PerEmitCount", "Emitter_PerEmitOffsetX", "Emitter_Angle", "Bullet_ElapsedTime", "Bullet_PosX", "Bullet_PosY", "Bullet_Damage", "Bullet_Speed", "Bullet_SpeedAngle", "Bullet_Acceleration", "Bullet_AccelerationAngle", "Bullet_Scale", "Bullet_ColorR", "Bullet_ColorG", "Bullet_ColorB", "Bullet_ColorA", "getInitialValue", "Emitter_Active", "status", "None", "initialDelay", "preWarmDuration", "emitDuration", "totalElapsedTime", "loopInterval", "perEmitInterval", "perEmitCount", "perEmitOffsetX", "angle", "Emitter_Count", "count", "duration", "node", "getPosition", "x", "y", "damage", "mover", "speed", "speedAngle", "acceleration", "accelerationAngle", "getScale", "bulletSprite", "color", "r", "g", "b", "a", "Bullet_FaceMovingDir", "isFacingMoveDir", "Bullet_TrackingTarget", "isTrackingTarget", "Bullet_Destructive", "isDestructive", "Bullet_DestructiveOnHit", "isDestructiveOnHit", "getTargetValue", "isRandom", "Math", "random", "maxValue", "minValue", "boolValue", "start", "console", "error", "tick", "dt", "apply", "currentValue", "lerp<PERSON><PERSON>ue", "progress", "min", "easedProgress", "applyEasing", "easing", "applyAction", "t", "Linear", "InSine", "cos", "PI", "OutSine", "sin", "InOutSine", "InQuad", "OutQuad", "InOutQuad", "pow", "isTriggered", "isConditionAMet", "isConditionBMet", "init", "registerListeners", "tryTrigger", "groupOp", "And", "Or", "action", "actions", "createActionRunner", "unRegisterListeners", "conditionA", "on", "eventType", "onConditionAChanged", "bind", "conditionB", "onConditionBChanged", "off", "value", "compareValue", "eventOp", "op", "Equal", "NotEqual", "Greater", "Less", "GreaterEqual", "LessEqual"], "mappings": ";;;mMASaA,iB,EAkOAC,gB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA1OJC,MAAAA,kB,iBAAAA,kB;AAAoBC,MAAAA,iB,iBAAAA,iB;;AACpBC,MAAAA,iB,iBAAAA,iB;;AACAC,MAAAA,iB,iBAAAA,iB;;AAEAC,MAAAA,c,iBAAAA,c;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Y,iBAAAA,Y;;;;;;;;;mCAEIR,iB,GAAN,MAAMA,iBAAN,CAAwB;AAMZ,YAAXS,WAAW,GAAY;AACvB,iBAAO,KAAKC,YAAZ;AACH;;AAEY,YAATC,SAAS,GAAY;AACrB,iBAAO,CAAC,KAAKD,YAAN,IAAsB,KAAKE,WAAL,GAAmB,CAAhD;AACH,SAZ0B,CAc3B;;;AAIAC,QAAAA,WAAW,CAACC,KAAD,EAA0BC,IAA1B,EAAsE;AAAA,eAhBzEC,UAgByE,GAhBpD,CAgBoD;AAAA,eAfzEC,WAeyE,GAfnD,CAemD;AAAA,eAdzEL,WAcyE,GAdnD,CAcmD;AAAA,eAbzEF,YAayE,GAbjD,KAaiD;AAAA,eAHxEK,IAGwE;AAAA,eAFjFD,KAEiF,GAFvD,IAEuD;AAC7E,eAAKA,KAAL,GAAaA,KAAb;AACA,eAAKC,IAAL,GAAYA,IAAZ;AACH;;AAEDG,QAAAA,iBAAiB,GAAY;AACzB,iBAAO,KAAKJ,KAAZ;AACH;;AAEDK,QAAAA,gBAAgB,GAAW;AACvB,iBAAO,KAAKL,KAAZ;AACH;;AAEa,eAAPM,OAAO,CAACC,UAAD,EAA8B;AACxC,cAAIA,UAAU,GAAG;AAAA;AAAA,sDAAkBC,eAAnC,EAAoD;AAChD,oBAAQD,UAAR;AACI,mBAAK;AAAA;AAAA,4DAAmBE,oBAAxB;AACA,mBAAK;AAAA;AAAA,4DAAmBC,uBAAxB;AACA,mBAAK;AAAA;AAAA,4DAAmBC,gBAAxB;AACA,mBAAK;AAAA;AAAA,4DAAmBC,mBAAxB;AACA,mBAAK;AAAA;AAAA,4DAAmBC,oBAAxB;AACA,mBAAK;AAAA;AAAA,4DAAmBC,uBAAxB;AACA,mBAAK;AAAA;AAAA,4DAAmBC,oBAAxB;AACA,mBAAK;AAAA;AAAA,4DAAmBC,sBAAxB;AACA,mBAAK;AAAA;AAAA,4DAAmBC,aAAxB;AACI,uBAAO,IAAP;;AACJ;AACI,uBAAO,KAAP;AAZR;AAcH,WAfD,MAgBK;AACD,oBAAQV,UAAR;AACI,mBAAK;AAAA;AAAA,0DAAkBC,eAAvB;AACA,mBAAK;AAAA;AAAA,0DAAkBU,kBAAvB;AACA,mBAAK;AAAA;AAAA,0DAAkBC,WAAvB;AACA,mBAAK;AAAA;AAAA,0DAAkBC,WAAvB;AACA,mBAAK;AAAA;AAAA,0DAAkBC,aAAvB;AACA,mBAAK;AAAA;AAAA,0DAAkBC,YAAvB;AACA,mBAAK;AAAA;AAAA,0DAAkBC,iBAAvB;AACA,mBAAK;AAAA;AAAA,0DAAkBC,mBAAvB;AACA,mBAAK;AAAA;AAAA,0DAAkBC,wBAAvB;AACA,mBAAK;AAAA;AAAA,0DAAkBC,YAAvB;AACA,mBAAK;AAAA;AAAA,0DAAkBC,aAAvB;AACA,mBAAK;AAAA;AAAA,0DAAkBC,aAAvB;AACA,mBAAK;AAAA;AAAA,0DAAkBC,aAAvB;AACA,mBAAK;AAAA;AAAA,0DAAkBC,aAAvB;AACI,uBAAO,IAAP;;AACJ;AACI,uBAAO,KAAP;AAjBR;AAmBH;AACJ;;AAEDC,QAAAA,eAAe,GAAW;AACtB,kBAAQ,KAAK9B,IAAL,CAAUM,UAAlB;AACI,iBAAK;AAAA;AAAA,0DAAmByB,cAAxB;AACI,qBAAO,KAAK5B,iBAAL,GAAyB6B,MAAzB,IAAmC;AAAA;AAAA,oDAAeC,IAAlD,GAAyD,CAAzD,GAA6D,CAApE;;AACJ,iBAAK;AAAA;AAAA,0DAAmBzB,oBAAxB;AACI,qBAAO,KAAKL,iBAAL,GAAyB+B,YAAhC;;AACJ,iBAAK;AAAA;AAAA,0DAAmBzB,uBAAxB;AACI,qBAAO,KAAKN,iBAAL,GAAyBgC,eAAhC;;AACJ,iBAAK;AAAA;AAAA,0DAAmBzB,gBAAxB;AACI,qBAAO,KAAKP,iBAAL,GAAyBiC,YAAhC;;AACJ,iBAAK;AAAA;AAAA,0DAAmBzB,mBAAxB;AACI,qBAAO,KAAKR,iBAAL,GAAyBkC,gBAAhC;;AACJ,iBAAK;AAAA;AAAA,0DAAmBzB,oBAAxB;AACI,qBAAO,KAAKT,iBAAL,GAAyBmC,YAAhC;;AACJ,iBAAK;AAAA;AAAA,0DAAmBzB,uBAAxB;AACI,qBAAO,KAAKV,iBAAL,GAAyBoC,eAAhC;;AACJ,iBAAK;AAAA;AAAA,0DAAmBzB,oBAAxB;AACI,qBAAO,KAAKX,iBAAL,GAAyBqC,YAAhC;;AACJ,iBAAK;AAAA;AAAA,0DAAmBzB,sBAAxB;AACI,qBAAO,KAAKZ,iBAAL,GAAyBsC,cAAhC;;AACJ,iBAAK;AAAA;AAAA,0DAAmBzB,aAAxB;AACI,qBAAO,KAAKb,iBAAL,GAAyBuC,KAAhC;;AACJ,iBAAK;AAAA;AAAA,0DAAmBC,aAAxB;AACI,qBAAO,KAAKxC,iBAAL,GAAyByC,KAAhC;AAEJ;;AACA,iBAAK;AAAA;AAAA,wDAAkBrC,eAAvB;AACI,qBAAO,KAAKH,gBAAL,GAAwByC,QAA/B;;AACJ,iBAAK;AAAA;AAAA,wDAAkB5B,kBAAvB;AACI,qBAAO,KAAKb,gBAAL,GAAwBP,WAA/B;;AACJ,iBAAK;AAAA;AAAA,wDAAkBqB,WAAvB;AACI,qBAAO,KAAKd,gBAAL,GAAwB0C,IAAxB,CAA6BC,WAA7B,GAA2CC,CAAlD;;AACJ,iBAAK;AAAA;AAAA,wDAAkB7B,WAAvB;AACI,qBAAO,KAAKf,gBAAL,GAAwB0C,IAAxB,CAA6BC,WAA7B,GAA2CE,CAAlD;;AACJ,iBAAK;AAAA;AAAA,wDAAkB7B,aAAvB;AACI,qBAAO,KAAKhB,gBAAL,GAAwB8C,MAA/B;;AACJ,iBAAK;AAAA;AAAA,wDAAkB7B,YAAvB;AACI,qBAAO,KAAKjB,gBAAL,GAAwB+C,KAAxB,CAA8BC,KAArC;;AACJ,iBAAK;AAAA;AAAA,wDAAkB9B,iBAAvB;AACI,qBAAO,KAAKlB,gBAAL,GAAwB+C,KAAxB,CAA8BE,UAArC;;AACJ,iBAAK;AAAA;AAAA,wDAAkB9B,mBAAvB;AACI,qBAAO,KAAKnB,gBAAL,GAAwB+C,KAAxB,CAA8BG,YAArC;;AACJ,iBAAK;AAAA;AAAA,wDAAkB9B,wBAAvB;AACI,qBAAO,KAAKpB,gBAAL,GAAwB+C,KAAxB,CAA8BI,iBAArC;;AACJ,iBAAK;AAAA;AAAA,wDAAkB9B,YAAvB;AACI,qBAAO,KAAKrB,gBAAL,GAAwB0C,IAAxB,CAA6BU,QAA7B,GAAwCR,CAA/C;;AACJ,iBAAK;AAAA;AAAA,wDAAkBtB,aAAvB;AACI,qBAAO,KAAKtB,gBAAL,GAAwBqD,YAAxB,CAAqCC,KAArC,CAA2CC,CAAlD;;AACJ,iBAAK;AAAA;AAAA,wDAAkBhC,aAAvB;AACI,qBAAO,KAAKvB,gBAAL,GAAwBqD,YAAxB,CAAqCC,KAArC,CAA2CE,CAAlD;;AACJ,iBAAK;AAAA;AAAA,wDAAkBhC,aAAvB;AACI,qBAAO,KAAKxB,gBAAL,GAAwBqD,YAAxB,CAAqCC,KAArC,CAA2CG,CAAlD;;AACJ,iBAAK;AAAA;AAAA,wDAAkBhC,aAAvB;AACI,qBAAO,KAAKzB,gBAAL,GAAwBqD,YAAxB,CAAqCC,KAArC,CAA2CI,CAAlD;;AACJ,iBAAK;AAAA;AAAA,wDAAkBC,oBAAvB;AACI,qBAAO,KAAK3D,gBAAL,GAAwB+C,KAAxB,CAA8Ba,eAA9B,GAAgD,CAAhD,GAAoD,CAA3D;;AACJ,iBAAK;AAAA;AAAA,wDAAkBC,qBAAvB;AACI,qBAAO,KAAK7D,gBAAL,GAAwB+C,KAAxB,CAA8Be,gBAA9B,GAAiD,CAAjD,GAAqD,CAA5D;;AACJ,iBAAK;AAAA;AAAA,wDAAkBC,kBAAvB;AACI,qBAAO,KAAK/D,gBAAL,GAAwBgE,aAAxB,GAAwC,CAAxC,GAA4C,CAAnD;;AACJ,iBAAK;AAAA;AAAA,wDAAkBC,uBAAvB;AACI,qBAAO,KAAKjE,gBAAL,GAAwBkE,kBAAxB,GAA6C,CAA7C,GAAiD,CAAxD;;AAEJ;AAAS;AA9Db;;AAiEA,iBAAO,CAAP;AACH;;AAEDC,QAAAA,cAAc,GAAW;AACrB,cAAItF,iBAAiB,CAACoB,OAAlB,CAA0B,KAAKL,IAAL,CAAUM,UAApC,CAAJ,EAAqD;AACjD,gBAAI,KAAKN,IAAL,CAAUwE,QAAd,EAAwB;AACpB,qBAAOC,IAAI,CAACC,MAAL,MAAiB,KAAK1E,IAAL,CAAU2E,QAAV,GAAqB,KAAK3E,IAAL,CAAU4E,QAAhD,IAA4D,KAAK5E,IAAL,CAAU4E,QAA7E;AACH;;AACD,mBAAO,KAAK5E,IAAL,CAAU4E,QAAjB;AACH;;AAED,iBAAO,KAAK5E,IAAL,CAAU6E,SAAV,GAAsB,CAAtB,GAA0B,CAAjC;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ,cAAI,CAAC,KAAK9E,IAAV,EAAgB;AACZ+E,YAAAA,OAAO,CAACC,KAAR,CAAc,iCAAd;AACA;AACH;;AAED,eAAK/E,UAAL,GAAkB,KAAK6B,eAAL,EAAlB;AACA,eAAK5B,WAAL,GAAmB,KAAKqE,cAAL,EAAnB;AACA,eAAK1E,WAAL,GAAmB,CAAnB;AACA,eAAKF,YAAL,GAAoB,KAApB;AACH;;AAEDsF,QAAAA,IAAI,CAACC,EAAD,EAAa;AACb;AACA,eAAKrF,WAAL,IAAoBqF,EAApB;;AAEA,cAAI,KAAKrF,WAAL,IAAoB,KAAKG,IAAL,CAAU6C,QAAlC,EAA4C;AACxC,iBAAKlD,YAAL,GAAoB,IAApB;AACA,iBAAKwF,KAAL,CAAW,KAAKjF,WAAhB;AACH,WAHD,MAIK;AACD,gBAAIjB,iBAAiB,CAACoB,OAAlB,CAA0B,KAAKL,IAAL,CAAUM,UAApC,CAAJ,EAAqD;AACjD,kBAAM8E,YAAY,GAAG,KAAKC,SAAL,CAAe,KAAKpF,UAApB,EAAgC,KAAKC,WAArC,CAArB;AACA,mBAAKiF,KAAL,CAAWC,YAAX;AACH;AACJ;AACJ;;AAEDC,QAAAA,SAAS,CAACpF,UAAD,EAAqBC,WAArB,EAAkD;AACvD;AACA,cAAIoF,QAAQ,GAAGb,IAAI,CAACc,GAAL,CAAS,GAAT,EAAc,KAAK1F,WAAL,GAAmB,KAAKG,IAAL,CAAU6C,QAA3C,CAAf,CAFuD,CAGvD;;AACA,cAAI2C,aAAa,GAAGvG,iBAAiB,CAACwG,WAAlB,CAA8B,KAAKzF,IAAL,CAAU0F,MAAxC,EAAgDJ,QAAhD,CAApB,CAJuD,CAKvD;;AACA,iBAAOrF,UAAU,GAAG,CAACC,WAAW,GAAGD,UAAf,IAA6BuF,aAAjD;AACH;;AAEDL,QAAAA,KAAK,CAACC,YAAD,EAAuB;AACxB,cAAI,KAAKpF,IAAL,CAAUM,UAAV,GAAuB;AAAA;AAAA,sDAAkBC,eAA7C,EAA8D;AAC1D,iBAAKJ,iBAAL,GAAyBwF,WAAzB,CAAqC,KAAK3F,IAAL,CAAUM,UAA/C,EAAiF8E,YAAjF;AACH,WAFD,MAGK;AACD,iBAAKhF,gBAAL,GAAwBuF,WAAxB,CAAoC,KAAK3F,IAAL,CAAUM,UAA9C,EAA+E8E,YAA/E;AACH;AACJ;;AAEiB,eAAXK,WAAW,CAACC,MAAD,EAAkBE,CAAlB,EAAqC;AACnD,kBAAQF,MAAR;AACI,iBAAK;AAAA;AAAA,oCAAQG,MAAb;AACI,qBAAOD,CAAP;;AAEJ,iBAAK;AAAA;AAAA,oCAAQE,MAAb;AACI,qBAAO,IAAIrB,IAAI,CAACsB,GAAL,CAASH,CAAC,GAAGnB,IAAI,CAACuB,EAAT,GAAc,CAAvB,CAAX;;AAEJ,iBAAK;AAAA;AAAA,oCAAQC,OAAb;AACI,qBAAOxB,IAAI,CAACyB,GAAL,CAASN,CAAC,GAAGnB,IAAI,CAACuB,EAAT,GAAc,CAAvB,CAAP;;AAEJ,iBAAK;AAAA;AAAA,oCAAQG,SAAb;AACI,qBAAO,EAAE1B,IAAI,CAACsB,GAAL,CAAStB,IAAI,CAACuB,EAAL,GAAUJ,CAAnB,IAAwB,CAA1B,IAA+B,CAAtC;;AAEJ,iBAAK;AAAA;AAAA,oCAAQQ,MAAb;AACI,qBAAOR,CAAC,GAAGA,CAAX;;AAEJ,iBAAK;AAAA;AAAA,oCAAQS,OAAb;AACI,qBAAO,IAAI,CAAC,IAAIT,CAAL,KAAW,IAAIA,CAAf,CAAX;;AAEJ,iBAAK;AAAA;AAAA,oCAAQU,SAAb;AACI,qBAAOV,CAAC,GAAG,GAAJ,GAAU,IAAIA,CAAJ,GAAQA,CAAlB,GAAsB,IAAInB,IAAI,CAAC8B,GAAL,CAAS,CAAC,CAAD,GAAKX,CAAL,GAAS,CAAlB,EAAqB,CAArB,IAA0B,CAA3D;;AAEJ;AACI,qBAAOA,CAAP;AAvBR;AAyBH;;AA/N0B,O;;kCAkOlB1G,gB,GAAN,MAAMA,gBAAN,CAAuB;AAO1BY,QAAAA,WAAW,CAACE,IAAD,EAA2C;AAAA,eAN7CA,IAM6C;AAAA,eALtDD,KAKsD,GAL5B,IAK4B;AAAA,eAJtDyG,WAIsD,GAJ/B,KAI+B;AAAA,eAHtDC,eAGsD,GAH3B,KAG2B;AAAA,eAFtDC,eAEsD,GAF3B,KAE2B;AAClD,eAAK1G,IAAL,GAAYA,IAAZ;AACH;;AAEM2G,QAAAA,IAAI,CAAC5G,KAAD,EAA0B;AACjC,eAAKA,KAAL,GAAaA,KAAb;AACA,eAAKyG,WAAL,GAAmB,KAAKC,eAAL,GAAuB,KAAKC,eAAL,GAAuB,KAAjE;AACA,eAAKE,iBAAL;AACH;;AAEOC,QAAAA,UAAU,GAAG;AACjB,kBAAQ,KAAK7G,IAAL,CAAU8G,OAAlB;AACI,iBAAK;AAAA;AAAA,wDAAkBC,GAAvB;AACI,mBAAKP,WAAL,GAAmB,KAAKC,eAAL,IAAwB,KAAKC,eAAhD;AACA;;AACJ,iBAAK;AAAA;AAAA,wDAAkBM,EAAvB;AACI,mBAAKR,WAAL,GAAmB,KAAKC,eAAL,IAAwB,KAAKC,eAAhD;AACA;AANR;;AASA,cAAI,KAAKF,WAAT,EAAsB;AAClB,iBAAK,IAAMS,MAAX,IAAqB,KAAKjH,IAAL,CAAUkH,OAA/B,EAAwC;AACpC;AAAA;AAAA,gDAAaC,kBAAb,CAAgC,KAAKpH,KAArC,EAA4CkH,MAA5C;AACH;;AACD,iBAAKG,mBAAL;AACH;AACJ;;AAEOR,QAAAA,iBAAiB,GAAG;AACxB,cAAI,KAAK5G,IAAL,CAAUqH,UAAd,EAA0B;AACtB,iBAAKZ,eAAL,GAAuB,KAAvB;AACA;AAAA;AAAA,8CAAaa,EAAb,CAAgB,KAAKtH,IAAL,CAAUqH,UAAV,CAAqBE,SAArC,EAAgD,KAAKC,mBAAL,CAAyBC,IAAzB,CAA8B,IAA9B,CAAhD;AACH,WAHD,MAIK;AACD,iBAAKhB,eAAL,GAAuB,IAAvB;AACH;;AAED,cAAI,KAAKzG,IAAL,CAAU0H,UAAd,EAA0B;AACtB,iBAAKhB,eAAL,GAAuB,KAAvB;AACA;AAAA;AAAA,8CAAaY,EAAb,CAAgB,KAAKtH,IAAL,CAAU0H,UAAV,CAAqBH,SAArC,EAAgD,KAAKI,mBAAL,CAAyBF,IAAzB,CAA8B,IAA9B,CAAhD;AACH,WAHD,MAIK;AACD,iBAAKf,eAAL,GAAuB,IAAvB;AACH;AACJ;;AAEOU,QAAAA,mBAAmB,GAAG;AAC1B,cAAI,KAAKpH,IAAL,CAAUqH,UAAd,EAA0B;AACtB;AAAA;AAAA,8CAAaO,GAAb,CAAiB,KAAK5H,IAAL,CAAUqH,UAAV,CAAqBE,SAAtC,EAAiD,KAAKC,mBAAL,CAAyBC,IAAzB,CAA8B,IAA9B,CAAjD;AACH;;AAED,cAAI,KAAKzH,IAAL,CAAU0H,UAAd,EAA0B;AACtB;AAAA;AAAA,8CAAaE,GAAb,CAAiB,KAAK5H,IAAL,CAAU0H,UAAV,CAAqBH,SAAtC,EAAiD,KAAKI,mBAAL,CAAyBF,IAAzB,CAA8B,IAA9B,CAAjD;AACH;AACJ;;AAEOD,QAAAA,mBAAmB,CAACK,KAAD,EAAgB;AACvC;AACA,eAAKpB,eAAL,GAAuBvH,gBAAgB,CAAC4I,YAAjB,CAA8B,KAAK9H,IAAL,CAAUqH,UAAV,CAAqBU,OAAnD,EAA4DF,KAA5D,EAAmE,KAAK7H,IAAL,CAAUqH,UAAV,CAAqBnH,WAAxF,CAAvB;AACA,eAAK2G,UAAL;AACH;;AAEOc,QAAAA,mBAAmB,CAACE,KAAD,EAAgB;AACvC;AACA,eAAKnB,eAAL,GAAuBxH,gBAAgB,CAAC4I,YAAjB,CAA8B,KAAK9H,IAAL,CAAU0H,UAAV,CAAqBK,OAAnD,EAA4DF,KAA5D,EAAmE,KAAK7H,IAAL,CAAU0H,UAAV,CAAqBxH,WAAxF,CAAvB;AACA,eAAK2G,UAAL;AACH;;AAGkB,eAAZiB,YAAY,CAACE,EAAD,EAAwBlE,CAAxB,EAAmCD,CAAnC,EAAuD;AACtE,kBAAQmE,EAAR;AACI,iBAAK;AAAA;AAAA,wDAAkBC,KAAvB;AACI,qBAAOnE,CAAC,KAAKD,CAAb;;AACJ,iBAAK;AAAA;AAAA,wDAAkBqE,QAAvB;AACI,qBAAOpE,CAAC,KAAKD,CAAb;;AACJ,iBAAK;AAAA;AAAA,wDAAkBsE,OAAvB;AACI,qBAAOrE,CAAC,GAAGD,CAAX;;AACJ,iBAAK;AAAA;AAAA,wDAAkBuE,IAAvB;AACI,qBAAOtE,CAAC,GAAGD,CAAX;;AACJ,iBAAK;AAAA;AAAA,wDAAkBwE,YAAvB;AACI,qBAAOvE,CAAC,IAAID,CAAZ;;AACJ,iBAAK;AAAA;AAAA,wDAAkByE,SAAvB;AACI,qBAAOxE,CAAC,IAAID,CAAZ;;AACJ;AACI,qBAAO,KAAP;AAdR;AAgBH;;AA7FyB,O", "sourcesContent": ["import { _decorator, misc, Component, Enum, Vec3 } from 'cc';\r\nimport { eEmitterActionType, eBulletActionType, EmitterActionData, BulletActionData } from '../data/EventActionData';\r\nimport { eConditionGroupOp, EmitterGroupData, BulletGroupData } from '../data/EventGroupData';\r\nimport { eEventConditionOp } from '../data/EventConditionData';\r\nimport { Bullet } from './Bullet';\r\nimport { eEmitterStatus, Emitter } from './Emitter';\r\nimport { eEasing } from '../move/IMovable';\r\nimport { BulletSystem } from './BulletSystem';\r\n\r\nexport class EventActionRunner {\r\n\r\n    private startValue: number = 0;\r\n    private targetValue: number = 0;\r\n    private elapsedTime: number = 0;\r\n    private _isCompleted: boolean = false;\r\n    get isCompleted(): boolean {\r\n        return this._isCompleted;\r\n    }\r\n    \r\n    get isRunning(): boolean {\r\n        return !this._isCompleted && this.elapsedTime > 0;\r\n    }\r\n\r\n    // readonly EventActionData\r\n    readonly data: EmitterActionData | BulletActionData;\r\n    owner: Emitter | Bullet = null;\r\n\r\n    constructor(owner: Emitter | Bullet, data: EmitterActionData | BulletActionData) {\r\n        this.owner = owner;\r\n        this.data = data;\r\n    }\r\n\r\n    getOwnerAsEmitter(): Emitter {\r\n        return this.owner as Emitter;\r\n    }\r\n\r\n    getOwnerAsBullet(): Bullet {\r\n        return this.owner as Bullet;\r\n    }\r\n\r\n    static canLerp(actionType: number): boolean {\r\n        if (actionType < eBulletActionType.Bullet_Duration) {\r\n            switch (actionType) {\r\n                case eEmitterActionType.Emitter_InitialDelay:\r\n                case eEmitterActionType.Emitter_PrewarmDuration:\r\n                case eEmitterActionType.Emitter_Duration:\r\n                case eEmitterActionType.Emitter_ElapsedTime:\r\n                case eEmitterActionType.Emitter_LoopInterval:\r\n                case eEmitterActionType.Emitter_PerEmitInterval:\r\n                case eEmitterActionType.Emitter_PerEmitCount:\r\n                case eEmitterActionType.Emitter_PerEmitOffsetX:\r\n                case eEmitterActionType.Emitter_Angle:\r\n                    return true;\r\n                default:\r\n                    return false;\r\n            }\r\n        }\r\n        else {\r\n            switch (actionType) {\r\n                case eBulletActionType.Bullet_Duration:\r\n                case eBulletActionType.Bullet_ElapsedTime:\r\n                case eBulletActionType.Bullet_PosX:\r\n                case eBulletActionType.Bullet_PosY:\r\n                case eBulletActionType.Bullet_Damage:\r\n                case eBulletActionType.Bullet_Speed:\r\n                case eBulletActionType.Bullet_SpeedAngle:\r\n                case eBulletActionType.Bullet_Acceleration:\r\n                case eBulletActionType.Bullet_AccelerationAngle:\r\n                case eBulletActionType.Bullet_Scale:\r\n                case eBulletActionType.Bullet_ColorR:\r\n                case eBulletActionType.Bullet_ColorG:\r\n                case eBulletActionType.Bullet_ColorB:\r\n                case eBulletActionType.Bullet_ColorA:\r\n                    return true;\r\n                default:\r\n                    return false;\r\n            }\r\n        }\r\n    }\r\n\r\n    getInitialValue(): number {\r\n        switch (this.data.actionType) {\r\n            case eEmitterActionType.Emitter_Active:\r\n                return this.getOwnerAsEmitter().status != eEmitterStatus.None ? 1 : 0;\r\n            case eEmitterActionType.Emitter_InitialDelay:\r\n                return this.getOwnerAsEmitter().initialDelay;\r\n            case eEmitterActionType.Emitter_PrewarmDuration:\r\n                return this.getOwnerAsEmitter().preWarmDuration;\r\n            case eEmitterActionType.Emitter_Duration:\r\n                return this.getOwnerAsEmitter().emitDuration;\r\n            case eEmitterActionType.Emitter_ElapsedTime:\r\n                return this.getOwnerAsEmitter().totalElapsedTime;\r\n            case eEmitterActionType.Emitter_LoopInterval:\r\n                return this.getOwnerAsEmitter().loopInterval;\r\n            case eEmitterActionType.Emitter_PerEmitInterval:\r\n                return this.getOwnerAsEmitter().perEmitInterval;\r\n            case eEmitterActionType.Emitter_PerEmitCount:\r\n                return this.getOwnerAsEmitter().perEmitCount;\r\n            case eEmitterActionType.Emitter_PerEmitOffsetX:\r\n                return this.getOwnerAsEmitter().perEmitOffsetX;\r\n            case eEmitterActionType.Emitter_Angle:\r\n                return this.getOwnerAsEmitter().angle;\r\n            case eEmitterActionType.Emitter_Count:\r\n                return this.getOwnerAsEmitter().count;\r\n\r\n            // 子弹部分\r\n            case eBulletActionType.Bullet_Duration:\r\n                return this.getOwnerAsBullet().duration;\r\n            case eBulletActionType.Bullet_ElapsedTime:\r\n                return this.getOwnerAsBullet().elapsedTime;\r\n            case eBulletActionType.Bullet_PosX:\r\n                return this.getOwnerAsBullet().node.getPosition().x;\r\n            case eBulletActionType.Bullet_PosY:\r\n                return this.getOwnerAsBullet().node.getPosition().y;\r\n            case eBulletActionType.Bullet_Damage:\r\n                return this.getOwnerAsBullet().damage;\r\n            case eBulletActionType.Bullet_Speed:\r\n                return this.getOwnerAsBullet().mover.speed;\r\n            case eBulletActionType.Bullet_SpeedAngle:\r\n                return this.getOwnerAsBullet().mover.speedAngle;\r\n            case eBulletActionType.Bullet_Acceleration:\r\n                return this.getOwnerAsBullet().mover.acceleration;\r\n            case eBulletActionType.Bullet_AccelerationAngle:\r\n                return this.getOwnerAsBullet().mover.accelerationAngle;\r\n            case eBulletActionType.Bullet_Scale:\r\n                return this.getOwnerAsBullet().node.getScale().x;\r\n            case eBulletActionType.Bullet_ColorR:\r\n                return this.getOwnerAsBullet().bulletSprite.color.r;\r\n            case eBulletActionType.Bullet_ColorG:\r\n                return this.getOwnerAsBullet().bulletSprite.color.g;\r\n            case eBulletActionType.Bullet_ColorB:\r\n                return this.getOwnerAsBullet().bulletSprite.color.b;\r\n            case eBulletActionType.Bullet_ColorA:\r\n                return this.getOwnerAsBullet().bulletSprite.color.a;\r\n            case eBulletActionType.Bullet_FaceMovingDir:\r\n                return this.getOwnerAsBullet().mover.isFacingMoveDir ? 1 : 0;\r\n            case eBulletActionType.Bullet_TrackingTarget:\r\n                return this.getOwnerAsBullet().mover.isTrackingTarget ? 1 : 0;\r\n            case eBulletActionType.Bullet_Destructive:\r\n                return this.getOwnerAsBullet().isDestructive ? 1 : 0;\r\n            case eBulletActionType.Bullet_DestructiveOnHit:\r\n                return this.getOwnerAsBullet().isDestructiveOnHit ? 1 : 0;\r\n\r\n            default: break;\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    getTargetValue(): number {\r\n        if (EventActionRunner.canLerp(this.data.actionType)) {\r\n            if (this.data.isRandom) {\r\n                return Math.random() * (this.data.maxValue - this.data.minValue) + this.data.minValue;\r\n            }\r\n            return this.data.minValue;\r\n        }\r\n\r\n        return this.data.boolValue ? 1 : 0;\r\n    }\r\n\r\n    start() {\r\n        if (!this.data) {\r\n            console.error('EventActionRunner: data is null');\r\n            return;\r\n        }\r\n        \r\n        this.startValue = this.getInitialValue();\r\n        this.targetValue = this.getTargetValue();\r\n        this.elapsedTime = 0;\r\n        this._isCompleted = false;\r\n    }\r\n\r\n    tick(dt: number) {\r\n        // Update current time\r\n        this.elapsedTime += dt;\r\n\r\n        if (this.elapsedTime >= this.data.duration) {\r\n            this._isCompleted = true;\r\n            this.apply(this.targetValue);\r\n        }\r\n        else {\r\n            if (EventActionRunner.canLerp(this.data.actionType)) {\r\n                const currentValue = this.lerpValue(this.startValue, this.targetValue);\r\n                this.apply(currentValue);\r\n            }\r\n        }\r\n    }\r\n\r\n    lerpValue(startValue: number, targetValue: number): number {\r\n        // Calculate progress ratio (0 to 1)\r\n        let progress = Math.min(1.0, this.elapsedTime / this.data.duration);\r\n        // Apply easing function\r\n        let easedProgress = EventActionRunner.applyEasing(this.data.easing, progress);\r\n        // Interpolate between start value and target value\r\n        return startValue + (targetValue - startValue) * easedProgress;\r\n    }\r\n\r\n    apply(currentValue: number) {\r\n        if (this.data.actionType < eBulletActionType.Bullet_Duration) {\r\n            this.getOwnerAsEmitter().applyAction(this.data.actionType as eEmitterActionType, currentValue);\r\n        }\r\n        else {\r\n            this.getOwnerAsBullet().applyAction(this.data.actionType as eBulletActionType, currentValue);\r\n        }\r\n    }\r\n    \r\n    static applyEasing(easing: eEasing, t: number): number {\r\n        switch (easing) {\r\n            case eEasing.Linear:\r\n                return t;\r\n\r\n            case eEasing.InSine:\r\n                return 1 - Math.cos(t * Math.PI / 2);\r\n\r\n            case eEasing.OutSine:\r\n                return Math.sin(t * Math.PI / 2);\r\n\r\n            case eEasing.InOutSine:\r\n                return -(Math.cos(Math.PI * t) - 1) / 2;\r\n\r\n            case eEasing.InQuad:\r\n                return t * t;\r\n\r\n            case eEasing.OutQuad:\r\n                return 1 - (1 - t) * (1 - t);\r\n\r\n            case eEasing.InOutQuad:\r\n                return t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2;\r\n\r\n            default:\r\n                return t;\r\n        }\r\n    }\r\n}\r\n\r\nexport class EventGroupRunner {\r\n    readonly data: EmitterGroupData | BulletGroupData;\r\n    owner: Emitter | Bullet = null;\r\n    isTriggered: boolean = false;\r\n    isConditionAMet: boolean = false;\r\n    isConditionBMet: boolean = false;\r\n\r\n    constructor(data: EmitterGroupData | BulletGroupData) {\r\n        this.data = data;\r\n    }\r\n\r\n    public init(owner: Emitter | Bullet) {\r\n        this.owner = owner;\r\n        this.isTriggered = this.isConditionAMet = this.isConditionBMet = false;\r\n        this.registerListeners();\r\n    }\r\n\r\n    private tryTrigger() {\r\n        switch (this.data.groupOp) {\r\n            case eConditionGroupOp.And:\r\n                this.isTriggered = this.isConditionAMet && this.isConditionBMet;\r\n                break;\r\n            case eConditionGroupOp.Or:\r\n                this.isTriggered = this.isConditionAMet || this.isConditionBMet;\r\n                break;\r\n        }\r\n\r\n        if (this.isTriggered) {\r\n            for (const action of this.data.actions) {\r\n                BulletSystem.createActionRunner(this.owner, action);\r\n            }\r\n            this.unRegisterListeners();\r\n        }\r\n    }\r\n\r\n    private registerListeners() {\r\n        if (this.data.conditionA) {\r\n            this.isConditionAMet = false;\r\n            BulletSystem.on(this.data.conditionA.eventType, this.onConditionAChanged.bind(this));\r\n        }\r\n        else {\r\n            this.isConditionAMet = true;\r\n        }\r\n\r\n        if (this.data.conditionB) {\r\n            this.isConditionBMet = false;\r\n            BulletSystem.on(this.data.conditionB.eventType, this.onConditionBChanged.bind(this));\r\n        }\r\n        else {\r\n            this.isConditionBMet = true;\r\n        }\r\n    }\r\n\r\n    private unRegisterListeners() {\r\n        if (this.data.conditionA) {\r\n            BulletSystem.off(this.data.conditionA.eventType, this.onConditionAChanged.bind(this));\r\n        }\r\n\r\n        if (this.data.conditionB) {\r\n            BulletSystem.off(this.data.conditionB.eventType, this.onConditionBChanged.bind(this));\r\n        }\r\n    }\r\n\r\n    private onConditionAChanged(value: number) {\r\n        // Handle condition A change\r\n        this.isConditionAMet = EventGroupRunner.compareValue(this.data.conditionA.eventOp, value, this.data.conditionA.targetValue);\r\n        this.tryTrigger();\r\n    }\r\n\r\n    private onConditionBChanged(value: number) {\r\n        // Handle condition B change\r\n        this.isConditionBMet = EventGroupRunner.compareValue(this.data.conditionB.eventOp, value, this.data.conditionB.targetValue);\r\n        this.tryTrigger();\r\n    }\r\n\r\n\r\n    static compareValue(op: eEventConditionOp, a: number, b: number): boolean {\r\n        switch (op) {\r\n            case eEventConditionOp.Equal:\r\n                return a === b;\r\n            case eEventConditionOp.NotEqual:\r\n                return a !== b;\r\n            case eEventConditionOp.Greater:\r\n                return a > b;\r\n            case eEventConditionOp.Less:\r\n                return a < b;\r\n            case eEventConditionOp.GreaterEqual:\r\n                return a >= b;\r\n            case eEventConditionOp.LessEqual:\r\n                return a <= b;\r\n            default:\r\n                return false;\r\n        }\r\n    }\r\n}"]}