import { _decorator } from 'cc';
import { ActionValue } from './ActionValue';
const { ccclass } = _decorator;

/**
 * Interface for handling specific action types
 */
export interface IActionHandler<T = ActionValue> {
    readonly actionType: number;
    readonly valueType: 'number' | 'boolean' | 'string' | 'rpn';
    
    /**
     * Get the current value from the target object
     */
    getValue(target: any): T;
    
    /**
     * Set the value on the target object
     */
    setValue(target: any, value: T): void;
    
    /**
     * Whether this action type supports smooth interpolation
     */
    canInterpolate(): boolean;
    
    /**
     * Optional: Get a human-readable name for this action
     */
    getDisplayName?(): string;
    
    /**
     * Optional: Validate if the target object is compatible with this handler
     */
    isValidTarget?(target: any): boolean;
}

/**
 * Base class for action handlers providing common functionality
 */
export abstract class BaseActionHandler<T = ActionValue> implements IActionHandler<T> {
    constructor(
        public readonly actionType: number,
        public readonly valueType: 'number' | 'boolean' | 'string' | 'rpn',
        private displayName?: string
    ) {}
    
    abstract getValue(target: any): T;
    abstract setValue(target: any, value: T): void;
    abstract canInterpolate(): boolean;
    
    getDisplayName(): string {
        return this.displayName || `Action_${this.actionType}`;
    }
    
    isValidTarget(target: any): boolean {
        // Default implementation - can be overridden by specific handlers
        return target != null;
    }
    
    /**
     * Helper method to safely get a property from target
     */
    protected safeGetProperty(target: any, propertyPath: string): any {
        try {
            const parts = propertyPath.split('.');
            let current = target;
            for (const part of parts) {
                if (current == null) return undefined;
                current = current[part];
            }
            return current;
        } catch (error) {
            console.warn(`Failed to get property ${propertyPath} from target:`, error);
            return undefined;
        }
    }
    
    /**
     * Helper method to safely set a property on target
     */
    protected safeSetProperty(target: any, propertyPath: string, value: any): boolean {
        try {
            const parts = propertyPath.split('.');
            let current = target;
            
            // Navigate to the parent object
            for (let i = 0; i < parts.length - 1; i++) {
                if (current == null) return false;
                current = current[parts[i]];
            }
            
            if (current == null) return false;
            
            // Set the final property
            const finalProperty = parts[parts.length - 1];
            current[finalProperty] = value;
            return true;
        } catch (error) {
            console.warn(`Failed to set property ${propertyPath} on target:`, error);
            return false;
        }
    }
}
