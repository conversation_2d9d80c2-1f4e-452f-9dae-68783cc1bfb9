{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/actions/ActionRegistry.ts"], "names": ["ActionRegistry", "_decorator", "EmitterActiveHandler", "EmitterInitialDelayHandler", "Emitter<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EmitterPrewarmDurationHandler", "EmitterDurationHandler", "EmitterElapsedTimeHandler", "Emitter<PERSON><PERSON><PERSON><PERSON><PERSON>", "EmitterLoopIntervalHandler", "EmitterPerEmitIntervalHandler", "EmitterPerEmitCountHandler", "EmitterPerEmitOffsetXHandler", "Emitter<PERSON><PERSON><PERSON><PERSON><PERSON>", "EmitterCount<PERSON>andler", "BulletDurationHandler", "BulletElapsedTimeHandler", "BulletPosXHandler", "BulletPosYHandler", "BulletDamageHandler", "BulletSpeedHandler", "BulletSpeedAngleHandler", "BulletAccelerationHandler", "BulletAccelerationAngleHandler", "BulletScaleHandler", "BulletColorRHandler", "BulletColorGHandler", "BulletColorBHandler", "BulletColorAHandler", "BulletFaceMovingDirHandler", "BulletTrackingTargetHandler", "BulletDestructiveHandler", "BulletDestructiveOnHitHandler", "ccclass", "initialize", "initialized", "console", "warn", "log", "register", "handlers", "size", "handler", "has", "actionType", "set", "getDisplayName", "<PERSON><PERSON><PERSON><PERSON>", "error", "get", "getAllHandlers", "Array", "from", "values", "getHandlersByValueType", "valueType", "filter", "<PERSON><PERSON><PERSON><PERSON>", "getHandlerCount", "clear", "getDebugInfo", "info", "map", "join", "Map"], "mappings": ";;;u1BA0CaA,c;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA1CJC,MAAAA,U,OAAAA,U;;AAGLC,MAAAA,oB,iBAAAA,oB;AACAC,MAAAA,0B,iBAAAA,0B;AACAC,MAAAA,qB,iBAAAA,qB;AACAC,MAAAA,6B,iBAAAA,6B;AACAC,MAAAA,sB,iBAAAA,sB;AACAC,MAAAA,yB,iBAAAA,yB;AACAC,MAAAA,kB,iBAAAA,kB;AACAC,MAAAA,0B,iBAAAA,0B;AACAC,MAAAA,6B,iBAAAA,6B;AACAC,MAAAA,0B,iBAAAA,0B;AACAC,MAAAA,4B,iBAAAA,4B;AACAC,MAAAA,mB,iBAAAA,mB;AACAC,MAAAA,mB,iBAAAA,mB;;AAGAC,MAAAA,qB,iBAAAA,qB;AACAC,MAAAA,wB,iBAAAA,wB;AACAC,MAAAA,iB,iBAAAA,iB;AACAC,MAAAA,iB,iBAAAA,iB;AACAC,MAAAA,mB,iBAAAA,mB;AACAC,MAAAA,kB,iBAAAA,kB;AACAC,MAAAA,uB,iBAAAA,uB;AACAC,MAAAA,yB,iBAAAA,yB;AACAC,MAAAA,8B,iBAAAA,8B;AACAC,MAAAA,kB,iBAAAA,kB;AACAC,MAAAA,mB,iBAAAA,mB;AACAC,MAAAA,mB,iBAAAA,mB;AACAC,MAAAA,mB,iBAAAA,mB;AACAC,MAAAA,mB,iBAAAA,mB;AACAC,MAAAA,0B,iBAAAA,0B;AACAC,MAAAA,2B,iBAAAA,2B;AACAC,MAAAA,wB,iBAAAA,wB;AACAC,MAAAA,6B,iBAAAA,6B;;;;;;;;;OAEE;AAAEC,QAAAA;AAAF,O,GAAchC,U;AAEpB;AACA;AACA;;gCACaD,c,GAAN,MAAMA,cAAN,CAAqB;AAIxB;AACJ;AACA;AACqB,eAAVkC,UAAU,GAAG;AAChB,cAAI,KAAKC,WAAT,EAAsB;AAClBC,YAAAA,OAAO,CAACC,IAAR,CAAa,oCAAb;AACA;AACH;;AAEDD,UAAAA,OAAO,CAACE,GAAR,CAAY,gCAAZ,EANgB,CAQhB;;AACA,eAAKC,QAAL,CAAc;AAAA;AAAA,6DAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,yEAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,+DAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,+EAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,iEAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,uEAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,yDAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,yEAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,+EAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,yEAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,6EAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,2DAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,2DAAd,EArBgB,CAuBhB;;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,+DAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,qEAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,uDAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,uDAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,2DAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,yDAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,mEAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,uEAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,iFAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,yDAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,2DAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,2DAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,2DAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,2DAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,yEAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,2EAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,qEAAd;AACA,eAAKA,QAAL,CAAc;AAAA;AAAA,+EAAd;AAEA,eAAKJ,WAAL,GAAmB,IAAnB;AACAC,UAAAA,OAAO,CAACE,GAAR,CAAa,mCAAkC,KAAKE,QAAL,CAAcC,IAAK,WAAlE;AACH;AAED;AACJ;AACA;;;AACmB,eAARF,QAAQ,CAACG,OAAD,EAA0B;AACrC,cAAI,KAAKF,QAAL,CAAcG,GAAd,CAAkBD,OAAO,CAACE,UAA1B,CAAJ,EAA2C;AACvCR,YAAAA,OAAO,CAACC,IAAR,CAAc,2BAA0BK,OAAO,CAACE,UAAW,8BAA3D;AACH;;AAED,eAAKJ,QAAL,CAAcK,GAAd,CAAkBH,OAAO,CAACE,UAA1B,EAAsCF,OAAtC;AACAN,UAAAA,OAAO,CAACE,GAAR,CAAa,uBAAsBI,OAAO,CAACI,cAA/B,oBAAuBJ,OAAO,CAACI,cAAR,EAA2B,KAAIJ,OAAO,CAACE,UAAW,GAArF;AACH;AAED;AACJ;AACA;;;AACqB,eAAVG,UAAU,CAACH,UAAD,EAA4C;AACzD,cAAI,CAAC,KAAKT,WAAV,EAAuB;AACnBC,YAAAA,OAAO,CAACY,KAAR,CAAc,yEAAd;AACA,mBAAO,IAAP;AACH;;AAED,iBAAO,KAAKR,QAAL,CAAcS,GAAd,CAAkBL,UAAlB,KAAiC,IAAxC;AACH;AAED;AACJ;AACA;;;AACyB,eAAdM,cAAc,GAAqB;AACtC,iBAAOC,KAAK,CAACC,IAAN,CAAW,KAAKZ,QAAL,CAAca,MAAd,EAAX,CAAP;AACH;AAED;AACJ;AACA;;;AACiC,eAAtBC,sBAAsB,CAACC,SAAD,EAAuE;AAChG,iBAAO,KAAKL,cAAL,GAAsBM,MAAtB,CAA6Bd,OAAO,IAAIA,OAAO,CAACa,SAAR,KAAsBA,SAA9D,CAAP;AACH;AAED;AACJ;AACA;;;AACqB,eAAVE,UAAU,CAACb,UAAD,EAA8B;AAC3C,iBAAO,KAAKJ,QAAL,CAAcG,GAAd,CAAkBC,UAAlB,CAAP;AACH;AAED;AACJ;AACA;;;AAC0B,eAAfc,eAAe,GAAW;AAC7B,iBAAO,KAAKlB,QAAL,CAAcC,IAArB;AACH;AAED;AACJ;AACA;;;AACgB,eAALkB,KAAK,GAAG;AACX,eAAKnB,QAAL,CAAcmB,KAAd;AACA,eAAKxB,WAAL,GAAmB,KAAnB;AACAC,UAAAA,OAAO,CAACE,GAAR,CAAY,wBAAZ;AACH;AAED;AACJ;AACA;;;AACuB,eAAZsB,YAAY,GAAW;AAC1B,gBAAMpB,QAAQ,GAAG,KAAKU,cAAL,EAAjB;AACA,gBAAMW,IAAI,GAAGrB,QAAQ,CAACsB,GAAT,CAAapB,OAAO,IAC5B,GAAEA,OAAO,CAACE,UAAW,KAAIF,OAAO,CAACI,cAAlC,oBAA0BJ,OAAO,CAACI,cAAR,EAA2B,KAAIJ,OAAO,CAACa,SAAU,GADlE,EAEXQ,IAFW,CAEN,IAFM,CAAb;AAIA,iBAAQ,+BAA8BF,IAAK,EAA3C;AACH;;AA7HuB,O;;AAAf7D,MAAAA,c,CACMwC,Q,GAAW,IAAIwB,GAAJ,E;AADjBhE,MAAAA,c,CAEMmC,W,GAAc,K", "sourcesContent": ["import { _decorator } from 'cc';\nimport { IAction<PERSON><PERSON><PERSON> } from './IActionHandler';\nimport { \n    Emitter<PERSON><PERSON><PERSON>and<PERSON>, \n    EmitterInitial<PERSON><PERSON>y<PERSON><PERSON><PERSON>, \n    EmitterPrewarm<PERSON><PERSON><PERSON>,\n    EmitterPrewarmDuration<PERSON><PERSON><PERSON>,\n    <PERSON>itter<PERSON><PERSON><PERSON><PERSON><PERSON>,\n    <PERSON>itter<PERSON><PERSON>sed<PERSON><PERSON><PERSON><PERSON><PERSON>,\n    EmitterLoop<PERSON><PERSON><PERSON>,\n    EmitterLoopIntervalHandler,\n    EmitterPerEmitIntervalHandler,\n    EmitterPerEmitCountHandler,\n    EmitterPerEmitOffset<PERSON><PERSON><PERSON><PERSON>,\n    <PERSON>itter<PERSON><PERSON><PERSON><PERSON><PERSON>,\n    EmitterCountHandler\n} from './EmitterActionHandlers';\nimport { \n    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n    <PERSON>etElapsedTimeHandler,\n    BulletPosXHandler,\n    BulletPosYHandler,\n    BulletDamageHandler,\n    BulletSpeedHandler,\n    BulletSpeedAngleHandler,\n    BulletAccelerationHandler,\n    BulletAccelerationAngleHand<PERSON>,\n    <PERSON>etScaleHand<PERSON>,\n    <PERSON>etColorR<PERSON><PERSON><PERSON>,\n    <PERSON>etColorG<PERSON><PERSON><PERSON>,\n    <PERSON>et<PERSON>olor<PERSON><PERSON><PERSON><PERSON>,\n    <PERSON>et<PERSON>olor<PERSON><PERSON><PERSON><PERSON>,\n    BulletFaceMovingDirHandler,\n    BulletTrackingTargetHandler,\n    BulletDestructiveHandler,\n    BulletDestructiveOnHitHandler\n} from './BulletActionHandlers';\nconst { ccclass } = _decorator;\n\n/**\n * Registry for managing action handlers\n */\nexport class ActionRegistry {\n    private static handlers = new Map<number, IActionHandler>();\n    private static initialized = false;\n    \n    /**\n     * Initialize the registry with all available handlers\n     */\n    static initialize() {\n        if (this.initialized) {\n            console.warn('ActionRegistry already initialized');\n            return;\n        }\n        \n        console.log('Initializing ActionRegistry...');\n        \n        // Register emitter handlers\n        this.register(new EmitterActiveHandler());\n        this.register(new EmitterInitialDelayHandler());\n        this.register(new EmitterPrewarmHandler());\n        this.register(new EmitterPrewarmDurationHandler());\n        this.register(new EmitterDurationHandler());\n        this.register(new EmitterElapsedTimeHandler());\n        this.register(new EmitterLoopHandler());\n        this.register(new EmitterLoopIntervalHandler());\n        this.register(new EmitterPerEmitIntervalHandler());\n        this.register(new EmitterPerEmitCountHandler());\n        this.register(new EmitterPerEmitOffsetXHandler());\n        this.register(new EmitterAngleHandler());\n        this.register(new EmitterCountHandler());\n        \n        // Register bullet handlers\n        this.register(new BulletDurationHandler());\n        this.register(new BulletElapsedTimeHandler());\n        this.register(new BulletPosXHandler());\n        this.register(new BulletPosYHandler());\n        this.register(new BulletDamageHandler());\n        this.register(new BulletSpeedHandler());\n        this.register(new BulletSpeedAngleHandler());\n        this.register(new BulletAccelerationHandler());\n        this.register(new BulletAccelerationAngleHandler());\n        this.register(new BulletScaleHandler());\n        this.register(new BulletColorRHandler());\n        this.register(new BulletColorGHandler());\n        this.register(new BulletColorBHandler());\n        this.register(new BulletColorAHandler());\n        this.register(new BulletFaceMovingDirHandler());\n        this.register(new BulletTrackingTargetHandler());\n        this.register(new BulletDestructiveHandler());\n        this.register(new BulletDestructiveOnHitHandler());\n        \n        this.initialized = true;\n        console.log(`ActionRegistry initialized with ${this.handlers.size} handlers`);\n    }\n    \n    /**\n     * Register a new action handler\n     */\n    static register(handler: IActionHandler) {\n        if (this.handlers.has(handler.actionType)) {\n            console.warn(`Handler for action type ${handler.actionType} already exists, overwriting`);\n        }\n        \n        this.handlers.set(handler.actionType, handler);\n        console.log(`Registered handler: ${handler.getDisplayName?.()} (${handler.actionType})`);\n    }\n    \n    /**\n     * Get a handler for a specific action type\n     */\n    static getHandler(actionType: number): IActionHandler | null {\n        if (!this.initialized) {\n            console.error('ActionRegistry not initialized. Call ActionRegistry.initialize() first.');\n            return null;\n        }\n        \n        return this.handlers.get(actionType) || null;\n    }\n    \n    /**\n     * Get all registered handlers\n     */\n    static getAllHandlers(): IActionHandler[] {\n        return Array.from(this.handlers.values());\n    }\n    \n    /**\n     * Get handlers by value type\n     */\n    static getHandlersByValueType(valueType: 'number' | 'boolean' | 'string' | 'rpn'): IActionHandler[] {\n        return this.getAllHandlers().filter(handler => handler.valueType === valueType);\n    }\n    \n    /**\n     * Check if a handler exists for an action type\n     */\n    static hasHandler(actionType: number): boolean {\n        return this.handlers.has(actionType);\n    }\n    \n    /**\n     * Get the number of registered handlers\n     */\n    static getHandlerCount(): number {\n        return this.handlers.size;\n    }\n    \n    /**\n     * Clear all handlers (useful for testing)\n     */\n    static clear() {\n        this.handlers.clear();\n        this.initialized = false;\n        console.log('ActionRegistry cleared');\n    }\n    \n    /**\n     * Get debug information about registered handlers\n     */\n    static getDebugInfo(): string {\n        const handlers = this.getAllHandlers();\n        const info = handlers.map(handler => \n            `${handler.actionType}: ${handler.getDisplayName?.()} (${handler.valueType})`\n        ).join('\\n');\n        \n        return `ActionRegistry Debug Info:\\n${info}`;\n    }\n}\n"]}