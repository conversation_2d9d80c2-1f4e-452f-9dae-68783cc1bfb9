System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Button, EditBox, csproto, GameInstance, DevLoginData, logDebug, logError, BaseUI, UILayer, UIMgr, uiSelect, _dec, _dec2, _dec3, _dec4, _dec5, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _crd, ccclass, property, DevLoginUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "../AutoGen/PB/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameInstance(extras) {
    _reporterNs.report("GameInstance", "../GameInstance", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDevLoginData(extras) {
    _reporterNs.report("DevLoginData", "../PlatformSDK/DevLoginData", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogDebug(extras) {
    _reporterNs.report("logDebug", "../Utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogError(extras) {
    _reporterNs.report("logError", "../Utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "./UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "./UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "./UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfuiSelect(extras) {
    _reporterNs.report("uiSelect", "./common/components/SelectList/uiSelect", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Button = _cc.Button;
      EditBox = _cc.EditBox;
    }, function (_unresolved_2) {
      csproto = _unresolved_2.default;
    }, function (_unresolved_3) {
      GameInstance = _unresolved_3.GameInstance;
    }, function (_unresolved_4) {
      DevLoginData = _unresolved_4.DevLoginData;
    }, function (_unresolved_5) {
      logDebug = _unresolved_5.logDebug;
      logError = _unresolved_5.logError;
    }, function (_unresolved_6) {
      BaseUI = _unresolved_6.BaseUI;
      UILayer = _unresolved_6.UILayer;
      UIMgr = _unresolved_6.UIMgr;
    }, function (_unresolved_7) {
      uiSelect = _unresolved_7.uiSelect;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "47d87AxIPVKZrWD7+ErDzVt", "DevLoginUI", undefined);

      __checkObsolete__(['_decorator', 'Button', 'EditBox']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("DevLoginUI", DevLoginUI = (_dec = ccclass('DevLoginUI'), _dec2 = property(Button), _dec3 = property(EditBox), _dec4 = property(EditBox), _dec5 = property(_crd && uiSelect === void 0 ? (_reportPossibleCrUseOfuiSelect({
        error: Error()
      }), uiSelect) : uiSelect), _dec(_class = (_class2 = class DevLoginUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "loginButton", _descriptor, this);

          _initializerDefineProperty(this, "usernameEditBox", _descriptor2, this);

          _initializerDefineProperty(this, "passwordEditBox", _descriptor3, this);

          _initializerDefineProperty(this, "serverSelect", _descriptor4, this);

          this._onGetRoleBound = this.onGetRole.bind(this);
        }

        static getUrl() {
          return "ui/DevLoginUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Top;
        }

        async onHide(...args) {}

        async onShow(...args) {
          this.loginButton.node.on(Button.EventType.CLICK, this.onLoginButtonClick, this);
          (_crd && GameInstance === void 0 ? (_reportPossibleCrUseOfGameInstance({
            error: Error()
          }), GameInstance) : GameInstance).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_ROLE, this._onGetRoleBound);
          (_crd && DevLoginData === void 0 ? (_reportPossibleCrUseOfDevLoginData({
            error: Error()
          }), DevLoginData) : DevLoginData).serverList.forEach((value, key) => {
            this.serverSelect.itemDatas.push(key);
            this.serverSelect.itemDatas.push(key + "1");
          });
          this.serverSelect.setChooseItemData((_crd && DevLoginData === void 0 ? (_reportPossibleCrUseOfDevLoginData({
            error: Error()
          }), DevLoginData) : DevLoginData).instance.servername);

          this.serverSelect.onChooseItem = itemData => {
            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)("LoginUI", `choose server ${itemData}`);
            (_crd && DevLoginData === void 0 ? (_reportPossibleCrUseOfDevLoginData({
              error: Error()
            }), DevLoginData) : DevLoginData).instance.servername = itemData;
          };

          this.usernameEditBox.string = (_crd && DevLoginData === void 0 ? (_reportPossibleCrUseOfDevLoginData({
            error: Error()
          }), DevLoginData) : DevLoginData).instance.user;
          this.passwordEditBox.string = (_crd && DevLoginData === void 0 ? (_reportPossibleCrUseOfDevLoginData({
            error: Error()
          }), DevLoginData) : DevLoginData).instance.password;
        }

        async onClose(...args) {
          (_crd && GameInstance === void 0 ? (_reportPossibleCrUseOfGameInstance({
            error: Error()
          }), GameInstance) : GameInstance).netMgr.unregisterHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_ROLE, this._onGetRoleBound);
        }

        onLoginButtonClick() {
          var username = this.usernameEditBox.string;
          var password = this.passwordEditBox.string;
          (_crd && DevLoginData === void 0 ? (_reportPossibleCrUseOfDevLoginData({
            error: Error()
          }), DevLoginData) : DevLoginData).instance.user = username;
          (_crd && DevLoginData === void 0 ? (_reportPossibleCrUseOfDevLoginData({
            error: Error()
          }), DevLoginData) : DevLoginData).instance.password = password;
          (_crd && GameInstance === void 0 ? (_reportPossibleCrUseOfGameInstance({
            error: Error()
          }), GameInstance) : GameInstance).platformSDK.login((err, info) => {
            if (err) {
              (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
                error: Error()
              }), logError) : logError)("DevLoginUI", `login failed ${err}`);
              return;
            }

            (_crd && GameInstance === void 0 ? (_reportPossibleCrUseOfGameInstance({
              error: Error()
            }), GameInstance) : GameInstance).netMgr.login(info);
          });
        }

        onGetRole(msg) {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).closeUI(DevLoginUI);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "loginButton", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "usernameEditBox", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "passwordEditBox", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "serverSelect", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=fe228aac53b3e4a94ae203707a653baa732fc5ed.js.map