System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, InterpolatorFactory, ActionRegistry, eEasing, GenericActionRunner, _crd, ccclass;

  function _reportPossibleCrUseOfActionValue(extras) {
    _reporterNs.report("ActionValue", "./ActionValue", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIValueInterpolator(extras) {
    _reporterNs.report("IValueInterpolator", "./ActionValue", _context.meta, extras);
  }

  function _reportPossibleCrUseOfInterpolatorFactory(extras) {
    _reporterNs.report("InterpolatorFactory", "./ActionValue", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIActionHandler(extras) {
    _reporterNs.report("IActionHandler", "./IActionHandler", _context.meta, extras);
  }

  function _reportPossibleCrUseOfActionRegistry(extras) {
    _reporterNs.report("ActionRegistry", "./ActionRegistry", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEasing(extras) {
    _reporterNs.report("eEasing", "../../move/IMovable", _context.meta, extras);
  }

  _export("GenericActionRunner", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      InterpolatorFactory = _unresolved_2.InterpolatorFactory;
    }, function (_unresolved_3) {
      ActionRegistry = _unresolved_3.ActionRegistry;
    }, function (_unresolved_4) {
      eEasing = _unresolved_4.eEasing;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "c499b4A3RVJlrUSWybuW+nD", "GenericActionRunner", undefined);

      __checkObsolete__(['_decorator']);

      ({
        ccclass
      } = _decorator);
      /**
       * Generic action runner that can handle any action type through the registry system
       */

      _export("GenericActionRunner", GenericActionRunner = class GenericActionRunner {
        get isCompleted() {
          return this._isCompleted;
        }

        get actionType() {
          var _this$handler;

          return ((_this$handler = this.handler) == null ? void 0 : _this$handler.actionType) || -1;
        }

        get valueType() {
          var _this$handler2;

          return ((_this$handler2 = this.handler) == null ? void 0 : _this$handler2.valueType) || 'unknown';
        }

        constructor(owner, actionTypeId, duration, easing, targetVal, isRandom = false, minValue, maxValue) {
          this.startValue = 0;
          this.targetValue = 0;
          this.elapsedTime = 0;
          this.isRunning = false;
          this._isCompleted = false;
          this.handler = void 0;
          this.interpolator = void 0;
          this.owner = owner;
          this.actionTypeId = actionTypeId;
          this.duration = duration;
          this.easing = easing;
          this.targetVal = targetVal;
          this.isRandom = isRandom;
          this.minValue = minValue;
          this.maxValue = maxValue;
          this.handler = (_crd && ActionRegistry === void 0 ? (_reportPossibleCrUseOfActionRegistry({
            error: Error()
          }), ActionRegistry) : ActionRegistry).getHandler(actionTypeId);

          if (!this.handler) {
            throw new Error(`No handler found for action type: ${actionTypeId}`);
          } // Validate target compatibility


          if (this.handler.isValidTarget && !this.handler.isValidTarget(owner)) {
            throw new Error(`Target object is not compatible with action type: ${actionTypeId}`);
          }

          this.interpolator = (_crd && InterpolatorFactory === void 0 ? (_reportPossibleCrUseOfInterpolatorFactory({
            error: Error()
          }), InterpolatorFactory) : InterpolatorFactory).create(this.handler.valueType);
        }
        /**
         * Start the action execution
         */


        start() {
          try {
            var _this$handler$getDisp, _this$handler3;

            this.startValue = this.handler.getValue(this.owner);
            this.targetValue = this.calculateTargetValue();
            this.elapsedTime = 0;
            this.isRunning = true;
            this._isCompleted = false;
            console.log(`Started action ${(_this$handler$getDisp = (_this$handler3 = this.handler).getDisplayName) == null ? void 0 : _this$handler$getDisp.call(_this$handler3)}: ${this.startValue} -> ${this.targetValue}`);
          } catch (error) {
            console.error(`Failed to start action ${this.actionTypeId}:`, error);
            this._isCompleted = true;
            this.isRunning = false;
          }
        }
        /**
         * Calculate the target value based on configuration
         */


        calculateTargetValue() {
          if (this.isRandom && this.minValue !== undefined && this.maxValue !== undefined) {
            return this.interpolator.getRandomValue(this.minValue, this.maxValue);
          }

          return this.targetVal;
        }
        /**
         * Update the action (called each frame)
         */


        tick(dt) {
          if (!this.isRunning || this._isCompleted) {
            return;
          }

          this.elapsedTime += dt;

          if (this.elapsedTime >= this.duration) {
            // Action completed
            this.isRunning = false;
            this._isCompleted = true;
            this.apply(this.targetValue);
          } else if (this.handler.canInterpolate() && this.interpolator.canInterpolate()) {
            // Interpolate between start and target values
            const progress = Math.min(1.0, this.elapsedTime / this.duration);
            const easedProgress = this.applyEasing(this.easing, progress);
            const currentValue = this.interpolator.interpolate(this.startValue, this.targetValue, easedProgress);
            this.apply(currentValue);
          } else if (this.duration === 0) {
            // Instant action
            this.isRunning = false;
            this._isCompleted = true;
            this.apply(this.targetValue);
          }
        }
        /**
         * Apply the current value to the target object
         */


        apply(value) {
          try {
            this.handler.setValue(this.owner, value);
          } catch (error) {
            console.error(`Failed to apply action ${this.actionTypeId} with value ${value}:`, error);
            this._isCompleted = true;
            this.isRunning = false;
          }
        }
        /**
         * Apply easing function to progress
         */


        applyEasing(easing, t) {
          switch (easing) {
            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).Linear:
              return t;

            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).InSine:
              return 1 - Math.cos(t * Math.PI / 2);

            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).OutSine:
              return Math.sin(t * Math.PI / 2);

            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).InOutSine:
              return -(Math.cos(Math.PI * t) - 1) / 2;

            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).InQuad:
              return t * t;

            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).OutQuad:
              return 1 - (1 - t) * (1 - t);

            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).InOutQuad:
              return t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2;

            default:
              return t;
          }
        }
        /**
         * Force complete the action immediately
         */


        forceComplete() {
          if (!this._isCompleted) {
            this.apply(this.targetValue);
            this._isCompleted = true;
            this.isRunning = false;
          }
        }
        /**
         * Cancel the action without applying the target value
         */


        cancel() {
          this._isCompleted = true;
          this.isRunning = false;
        }
        /**
         * Get debug information about this action runner
         */


        getDebugInfo() {
          var _this$handler4;

          return `GenericActionRunner {
            actionType: ${this.actionTypeId} (${(_this$handler4 = this.handler) == null || _this$handler4.getDisplayName == null ? void 0 : _this$handler4.getDisplayName()}),
            valueType: ${this.valueType},
            startValue: ${this.startValue},
            targetValue: ${this.targetValue},
            elapsedTime: ${this.elapsedTime.toFixed(3)}s,
            duration: ${this.duration}s,
            isRunning: ${this.isRunning},
            isCompleted: ${this._isCompleted}
        }`;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b2bdb8d2806a105a57b92dda7afdf2a0d48922a5.js.map