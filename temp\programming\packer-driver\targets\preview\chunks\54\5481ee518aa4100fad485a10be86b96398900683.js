System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, BaseActionHandler, eEmitterActionType, Emitter, eEmitterStatus, EmitterActiveHandler, EmitterInitial<PERSON><PERSON>y<PERSON><PERSON><PERSON>, <PERSON>itter<PERSON>rew<PERSON><PERSON><PERSON><PERSON>, EmitterPrewarmDuration<PERSON>and<PERSON>, EmitterDurationHandler, EmitterElapsedTimeHandler, <PERSON>itter<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>itterLoopIntervalHandler, EmitterPerEmitIntervalH<PERSON><PERSON>, EmitterPerEmitCountHandler, EmitterPerEmitOffset<PERSON>H<PERSON><PERSON>, <PERSON>itter<PERSON>ngle<PERSON><PERSON>ler, EmitterCountHandler, _crd, ccclass;

  function _reportPossibleCrUseOfBaseActionHandler(extras) {
    _reporterNs.report("BaseActionHandler", "./IActionHandler", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEmitterActionType(extras) {
    _reporterNs.report("eEmitterActionType", "../../data/EventActionData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitter(extras) {
    _reporterNs.report("Emitter", "../Emitter", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEmitterStatus(extras) {
    _reporterNs.report("eEmitterStatus", "../Emitter", _context.meta, extras);
  }

  _export({
    EmitterActiveHandler: void 0,
    EmitterInitialDelayHandler: void 0,
    EmitterPrewarmHandler: void 0,
    EmitterPrewarmDurationHandler: void 0,
    EmitterDurationHandler: void 0,
    EmitterElapsedTimeHandler: void 0,
    EmitterLoopHandler: void 0,
    EmitterLoopIntervalHandler: void 0,
    EmitterPerEmitIntervalHandler: void 0,
    EmitterPerEmitCountHandler: void 0,
    EmitterPerEmitOffsetXHandler: void 0,
    EmitterAngleHandler: void 0,
    EmitterCountHandler: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      BaseActionHandler = _unresolved_2.BaseActionHandler;
    }, function (_unresolved_3) {
      eEmitterActionType = _unresolved_3.eEmitterActionType;
    }, function (_unresolved_4) {
      Emitter = _unresolved_4.Emitter;
      eEmitterStatus = _unresolved_4.eEmitterStatus;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "1cfc6Dp5upITbXIWRhA3qNV", "EmitterActionHandlers", undefined);

      __checkObsolete__(['_decorator']);

      ({
        ccclass
      } = _decorator);
      /**
       * Handler for Emitter Active state
       */

      _export("EmitterActiveHandler", EmitterActiveHandler = class EmitterActiveHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
            error: Error()
          }), eEmitterActionType) : eEmitterActionType).Emitter_Active, 'boolean', 'Emitter Active');
        }

        getValue(emitter) {
          return emitter.status !== (_crd && eEmitterStatus === void 0 ? (_reportPossibleCrUseOfeEmitterStatus({
            error: Error()
          }), eEmitterStatus) : eEmitterStatus).None;
        }

        setValue(emitter, value) {
          emitter.isActive = value;
        }

        canInterpolate() {
          return false;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Emitter === void 0 ? (_reportPossibleCrUseOfEmitter({
            error: Error()
          }), Emitter) : Emitter);
        }

      });
      /**
       * Handler for Emitter Initial Delay
       */


      _export("EmitterInitialDelayHandler", EmitterInitialDelayHandler = class EmitterInitialDelayHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
            error: Error()
          }), eEmitterActionType) : eEmitterActionType).Emitter_InitialDelay, 'number', 'Initial Delay');
        }

        getValue(emitter) {
          return emitter.initialDelay;
        }

        setValue(emitter, value) {
          emitter.initialDelay = value;
        }

        canInterpolate() {
          return true;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Emitter === void 0 ? (_reportPossibleCrUseOfEmitter({
            error: Error()
          }), Emitter) : Emitter);
        }

      });
      /**
       * Handler for Emitter Prewarm state
       */


      _export("EmitterPrewarmHandler", EmitterPrewarmHandler = class EmitterPrewarmHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
            error: Error()
          }), eEmitterActionType) : eEmitterActionType).Emitter_Prewarm, 'boolean', 'Prewarm');
        }

        getValue(emitter) {
          return emitter.isPreWarm;
        }

        setValue(emitter, value) {
          emitter.isPreWarm = value;
        }

        canInterpolate() {
          return false;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Emitter === void 0 ? (_reportPossibleCrUseOfEmitter({
            error: Error()
          }), Emitter) : Emitter);
        }

      });
      /**
       * Handler for Emitter Prewarm Duration
       */


      _export("EmitterPrewarmDurationHandler", EmitterPrewarmDurationHandler = class EmitterPrewarmDurationHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
            error: Error()
          }), eEmitterActionType) : eEmitterActionType).Emitter_PrewarmDuration, 'number', 'Prewarm Duration');
        }

        getValue(emitter) {
          return emitter.preWarmDuration;
        }

        setValue(emitter, value) {
          emitter.preWarmDuration = value;
        }

        canInterpolate() {
          return true;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Emitter === void 0 ? (_reportPossibleCrUseOfEmitter({
            error: Error()
          }), Emitter) : Emitter);
        }

      });
      /**
       * Handler for Emitter Duration
       */


      _export("EmitterDurationHandler", EmitterDurationHandler = class EmitterDurationHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
            error: Error()
          }), eEmitterActionType) : eEmitterActionType).Emitter_Duration, 'number', 'Duration');
        }

        getValue(emitter) {
          return emitter.emitDuration;
        }

        setValue(emitter, value) {
          emitter.emitDuration = value;
        }

        canInterpolate() {
          return true;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Emitter === void 0 ? (_reportPossibleCrUseOfEmitter({
            error: Error()
          }), Emitter) : Emitter);
        }

      });
      /**
       * Handler for Emitter Elapsed Time
       */


      _export("EmitterElapsedTimeHandler", EmitterElapsedTimeHandler = class EmitterElapsedTimeHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
            error: Error()
          }), eEmitterActionType) : eEmitterActionType).Emitter_ElapsedTime, 'number', 'Elapsed Time');
        }

        getValue(emitter) {
          return emitter.totalElapsedTime;
        }

        setValue(emitter, value) {
          // Note: This sets the internal status elapsed time, not total elapsed time
          emitter['_statusElapsedTime'] = value;
        }

        canInterpolate() {
          return true;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Emitter === void 0 ? (_reportPossibleCrUseOfEmitter({
            error: Error()
          }), Emitter) : Emitter);
        }

      });
      /**
       * Handler for Emitter Loop state
       */


      _export("EmitterLoopHandler", EmitterLoopHandler = class EmitterLoopHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
            error: Error()
          }), eEmitterActionType) : eEmitterActionType).Emitter_Loop, 'boolean', 'Loop');
        }

        getValue(emitter) {
          return emitter.isLoop;
        }

        setValue(emitter, value) {
          emitter.isLoop = value;
        }

        canInterpolate() {
          return false;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Emitter === void 0 ? (_reportPossibleCrUseOfEmitter({
            error: Error()
          }), Emitter) : Emitter);
        }

      });
      /**
       * Handler for Emitter Loop Interval
       */


      _export("EmitterLoopIntervalHandler", EmitterLoopIntervalHandler = class EmitterLoopIntervalHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
            error: Error()
          }), eEmitterActionType) : eEmitterActionType).Emitter_LoopInterval, 'number', 'Loop Interval');
        }

        getValue(emitter) {
          return emitter.loopInterval;
        }

        setValue(emitter, value) {
          emitter.loopInterval = value;
        }

        canInterpolate() {
          return true;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Emitter === void 0 ? (_reportPossibleCrUseOfEmitter({
            error: Error()
          }), Emitter) : Emitter);
        }

      });
      /**
       * Handler for Emitter Per Emit Interval
       */


      _export("EmitterPerEmitIntervalHandler", EmitterPerEmitIntervalHandler = class EmitterPerEmitIntervalHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
            error: Error()
          }), eEmitterActionType) : eEmitterActionType).Emitter_PerEmitInterval, 'number', 'Per Emit Interval');
        }

        getValue(emitter) {
          return emitter.perEmitInterval;
        }

        setValue(emitter, value) {
          emitter.perEmitInterval = value;
        }

        canInterpolate() {
          return true;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Emitter === void 0 ? (_reportPossibleCrUseOfEmitter({
            error: Error()
          }), Emitter) : Emitter);
        }

      });
      /**
       * Handler for Emitter Per Emit Count
       */


      _export("EmitterPerEmitCountHandler", EmitterPerEmitCountHandler = class EmitterPerEmitCountHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
            error: Error()
          }), eEmitterActionType) : eEmitterActionType).Emitter_PerEmitCount, 'number', 'Per Emit Count');
        }

        getValue(emitter) {
          return emitter.perEmitCount;
        }

        setValue(emitter, value) {
          emitter.perEmitCount = value;
        }

        canInterpolate() {
          return true;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Emitter === void 0 ? (_reportPossibleCrUseOfEmitter({
            error: Error()
          }), Emitter) : Emitter);
        }

      });
      /**
       * Handler for Emitter Per Emit Offset X
       */


      _export("EmitterPerEmitOffsetXHandler", EmitterPerEmitOffsetXHandler = class EmitterPerEmitOffsetXHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
            error: Error()
          }), eEmitterActionType) : eEmitterActionType).Emitter_PerEmitOffsetX, 'number', 'Per Emit Offset X');
        }

        getValue(emitter) {
          return emitter.perEmitOffsetX;
        }

        setValue(emitter, value) {
          emitter.perEmitOffsetX = value;
        }

        canInterpolate() {
          return true;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Emitter === void 0 ? (_reportPossibleCrUseOfEmitter({
            error: Error()
          }), Emitter) : Emitter);
        }

      });
      /**
       * Handler for Emitter Angle
       */


      _export("EmitterAngleHandler", EmitterAngleHandler = class EmitterAngleHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
            error: Error()
          }), eEmitterActionType) : eEmitterActionType).Emitter_Angle, 'number', 'Angle');
        }

        getValue(emitter) {
          return emitter.angle;
        }

        setValue(emitter, value) {
          emitter.angle = value;
        }

        canInterpolate() {
          return true;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Emitter === void 0 ? (_reportPossibleCrUseOfEmitter({
            error: Error()
          }), Emitter) : Emitter);
        }

      });
      /**
       * Handler for Emitter Count
       */


      _export("EmitterCountHandler", EmitterCountHandler = class EmitterCountHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
            error: Error()
          }), eEmitterActionType) : eEmitterActionType).Emitter_Count, 'number', 'Count');
        }

        getValue(emitter) {
          return emitter.count;
        }

        setValue(emitter, value) {
          emitter.count = value;
        }

        canInterpolate() {
          return true;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Emitter === void 0 ? (_reportPossibleCrUseOfEmitter({
            error: Error()
          }), Emitter) : Emitter);
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=5481ee518aa4100fad485a10be86b96398900683.js.map