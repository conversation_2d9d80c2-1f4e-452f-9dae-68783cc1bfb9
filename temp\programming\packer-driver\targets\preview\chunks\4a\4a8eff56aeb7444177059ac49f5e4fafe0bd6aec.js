System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Layout, tween, UITransform, v3, ButtonPlus, BaseUI, UILayer, UIMgr, BattleUI, PlaneUI, ShopUI, SkyIslandUI, TalentUI, _dec, _dec2, _class, _class2, _descriptor, _crd, ccclass, property, BottomUI;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "../common/components/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBattleUI(extras) {
    _reporterNs.report("BattleUI", "./BattleUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneUI(extras) {
    _reporterNs.report("PlaneUI", "./plane/PlaneUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfShopUI(extras) {
    _reporterNs.report("ShopUI", "./ShopUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSkyIslandUI(extras) {
    _reporterNs.report("SkyIslandUI", "./SkyIslandUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTalentUI(extras) {
    _reporterNs.report("TalentUI", "./TalentUI", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Layout = _cc.Layout;
      tween = _cc.tween;
      UITransform = _cc.UITransform;
      v3 = _cc.v3;
    }, function (_unresolved_2) {
      ButtonPlus = _unresolved_2.ButtonPlus;
    }, function (_unresolved_3) {
      BaseUI = _unresolved_3.BaseUI;
      UILayer = _unresolved_3.UILayer;
      UIMgr = _unresolved_3.UIMgr;
    }, function (_unresolved_4) {
      BattleUI = _unresolved_4.BattleUI;
    }, function (_unresolved_5) {
      PlaneUI = _unresolved_5.PlaneUI;
    }, function (_unresolved_6) {
      ShopUI = _unresolved_6.ShopUI;
    }, function (_unresolved_7) {
      SkyIslandUI = _unresolved_7.SkyIslandUI;
    }, function (_unresolved_8) {
      TalentUI = _unresolved_8.TalentUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "8ae4e9gWoVAipSQy6/1/OO1", "BottomUI", undefined);

      __checkObsolete__(['_decorator', 'EventTouch', 'Layout', 'math', 'tween', 'UITransform', 'v3']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("BottomUI", BottomUI = (_dec = ccclass('BottomUI'), _dec2 = property(Layout), _dec(_class = (_class2 = class BottomUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "bottomLayer", _descriptor, this);

          this._moduleBtns = [];
          this._ModuleUI = [_crd && ShopUI === void 0 ? (_reportPossibleCrUseOfShopUI({
            error: Error()
          }), ShopUI) : ShopUI, _crd && TalentUI === void 0 ? (_reportPossibleCrUseOfTalentUI({
            error: Error()
          }), TalentUI) : TalentUI, _crd && BattleUI === void 0 ? (_reportPossibleCrUseOfBattleUI({
            error: Error()
          }), BattleUI) : BattleUI, _crd && PlaneUI === void 0 ? (_reportPossibleCrUseOfPlaneUI({
            error: Error()
          }), PlaneUI) : PlaneUI, _crd && SkyIslandUI === void 0 ? (_reportPossibleCrUseOfSkyIslandUI({
            error: Error()
          }), SkyIslandUI) : SkyIslandUI];
          this._clickIndex = 2;
          this._originSize = void 0;
        }

        static getUrl() {
          return "ui/main/BottomUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Default;
        }

        onLoad() {
          var moduleBtns = this.bottomLayer.node.getComponentsInChildren(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
            error: Error()
          }), ButtonPlus) : ButtonPlus);

          for (var i = 0; i < moduleBtns.length; i++) {
            var element = moduleBtns[i];
            element.addClick(this.onClick, this);

            this._moduleBtns.push(element);
          }

          this._originSize = this._moduleBtns[0].node.getComponent(UITransform).contentSize;
          this.updateClickState();
        }

        updateLayout(immediate) {
          if (immediate === void 0) {
            immediate = false;
          }

          this._moduleBtns.forEach((btn, i) => {
            var isSelected = i === this._clickIndex;
            var targetScale = isSelected ? 1.2 : 0.8;

            if (immediate) {
              btn.node.setScale(v3(targetScale, targetScale, 1));
            } else {
              tween(btn.node).to(.3, {
                scale: v3(targetScale, targetScale, 1)
              }, {
                easing: 'sineOut'
              }).start();
            }
          }); // 延迟更新Layout以确保动画完成后重新计算位置


          this.scheduleOnce(() => {
            var _this$bottomLayer;

            (_this$bottomLayer = this.bottomLayer) == null || _this$bottomLayer.updateLayout();
          }, immediate ? 0 : .3);
        }

        updateClickState() {
          this._ModuleUI.forEach((v, i) => {
            if (i == this._clickIndex) return;
            var ui = (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).get(v);
            if (ui) (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).hideUI(v);
          });

          this.updateLayout(true);
        }

        onClick(event) {
          var _this = this;

          return _asyncToGenerator(function* () {
            console.log("onClick", event.target.name);

            var index = _this._moduleBtns.findIndex(v => {
              return v.node === event.target;
            });

            if (index == _this._clickIndex) return;
            _this._clickIndex = index;
            yield (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).openUI(_this._ModuleUI[index]);

            _this.updateClickState();
          })();
        }

        onShow() {
          return _asyncToGenerator(function* () {})();
        }

        onHide() {
          return _asyncToGenerator(function* () {})();
        }

        onClose() {
          return _asyncToGenerator(function* () {})();
        }

        update(dt) {}

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "bottomLayer", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=4a8eff56aeb7444177059ac49f5e4fafe0bd6aec.js.map