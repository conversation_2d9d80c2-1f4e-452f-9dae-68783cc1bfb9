/* eslint-disable vue/one-component-per-file */

import { readFileSync } from 'fs-extra';
import { join } from 'path';
import { createApp, App, defineComponent } from 'vue';
const panelDataMap = new WeakMap<any, App>();
/**
 * @zh 如果希望兼容 3.3 之前的版本可以使用下方的代码
 * @en You can add the code below if you want compatibility with versions prior to 3.3
 */
// Editor.Panel.define = Editor.Panel.define || function(options: any) { return options }
module.exports = Editor.Panel.define({
    listeners: {
        show() { console.log('show'); },
        hide() { console.log('hide'); },
    },
    template: readFileSync(join(__dirname, '../../../static/template/newlevel/index.html'), 'utf-8'),
    style: readFileSync(join(__dirname, '../../../static/style/newlevel/index.css'), 'utf-8'),
    $: {
        app: '#app',
    },
    methods: {
    },
    ready() {
        if (this.$.app) {
            const app = createApp({});
            app.config.compilerOptions.isCustomElement = (tag) => tag.startsWith('ui-');
           
            app.component('NewLevel', defineComponent({
                data() {
                    return {
                        inputValue: "",
                    };
                }, 
                methods: {
                    confirm() {
                        console.log("confirm:" + this.inputValue);
                        // close panel
                        Editor.Panel.close('level-editor.newlevel');
                    },
                    cancel() {
                        console.log("cancel:" + this.inputValue);
                        Editor.Panel.close('level-editor.newlevel');
                    },
                },
                template: readFileSync(join(__dirname, '../../../static/template/vue/newlevel.html'), 'utf-8'),
            }));
            app.mount(this.$.app);
            panelDataMap.set(this, app);
        }
    },
    beforeClose() { },
    close() {
        const app = panelDataMap.get(this);
        if (app) {
            app.unmount();
        }
    },
});
