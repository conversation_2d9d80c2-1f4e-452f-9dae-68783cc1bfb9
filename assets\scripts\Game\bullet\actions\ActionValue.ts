import { _decorator } from 'cc';
const { ccclass } = _decorator;

/**
 * Generic value type that can hold different types
 */
export type ActionValue = number | boolean | string | any;

/**
 * Interface for value interpolation
 */
export interface IValueInterpolator<T> {
    canInterpolate(): boolean;
    interpolate(start: T, target: T, progress: number): T;
    getRandomValue(min: T, max: T): T;
}

/**
 * Number interpolator - supports smooth interpolation between numeric values
 */
export class NumberInterpolator implements IValueInterpolator<number> {
    canInterpolate(): boolean { 
        return true; 
    }
    
    interpolate(start: number, target: number, progress: number): number {
        return start + (target - start) * progress;
    }
    
    getRandomValue(min: number, max: number): number {
        return Math.random() * (max - min) + min;
    }
}

/**
 * Boolean interpolator - no smooth interpolation, switches at threshold
 */
export class BooleanInterpolator implements IValueInterpolator<boolean> {
    canInterpolate(): boolean { 
        return false; 
    }
    
    interpolate(start: boolean, target: boolean, progress: number): boolean {
        // Switch at 50% progress, or immediately if no duration
        return progress >= 0.5 ? target : start;
    }
    
    getRandomValue(min: boolean, max: boolean): boolean {
        return Math.random() > 0.5;
    }
}

/**
 * String interpolator - no interpolation, switches at completion
 */
export class StringInterpolator implements IValueInterpolator<string> {
    canInterpolate(): boolean { 
        return false; 
    }
    
    interpolate(start: string, target: string, progress: number): string {
        return progress >= 1.0 ? target : start;
    }
    
    getRandomValue(min: string, max: string): string {
        // For strings, randomly pick between min and max
        return Math.random() > 0.5 ? max : min;
    }
}

/**
 * RPN (Reverse Polish Notation) interpolator - for future expression support
 */
export class RPNInterpolator implements IValueInterpolator<string> {
    canInterpolate(): boolean { 
        return false; // RPN expressions are evaluated, not interpolated
    }
    
    interpolate(start: string, target: string, progress: number): string {
        // For RPN, we would evaluate the expression at the current progress
        // This is a placeholder for future implementation
        return progress >= 1.0 ? target : start;
    }
    
    getRandomValue(min: string, max: string): string {
        // For RPN expressions, this would need special handling
        return Math.random() > 0.5 ? max : min;
    }
}

/**
 * Factory for creating appropriate interpolators based on value type
 */
export class InterpolatorFactory {
    static create(valueType: 'number' | 'boolean' | 'string' | 'rpn'): IValueInterpolator<ActionValue> {
        switch (valueType) {
            case 'number': 
                return new NumberInterpolator();
            case 'boolean': 
                return new BooleanInterpolator();
            case 'string': 
                return new StringInterpolator();
            case 'rpn': 
                return new RPNInterpolator();
            default: 
                console.warn(`Unknown value type: ${valueType}, defaulting to number interpolator`);
                return new NumberInterpolator();
        }
    }
}
