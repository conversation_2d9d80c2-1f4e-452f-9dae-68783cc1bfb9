import { _decorator, misc, Component, Node, Sprite, Color } from 'cc';
import { EDITOR } from 'cc/env';
import { BulletData } from '../data/BulletData';
import { ObjectPool } from './ObjectPool';
import { Movable } from '../move/Movable';
import { BulletSystem } from './BulletSystem';
import { eBulletActionType } from '../data/EventActionData';
const { ccclass, property, executeInEditMode } = _decorator;

// 子弹 Bullet 伤害计算 
// Weapon -> 发射器, 喷火, 技能武器, 激光
// WeaponSlot -> SetWeapon
@ccclass('Bullet')
@executeInEditMode
export class Bullet extends Component {

    @property({type: Movable, displayName: "移动组件"})
    public mover: Movable = null;

    @property({type: Sprite, displayName: "子弹精灵"})
    public bulletSprite: Sprite = null;

    @property({type: BulletData})
    public readonly bulletData: BulletData = null;

    public isRunning: boolean = false;
    public elapsedTime: number = 0;

    // 以下属性重新定义一遍, 作为可修改的属性, 部分定义在movable里
    public isDestructive : boolean = false;          // 是否可被破坏
    public isDestructiveOnHit : boolean = false;     // 命中时是否被销毁
    public duration : number = 10;                   // 子弹持续时间(超出后销毁回收)
    public damage : number = 1;                      // 子弹伤害
    public delayDestroy : number = 0;                 // 延迟销毁时间

    private _eventRunners : GenericActionRunner[] = null;

    onLoad(): void {
        if (!this.mover) {
            this.mover = this.getComponent(Movable);
            if (!this.mover) {
                this.mover = this.addComponent(Movable);
            }
        }

        this.mover.onBecomeInvisible = () => {
            BulletSystem.onDestroyBullet(this);
        };

        this._eventRunners = [];
        this.bulletData.eventGroupData.forEach(eventGroup => {
            const runner = BulletSystem.createBulletActionRunner(this, eventGroup);
            this._eventRunners.push(runner);
        });
    }

    /**
     * TODO: 如果后续自己写碰撞, 这里要相应进行替换
     */
    onCollisionEnter(other: Node, self: Node): void {
        // 判断另一个node也是子弹或者非子弹, 进行相应处理
        // 根据this.isDestructive 和 this.isDestructiveOnHit
        BulletSystem.onDestroyBullet(this);
    }
    
    public onCreate(): void {
        this.isRunning = true;
        this.elapsedTime = 0;

        this.isDestructive = this.bulletData.isDestructive;
        this.isDestructiveOnHit = this.bulletData.isDestructiveOnHit;
        this.duration = this.bulletData.duration;
        this.damage = this.bulletData.damage;
        this.delayDestroy = this.bulletData.delayDestroy;

        this.mover.isFacingMoveDir = this.bulletData.isFacingMoveDir;
        this.mover.isTrackingTarget = this.bulletData.isTrackingTarget;
        this.mover.speed = this.bulletData.speed;
        // speedAngle is set in Emitter
        // this.mover.speedAngle = this.node.eulerAngles.z;
        this.mover.acceleration = this.bulletData.acceleration;
        this.mover.accelerationAngle = this.bulletData.accelerationAngle;

        this._eventRunners.forEach(runner => {
            runner.init(this);
        });
    }

    public tick(dt:number) : void {
        if (!this.isRunning) return;

        this.elapsedTime += dt;
        if (this.elapsedTime > this.duration) {
            this.destroySelf();
            return;
        }

        this.mover?.tick(dt);
    }

    public destroySelf(): void {
        this.isRunning = false;
        const cb = () => {
            if (!this.node || !this.node.isValid) return;

            if (EDITOR) {
                this.node.destroy();
            } else {
                ObjectPool.returnNode(this.node);
            }
        };
        if (this.delayDestroy > 0) {
            this.scheduleOnce(() => {
                cb();
            }, this.delayDestroy);
        } else {
            cb();
        }
    }

    public applyAction(actType: eBulletActionType, actValue: number) {
        switch (actType) {
            case eBulletActionType.Bullet_Duration:
                this.duration = actValue;
                break;
            case eBulletActionType.Bullet_ElapsedTime:
                this.elapsedTime = actValue;
                break;
            case eBulletActionType.Bullet_PosX:
                this.node.setPosition(actValue, this.node.position.y, this.node.position.z);
                break;
            case eBulletActionType.Bullet_PosY:
                this.node.setPosition(this.node.position.x, actValue, this.node.position.z);
                break;
            case eBulletActionType.Bullet_Damage:
                this.damage = actValue;
                break;
            case eBulletActionType.Bullet_Speed:
                this.mover.speed = actValue;
                break;
            case eBulletActionType.Bullet_SpeedAngle:
                this.mover.speedAngle = actValue;
                break;
            case eBulletActionType.Bullet_Acceleration:
                this.mover.acceleration = actValue;
                break;
            case eBulletActionType.Bullet_AccelerationAngle:
                this.mover.accelerationAngle = actValue;
                break;
            case eBulletActionType.Bullet_Scale:
                this.node.setScale(actValue, actValue, actValue);
                break;
            case eBulletActionType.Bullet_ColorR:
                const colorR = this.bulletSprite.color;
                this.bulletSprite.color = new Color(actValue, colorR.g, colorR.b, colorR.a);
                break;
            case eBulletActionType.Bullet_ColorG:
                const colorG = this.bulletSprite.color;
                this.bulletSprite.color = new Color(colorG.r, actValue, colorG.b, colorG.a);
                break;
            case eBulletActionType.Bullet_ColorB:
                const colorB = this.bulletSprite.color;
                this.bulletSprite.color = new Color(colorB.r, colorB.g, actValue, colorB.a);
                break;
            case eBulletActionType.Bullet_ColorA:
                const colorA = this.bulletSprite.color;
                this.bulletSprite.color = new Color(colorA.r, colorA.g, colorA.b, actValue);
                break;
            case eBulletActionType.Bullet_FaceMovingDir:
                this.mover.isFacingMoveDir = actValue === 1 ? true : false;
                break;
            case eBulletActionType.Bullet_TrackingTarget:
                this.mover.isTrackingTarget = actValue === 1 ? true : false;
                // TODO: 子弹可能需要自动选择目标并追踪
                break;
            case eBulletActionType.Bullet_Destructive:
                this.isDestructive = actValue === 1 ? true : false;
                break;
            case eBulletActionType.Bullet_DestructiveOnHit:
                this.isDestructiveOnHit = actValue === 1 ? true : false;
                break;
        }
    }
}
