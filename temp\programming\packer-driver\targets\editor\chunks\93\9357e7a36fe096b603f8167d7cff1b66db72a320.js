System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Label, ProgressBar, _decorator, tween, BaseUI, UILayer, _dec, _dec2, _dec3, _dec4, _class, _class2, _descriptor, _descriptor2, _descriptor3, _crd, ccclass, property, LoadingUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "./UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "./UIMgr", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Label = _cc.Label;
      ProgressBar = _cc.ProgressBar;
      _decorator = _cc._decorator;
      tween = _cc.tween;
    }, function (_unresolved_2) {
      BaseUI = _unresolved_2.BaseUI;
      UILayer = _unresolved_2.UILayer;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e4d84KTMYtK6bAE924leygu", "LoadingUI", undefined);

      __checkObsolete__(['Label', 'ProgressBar', '_decorator', 'tween']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("LoadingUI", LoadingUI = (_dec = ccclass("LoadingUI"), _dec2 = property(ProgressBar), _dec3 = property(Label), _dec4 = property(Label), _dec(_class = (_class2 = class LoadingUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "bar", _descriptor, this);

          _initializerDefineProperty(this, "percent", _descriptor2, this);

          _initializerDefineProperty(this, "title", _descriptor3, this);

          this.runSec = 0;
          this.initPercent = 0;
        }

        static getUrl() {
          return "ui/LoadingUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Default;
        }

        /**
         *
         * @param runSec 运行时间
         * @param startPercent 开始的百分比数- 0.991=99.1%
         */
        async onShow(runSec, startPercent) {
          this.runSec = runSec;
          this.initPercent = startPercent;
          this.bar.progress = 0;
          this.percent.string = "00.0%";
        }

        async onHide(hideSec) {
          let lbPercent = this.percent;
          this.runSec = 0;
          this.initPercent = 0;
          return new Promise(resolve => {
            tween(this.bar).to(hideSec, {
              progress: 1
            }, {
              progress: (start, end, current, ratio) => {
                let value = start + (end - start) * ratio;
                lbPercent.string = (value * 100).toFixed(1) + "%";
                return value;
              }
            }).call(() => {
              setTimeout(resolve, 500);
              this.bar.progress = 1;
            }).start();
          });
        }

        async onClose(...args) {}

        update(dt) {
          if (this.runSec == 0) {
            return;
          }

          this.bar.progress += dt * this.initPercent / this.runSec;

          if (this.bar.progress > this.initPercent) {
            this.bar.progress = this.initPercent;
          }

          this.percent.string = (this.bar.progress * 100).toFixed(1) + "%";
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "bar", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "percent", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "title", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=9357e7a36fe096b603f8167d7cff1b66db72a320.js.map