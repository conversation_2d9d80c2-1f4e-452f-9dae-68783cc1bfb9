import { _decorator } from 'cc';
import { IAction<PERSON><PERSON><PERSON> } from './IActionHandler';
import { 
    Emitter<PERSON><PERSON><PERSON>and<PERSON>, 
    EmitterInitial<PERSON><PERSON>y<PERSON><PERSON><PERSON>, 
    EmitterPrewarm<PERSON><PERSON><PERSON>,
    EmitterPrewarmDuration<PERSON><PERSON><PERSON>,
    <PERSON>itter<PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>itter<PERSON><PERSON>sed<PERSON><PERSON><PERSON><PERSON><PERSON>,
    EmitterLoop<PERSON><PERSON><PERSON>,
    EmitterLoopIntervalHandler,
    EmitterPerEmitIntervalHandler,
    EmitterPerEmitCountHandler,
    EmitterPerEmitOffset<PERSON><PERSON><PERSON><PERSON>,
    <PERSON>itter<PERSON><PERSON><PERSON><PERSON><PERSON>,
    EmitterCountHandler
} from './EmitterActionHandlers';
import { 
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>etElapsedTimeHandler,
    BulletPosXHandler,
    BulletPosYHandler,
    BulletDamageHandler,
    BulletSpeedHandler,
    BulletSpeedAngleHandler,
    BulletAccelerationHandler,
    BulletAccelerationAngleHand<PERSON>,
    <PERSON>etScaleHand<PERSON>,
    <PERSON>etColorR<PERSON><PERSON><PERSON>,
    <PERSON>etColorG<PERSON><PERSON><PERSON>,
    <PERSON>et<PERSON>olor<PERSON><PERSON><PERSON><PERSON>,
    <PERSON>et<PERSON>olor<PERSON><PERSON><PERSON><PERSON>,
    BulletFaceMovingDirHandler,
    BulletTrackingTargetHandler,
    BulletDestructiveHandler,
    BulletDestructiveOnHitHandler
} from './BulletActionHandlers';
const { ccclass } = _decorator;

/**
 * Registry for managing action handlers
 */
export class ActionRegistry {
    private static handlers = new Map<number, IActionHandler>();
    private static initialized = false;
    
    /**
     * Initialize the registry with all available handlers
     */
    static initialize() {
        if (this.initialized) {
            console.warn('ActionRegistry already initialized');
            return;
        }
        
        console.log('Initializing ActionRegistry...');
        
        // Register emitter handlers
        this.register(new EmitterActiveHandler());
        this.register(new EmitterInitialDelayHandler());
        this.register(new EmitterPrewarmHandler());
        this.register(new EmitterPrewarmDurationHandler());
        this.register(new EmitterDurationHandler());
        this.register(new EmitterElapsedTimeHandler());
        this.register(new EmitterLoopHandler());
        this.register(new EmitterLoopIntervalHandler());
        this.register(new EmitterPerEmitIntervalHandler());
        this.register(new EmitterPerEmitCountHandler());
        this.register(new EmitterPerEmitOffsetXHandler());
        this.register(new EmitterAngleHandler());
        this.register(new EmitterCountHandler());
        
        // Register bullet handlers
        this.register(new BulletDurationHandler());
        this.register(new BulletElapsedTimeHandler());
        this.register(new BulletPosXHandler());
        this.register(new BulletPosYHandler());
        this.register(new BulletDamageHandler());
        this.register(new BulletSpeedHandler());
        this.register(new BulletSpeedAngleHandler());
        this.register(new BulletAccelerationHandler());
        this.register(new BulletAccelerationAngleHandler());
        this.register(new BulletScaleHandler());
        this.register(new BulletColorRHandler());
        this.register(new BulletColorGHandler());
        this.register(new BulletColorBHandler());
        this.register(new BulletColorAHandler());
        this.register(new BulletFaceMovingDirHandler());
        this.register(new BulletTrackingTargetHandler());
        this.register(new BulletDestructiveHandler());
        this.register(new BulletDestructiveOnHitHandler());
        
        this.initialized = true;
        console.log(`ActionRegistry initialized with ${this.handlers.size} handlers`);
    }
    
    /**
     * Register a new action handler
     */
    static register(handler: IActionHandler) {
        if (this.handlers.has(handler.actionType)) {
            console.warn(`Handler for action type ${handler.actionType} already exists, overwriting`);
        }
        
        this.handlers.set(handler.actionType, handler);
        console.log(`Registered handler: ${handler.getDisplayName?.()} (${handler.actionType})`);
    }
    
    /**
     * Get a handler for a specific action type
     */
    static getHandler(actionType: number): IActionHandler | null {
        if (!this.initialized) {
            console.error('ActionRegistry not initialized. Call ActionRegistry.initialize() first.');
            return null;
        }
        
        return this.handlers.get(actionType) || null;
    }
    
    /**
     * Get all registered handlers
     */
    static getAllHandlers(): IActionHandler[] {
        return Array.from(this.handlers.values());
    }
    
    /**
     * Get handlers by value type
     */
    static getHandlersByValueType(valueType: 'number' | 'boolean' | 'string' | 'rpn'): IActionHandler[] {
        return this.getAllHandlers().filter(handler => handler.valueType === valueType);
    }
    
    /**
     * Check if a handler exists for an action type
     */
    static hasHandler(actionType: number): boolean {
        return this.handlers.has(actionType);
    }
    
    /**
     * Get the number of registered handlers
     */
    static getHandlerCount(): number {
        return this.handlers.size;
    }
    
    /**
     * Clear all handlers (useful for testing)
     */
    static clear() {
        this.handlers.clear();
        this.initialized = false;
        console.log('ActionRegistry cleared');
    }
    
    /**
     * Get debug information about registered handlers
     */
    static getDebugInfo(): string {
        const handlers = this.getAllHandlers();
        const info = handlers.map(handler => 
            `${handler.actionType}: ${handler.getDisplayName?.()} (${handler.valueType})`
        ).join('\n');
        
        return `ActionRegistry Debug Info:\n${info}`;
    }
}
