{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/actions/GenericActionRunner.ts"], "names": ["GenericActionRunner", "_decorator", "InterpolatorFactory", "ActionRegistry", "eEasing", "ccclass", "isCompleted", "_isCompleted", "actionType", "handler", "valueType", "constructor", "owner", "actionTypeId", "duration", "easing", "targetVal", "isRandom", "minValue", "maxValue", "startValue", "targetValue", "elapsedTime", "isRunning", "interpolator", "<PERSON><PERSON><PERSON><PERSON>", "Error", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "create", "start", "getValue", "calculateTargetValue", "console", "log", "getDisplayName", "error", "undefined", "getRandomValue", "tick", "dt", "apply", "canInterpolate", "progress", "Math", "min", "easedProgress", "applyEasing", "currentValue", "interpolate", "value", "setValue", "t", "Linear", "InSine", "cos", "PI", "OutSine", "sin", "InOutSine", "InQuad", "OutQuad", "InOutQuad", "pow", "forceComplete", "cancel", "getDebugInfo", "toFixed"], "mappings": ";;;yIAUaA,mB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAVJC,MAAAA,U,OAAAA,U;;AACiCC,MAAAA,mB,iBAAAA,mB;;AAEjCC,MAAAA,c,iBAAAA,c;;AACAC,MAAAA,O,iBAAAA,O;;;;;;;;;OACH;AAAEC,QAAAA;AAAF,O,GAAcJ,U;AAEpB;AACA;AACA;;qCACaD,mB,GAAN,MAAMA,mBAAN,CAA0B;AASd,YAAXM,WAAW,GAAY;AACvB,iBAAO,KAAKC,YAAZ;AACH;;AAEa,YAAVC,UAAU,GAAW;AAAA;;AACrB,iBAAO,uBAAKC,OAAL,mCAAcD,UAAd,KAA4B,CAAC,CAApC;AACH;;AAEY,YAATE,SAAS,GAAW;AAAA;;AACpB,iBAAO,wBAAKD,OAAL,oCAAcC,SAAd,KAA2B,SAAlC;AACH;;AAEDC,QAAAA,WAAW,CACCC,KADD,EAECC,YAFD,EAGCC,QAHD,EAICC,MAJD,EAKCC,SALD,EAMCC,QAND,EAOCC,QAPD,EAQCC,QARD,EAST;AAAA,cAHUF,QAGV;AAHUA,YAAAA,QAGV,GAH8B,KAG9B;AAAA;;AAAA,eA7BMG,UA6BN,GA7BgC,CA6BhC;AAAA,eA5BMC,WA4BN,GA5BiC,CA4BjC;AAAA,eA3BMC,WA2BN,GA3B4B,CA2B5B;AAAA,eA1BMC,SA0BN,GA1B2B,KA0B3B;AAAA,eAzBMhB,YAyBN,GAzB8B,KAyB9B;AAAA,eAxBME,OAwBN;AAAA,eAvBMe,YAuBN;AAAA,eARUZ,KAQV,GARUA,KAQV;AAAA,eAPUC,YAOV,GAPUA,YAOV;AAAA,eANUC,QAMV,GANUA,QAMV;AAAA,eALUC,MAKV,GALUA,MAKV;AAAA,eAJUC,SAIV,GAJUA,SAIV;AAAA,eAHUC,QAGV,GAHUA,QAGV;AAAA,eAFUC,QAEV,GAFUA,QAEV;AAAA,eADUC,QACV,GADUA,QACV;AACE,eAAKV,OAAL,GAAe;AAAA;AAAA,gDAAegB,UAAf,CAA0BZ,YAA1B,CAAf;;AACA,cAAI,CAAC,KAAKJ,OAAV,EAAmB;AACf,kBAAM,IAAIiB,KAAJ,wCAA+Cb,YAA/C,CAAN;AACH,WAJH,CAME;;;AACA,cAAI,KAAKJ,OAAL,CAAakB,aAAb,IAA8B,CAAC,KAAKlB,OAAL,CAAakB,aAAb,CAA2Bf,KAA3B,CAAnC,EAAsE;AAClE,kBAAM,IAAIc,KAAJ,wDAA+Db,YAA/D,CAAN;AACH;;AAED,eAAKW,YAAL,GAAoB;AAAA;AAAA,0DAAoBI,MAApB,CAA2B,KAAKnB,OAAL,CAAaC,SAAxC,CAApB;AACH;AAED;AACJ;AACA;;;AACImB,QAAAA,KAAK,GAAG;AACJ,cAAI;AAAA;;AACA,iBAAKT,UAAL,GAAkB,KAAKX,OAAL,CAAaqB,QAAb,CAAsB,KAAKlB,KAA3B,CAAlB;AACA,iBAAKS,WAAL,GAAmB,KAAKU,oBAAL,EAAnB;AACA,iBAAKT,WAAL,GAAmB,CAAnB;AACA,iBAAKC,SAAL,GAAiB,IAAjB;AACA,iBAAKhB,YAAL,GAAoB,KAApB;AAEAyB,YAAAA,OAAO,CAACC,GAAR,+CAA8B,uBAAKxB,OAAL,EAAayB,cAA3C,qBAA8B,0CAA9B,WAAkE,KAAKd,UAAvE,YAAwF,KAAKC,WAA7F;AACH,WARD,CAQE,OAAOc,KAAP,EAAc;AACZH,YAAAA,OAAO,CAACG,KAAR,6BAAwC,KAAKtB,YAA7C,QAA8DsB,KAA9D;AACA,iBAAK5B,YAAL,GAAoB,IAApB;AACA,iBAAKgB,SAAL,GAAiB,KAAjB;AACH;AACJ;AAED;AACJ;AACA;;;AACYQ,QAAAA,oBAAoB,GAAgB;AACxC,cAAI,KAAKd,QAAL,IAAiB,KAAKC,QAAL,KAAkBkB,SAAnC,IAAgD,KAAKjB,QAAL,KAAkBiB,SAAtE,EAAiF;AAC7E,mBAAO,KAAKZ,YAAL,CAAkBa,cAAlB,CAAiC,KAAKnB,QAAtC,EAAgD,KAAKC,QAArD,CAAP;AACH;;AACD,iBAAO,KAAKH,SAAZ;AACH;AAED;AACJ;AACA;;;AACIsB,QAAAA,IAAI,CAACC,EAAD,EAAa;AACb,cAAI,CAAC,KAAKhB,SAAN,IAAmB,KAAKhB,YAA5B,EAA0C;AACtC;AACH;;AAED,eAAKe,WAAL,IAAoBiB,EAApB;;AAEA,cAAI,KAAKjB,WAAL,IAAoB,KAAKR,QAA7B,EAAuC;AACnC;AACA,iBAAKS,SAAL,GAAiB,KAAjB;AACA,iBAAKhB,YAAL,GAAoB,IAApB;AACA,iBAAKiC,KAAL,CAAW,KAAKnB,WAAhB;AACH,WALD,MAKO,IAAI,KAAKZ,OAAL,CAAagC,cAAb,MAAiC,KAAKjB,YAAL,CAAkBiB,cAAlB,EAArC,EAAyE;AAC5E;AACA,gBAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAL,CAAS,GAAT,EAAc,KAAKtB,WAAL,GAAmB,KAAKR,QAAtC,CAAjB;AACA,gBAAM+B,aAAa,GAAG,KAAKC,WAAL,CAAiB,KAAK/B,MAAtB,EAA8B2B,QAA9B,CAAtB;AACA,gBAAMK,YAAY,GAAG,KAAKvB,YAAL,CAAkBwB,WAAlB,CAA8B,KAAK5B,UAAnC,EAA+C,KAAKC,WAApD,EAAiEwB,aAAjE,CAArB;AACA,iBAAKL,KAAL,CAAWO,YAAX;AACH,WANM,MAMA,IAAI,KAAKjC,QAAL,KAAkB,CAAtB,EAAyB;AAC5B;AACA,iBAAKS,SAAL,GAAiB,KAAjB;AACA,iBAAKhB,YAAL,GAAoB,IAApB;AACA,iBAAKiC,KAAL,CAAW,KAAKnB,WAAhB;AACH;AACJ;AAED;AACJ;AACA;;;AACYmB,QAAAA,KAAK,CAACS,KAAD,EAAqB;AAC9B,cAAI;AACA,iBAAKxC,OAAL,CAAayC,QAAb,CAAsB,KAAKtC,KAA3B,EAAkCqC,KAAlC;AACH,WAFD,CAEE,OAAOd,KAAP,EAAc;AACZH,YAAAA,OAAO,CAACG,KAAR,6BAAwC,KAAKtB,YAA7C,oBAAwEoC,KAAxE,QAAkFd,KAAlF;AACA,iBAAK5B,YAAL,GAAoB,IAApB;AACA,iBAAKgB,SAAL,GAAiB,KAAjB;AACH;AACJ;AAED;AACJ;AACA;;;AACYuB,QAAAA,WAAW,CAAC/B,MAAD,EAAkBoC,CAAlB,EAAqC;AACpD,kBAAQpC,MAAR;AACI,iBAAK;AAAA;AAAA,oCAAQqC,MAAb;AACI,qBAAOD,CAAP;;AAEJ,iBAAK;AAAA;AAAA,oCAAQE,MAAb;AACI,qBAAO,IAAIV,IAAI,CAACW,GAAL,CAASH,CAAC,GAAGR,IAAI,CAACY,EAAT,GAAc,CAAvB,CAAX;;AAEJ,iBAAK;AAAA;AAAA,oCAAQC,OAAb;AACI,qBAAOb,IAAI,CAACc,GAAL,CAASN,CAAC,GAAGR,IAAI,CAACY,EAAT,GAAc,CAAvB,CAAP;;AAEJ,iBAAK;AAAA;AAAA,oCAAQG,SAAb;AACI,qBAAO,EAAEf,IAAI,CAACW,GAAL,CAASX,IAAI,CAACY,EAAL,GAAUJ,CAAnB,IAAwB,CAA1B,IAA+B,CAAtC;;AAEJ,iBAAK;AAAA;AAAA,oCAAQQ,MAAb;AACI,qBAAOR,CAAC,GAAGA,CAAX;;AAEJ,iBAAK;AAAA;AAAA,oCAAQS,OAAb;AACI,qBAAO,IAAI,CAAC,IAAIT,CAAL,KAAW,IAAIA,CAAf,CAAX;;AAEJ,iBAAK;AAAA;AAAA,oCAAQU,SAAb;AACI,qBAAOV,CAAC,GAAG,GAAJ,GAAU,IAAIA,CAAJ,GAAQA,CAAlB,GAAsB,IAAIR,IAAI,CAACmB,GAAL,CAAS,CAAC,CAAD,GAAKX,CAAL,GAAS,CAAlB,EAAqB,CAArB,IAA0B,CAA3D;;AAEJ;AACI,qBAAOA,CAAP;AAvBR;AAyBH;AAED;AACJ;AACA;;;AACIY,QAAAA,aAAa,GAAG;AACZ,cAAI,CAAC,KAAKxD,YAAV,EAAwB;AACpB,iBAAKiC,KAAL,CAAW,KAAKnB,WAAhB;AACA,iBAAKd,YAAL,GAAoB,IAApB;AACA,iBAAKgB,SAAL,GAAiB,KAAjB;AACH;AACJ;AAED;AACJ;AACA;;;AACIyC,QAAAA,MAAM,GAAG;AACL,eAAKzD,YAAL,GAAoB,IAApB;AACA,eAAKgB,SAAL,GAAiB,KAAjB;AACH;AAED;AACJ;AACA;;;AACI0C,QAAAA,YAAY,GAAW;AAAA;;AACnB,qEACkB,KAAKpD,YADvB,6BACwC,KAAKJ,OAD7C,aACwC,eAAcyB,cADtD,oBACwC,eAAcA,cAAd,EADxC,oCAEiB,KAAKxB,SAFtB,mCAGkB,KAAKU,UAHvB,oCAImB,KAAKC,WAJxB,oCAKmB,KAAKC,WAAL,CAAiB4C,OAAjB,CAAyB,CAAzB,CALnB,kCAMgB,KAAKpD,QANrB,mCAOiB,KAAKS,SAPtB,oCAQmB,KAAKhB,YARxB;AAUH;;AAnL4B,O", "sourcesContent": ["import { _decorator } from 'cc';\nimport { ActionValue, IValueInterpolator, InterpolatorFactory } from './ActionValue';\nimport { IActionHandler } from './IActionHandler';\nimport { ActionRegistry } from './ActionRegistry';\nimport { eEasing } from '../../move/IMovable';\nconst { ccclass } = _decorator;\n\n/**\n * Generic action runner that can handle any action type through the registry system\n */\nexport class GenericActionRunner {\n    private startValue: ActionValue = 0;\n    private targetValue: ActionValue = 0;\n    private elapsedTime: number = 0;\n    private isRunning: boolean = false;\n    private _isCompleted: boolean = false;\n    private handler: IActionHandler;\n    private interpolator: IValueInterpolator<ActionValue>;\n    \n    get isCompleted(): boolean {\n        return this._isCompleted;\n    }\n    \n    get actionType(): number {\n        return this.handler?.actionType || -1;\n    }\n    \n    get valueType(): string {\n        return this.handler?.valueType || 'unknown';\n    }\n    \n    constructor(\n        private owner: any,\n        private actionTypeId: number,\n        private duration: number,\n        private easing: eEasing,\n        private targetVal: ActionValue,\n        private isRandom: boolean = false,\n        private minValue?: ActionValue,\n        private maxValue?: ActionValue\n    ) {\n        this.handler = ActionRegistry.getHandler(actionTypeId);\n        if (!this.handler) {\n            throw new Error(`No handler found for action type: ${actionTypeId}`);\n        }\n        \n        // Validate target compatibility\n        if (this.handler.isValidTarget && !this.handler.isValidTarget(owner)) {\n            throw new Error(`Target object is not compatible with action type: ${actionTypeId}`);\n        }\n        \n        this.interpolator = InterpolatorFactory.create(this.handler.valueType);\n    }\n    \n    /**\n     * Start the action execution\n     */\n    start() {\n        try {\n            this.startValue = this.handler.getValue(this.owner);\n            this.targetValue = this.calculateTargetValue();\n            this.elapsedTime = 0;\n            this.isRunning = true;\n            this._isCompleted = false;\n            \n            console.log(`Started action ${this.handler.getDisplayName?.()}: ${this.startValue} -> ${this.targetValue}`);\n        } catch (error) {\n            console.error(`Failed to start action ${this.actionTypeId}:`, error);\n            this._isCompleted = true;\n            this.isRunning = false;\n        }\n    }\n    \n    /**\n     * Calculate the target value based on configuration\n     */\n    private calculateTargetValue(): ActionValue {\n        if (this.isRandom && this.minValue !== undefined && this.maxValue !== undefined) {\n            return this.interpolator.getRandomValue(this.minValue, this.maxValue);\n        }\n        return this.targetVal;\n    }\n    \n    /**\n     * Update the action (called each frame)\n     */\n    tick(dt: number) {\n        if (!this.isRunning || this._isCompleted) {\n            return;\n        }\n        \n        this.elapsedTime += dt;\n        \n        if (this.elapsedTime >= this.duration) {\n            // Action completed\n            this.isRunning = false;\n            this._isCompleted = true;\n            this.apply(this.targetValue);\n        } else if (this.handler.canInterpolate() && this.interpolator.canInterpolate()) {\n            // Interpolate between start and target values\n            const progress = Math.min(1.0, this.elapsedTime / this.duration);\n            const easedProgress = this.applyEasing(this.easing, progress);\n            const currentValue = this.interpolator.interpolate(this.startValue, this.targetValue, easedProgress);\n            this.apply(currentValue);\n        } else if (this.duration === 0) {\n            // Instant action\n            this.isRunning = false;\n            this._isCompleted = true;\n            this.apply(this.targetValue);\n        }\n    }\n    \n    /**\n     * Apply the current value to the target object\n     */\n    private apply(value: ActionValue) {\n        try {\n            this.handler.setValue(this.owner, value);\n        } catch (error) {\n            console.error(`Failed to apply action ${this.actionTypeId} with value ${value}:`, error);\n            this._isCompleted = true;\n            this.isRunning = false;\n        }\n    }\n    \n    /**\n     * Apply easing function to progress\n     */\n    private applyEasing(easing: eEasing, t: number): number {\n        switch (easing) {\n            case eEasing.Linear:\n                return t;\n\n            case eEasing.InSine:\n                return 1 - Math.cos(t * Math.PI / 2);\n\n            case eEasing.OutSine:\n                return Math.sin(t * Math.PI / 2);\n\n            case eEasing.InOutSine:\n                return -(Math.cos(Math.PI * t) - 1) / 2;\n\n            case eEasing.InQuad:\n                return t * t;\n\n            case eEasing.OutQuad:\n                return 1 - (1 - t) * (1 - t);\n\n            case eEasing.InOutQuad:\n                return t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2;\n\n            default:\n                return t;\n        }\n    }\n    \n    /**\n     * Force complete the action immediately\n     */\n    forceComplete() {\n        if (!this._isCompleted) {\n            this.apply(this.targetValue);\n            this._isCompleted = true;\n            this.isRunning = false;\n        }\n    }\n    \n    /**\n     * Cancel the action without applying the target value\n     */\n    cancel() {\n        this._isCompleted = true;\n        this.isRunning = false;\n    }\n    \n    /**\n     * Get debug information about this action runner\n     */\n    getDebugInfo(): string {\n        return `GenericActionRunner {\n            actionType: ${this.actionTypeId} (${this.handler?.getDisplayName?.()}),\n            valueType: ${this.valueType},\n            startValue: ${this.startValue},\n            targetValue: ${this.targetValue},\n            elapsedTime: ${this.elapsedTime.toFixed(3)}s,\n            duration: ${this.duration}s,\n            isRunning: ${this.isRunning},\n            isCompleted: ${this._isCompleted}\n        }`;\n    }\n}\n"]}