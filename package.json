{"name": "m2-game-client", "uuid": "82f8c37c-7b12-43af-a3b6-6be523499d35", "creator": {"version": "3.8.6"}, "dependencies": {"crypto-js": "^4.2.0", "long": "^5.3.2", "protobufjs": "^7.5.3"}, "scripts": {"build-proto:pbjs": "pbjs --dependency protobufjs/minimal.js --target static-module --force-long --keep-case --wrap commonjs --out ./assets/scripts/AutoGen/PB/cs_proto.js ../Data/Protocol/Proto/common.proto ../Data/Protocol/Proto/lobby_equip.proto ../Data/Protocol/Proto/lobby_game.proto ../Data/Protocol/Proto/lobby_item.proto ../Data/Protocol/Proto/lobby_role.proto ../Data/Protocol/Proto/lobby.proto ../Data/Protocol/Proto/userlog.proto", "build-proto:pbts": "pbts --main --out ./assets/scripts/AutoGen/PB/cs_proto.d.ts ./assets/scripts/AutoGen/PB/*.js"}, "devDependencies": {"protobufjs-cli": "^1.1.3"}}