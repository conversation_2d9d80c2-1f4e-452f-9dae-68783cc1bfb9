System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, find, EventActionRunner, ActionRegistry, GenericActionRunner, eEasing, BulletSystem, _crd, ccclass;

  function _reportPossibleCrUseOfBullet(extras) {
    _reporterNs.report("Bullet", "./Bullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitter(extras) {
    _reporterNs.report("Emitter", "./Emitter", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEventConditionType(extras) {
    _reporterNs.report("eEventConditionType", "../data/EventConditionData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventActionData(extras) {
    _reporterNs.report("EventActionData", "../data/EventActionData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventActionRunner(extras) {
    _reporterNs.report("EventActionRunner", "./EventRunner", _context.meta, extras);
  }

  function _reportPossibleCrUseOfActionRegistry(extras) {
    _reporterNs.report("ActionRegistry", "./actions", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGenericActionRunner(extras) {
    _reporterNs.report("GenericActionRunner", "./actions", _context.meta, extras);
  }

  function _reportPossibleCrUseOfActionValue(extras) {
    _reporterNs.report("ActionValue", "./actions", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEasing(extras) {
    _reporterNs.report("eEasing", "./actions", _context.meta, extras);
  }

  _export("BulletSystem", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      find = _cc.find;
    }, function (_unresolved_2) {
      EventActionRunner = _unresolved_2.EventActionRunner;
    }, function (_unresolved_3) {
      ActionRegistry = _unresolved_3.ActionRegistry;
      GenericActionRunner = _unresolved_3.GenericActionRunner;
      eEasing = _unresolved_3.eEasing;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "7cb83oXVZVMBJYZY7lJNPxV", "BulletSystem", undefined);

      __checkObsolete__(['_decorator', 'find', 'Vec3', 'Node']);

      ({
        ccclass
      } = _decorator);
      /**
       * BulletSystem - manages all bullets in the game world
       * Handles bullet creation, movement, collision, and cleanup
       */

      _export("BulletSystem", BulletSystem = class BulletSystem {
        /**
         * Initialize the bullet system
         */
        static initialize() {
          // Initialize the new action system
          (_crd && ActionRegistry === void 0 ? (_reportPossibleCrUseOfActionRegistry({
            error: Error()
          }), ActionRegistry) : ActionRegistry).initialize();
          console.log('BulletSystem initialized with new action system support');
        }
        /**
         * Enable the new action system
         */


        static enableNewActionSystem() {
          if (!(_crd && ActionRegistry === void 0 ? (_reportPossibleCrUseOfActionRegistry({
            error: Error()
          }), ActionRegistry) : ActionRegistry).getHandlerCount()) {
            (_crd && ActionRegistry === void 0 ? (_reportPossibleCrUseOfActionRegistry({
              error: Error()
            }), ActionRegistry) : ActionRegistry).initialize();
          }

          this.useNewActionSystem = true;
          console.log('New action system enabled');
        }
        /**
         * Disable the new action system (fallback to legacy)
         */


        static disableNewActionSystem() {
          this.useNewActionSystem = false;
          console.log('Fallback to legacy action system');
        }
        /**
         * Main update loop
         */


        static tick(dt) {
          this.tickEmitters(dt);
          this.tickBullets(dt);
          this.tickActionRunners(dt);
          this.tickGenericActionRunners(dt);
        }

        static tickEmitters(dt) {
          for (const emitter of this.allEmitters) {
            emitter.tick(dt);
          }
        }

        static tickBullets(dt) {
          for (const bullet of this.allBullets) {
            bullet.tick(dt);
          }
        }

        static tickActionRunners(dt) {
          for (let i = this.allActionRunners.length - 1; i >= 0; i--) {
            const runner = this.allActionRunners[i];
            runner.tick(dt);

            if (runner.isCompleted) {
              this.allActionRunners.splice(i, 1);
            }
          }
        }

        static tickGenericActionRunners(dt) {
          for (let i = this.allGenericActionRunners.length - 1; i >= 0; i--) {
            const runner = this.allGenericActionRunners[i];
            runner.tick(dt);

            if (runner.isCompleted) {
              this.allGenericActionRunners.splice(i, 1);
            }
          }
        }

        static onCreateEmitter(emitter) {
          for (let i = 0; i < this.allEmitters.length; i++) {
            if (this.allEmitters[i] === emitter) {
              return;
            }
          }

          this.allEmitters.push(emitter);

          if (!this.bulletParent || !this.bulletParent.isValid) {
            if (this.bulletParentPath.length > 0) {
              this.bulletParent = find(this.bulletParentPath);

              if (!this.bulletParent) {
                console.warn('没有找到子弹父节点请检查路径:' + this.bulletParentPath);
                this.bulletParent = emitter.node;
              }
            }
          }
        }

        static onDestroyEmitter(emitter) {
          this.allEmitters = this.allEmitters.filter(e => e !== emitter);
        }

        static onCreateBullet(bullet) {
          for (let i = 0; i < this.allBullets.length; i++) {
            if (this.allBullets[i] === bullet) {
              return;
            }
          }

          bullet.onCreate();
          this.allBullets.push(bullet);
          bullet.node.setParent(this.bulletParent, true);
        }

        static onDestroyBullet(bullet) {
          this.allBullets = this.allBullets.filter(b => b !== bullet);
        }

        static destroyAllBullets() {
          for (const bullet of this.allBullets) {
            bullet.destroySelf();
          }

          this.allBullets = [];
        }
        /**
         * EventCondition & EventAction 系统
         */
        // 记录条件状态


        static getConditionValue(conditionType) {
          return this.allConditions.get(conditionType) || 0;
        }
        /**
         * 这个函数需要在游戏各个阶段去调用设置
         */


        static setConditionValue(conditionType, value) {
          // check value changed
          const oldValue = this.allConditions.get(conditionType);

          if (oldValue !== value) {
            this.allConditions.set(conditionType, value);
            this.emit(conditionType, value);
          }
        }

        static on(conditionType, listener) {
          if (!this.conditionListeners.has(conditionType)) {
            this.conditionListeners.set(conditionType, []);
          }

          this.conditionListeners.get(conditionType).push(listener);
        }

        static off(conditionType, listener) {
          if (this.conditionListeners.has(conditionType)) {
            const listeners = this.conditionListeners.get(conditionType);
            this.conditionListeners.set(conditionType, listeners.filter(l => l !== listener));
          }
        }

        static emit(conditionType, ...args) {
          if (this.conditionListeners.has(conditionType)) {
            for (const listener of this.conditionListeners.get(conditionType)) {
              listener(...args);
            }
          }
        }

        static createActionRunner(owner, data) {
          const runner = new (_crd && EventActionRunner === void 0 ? (_reportPossibleCrUseOfEventActionRunner({
            error: Error()
          }), EventActionRunner) : EventActionRunner)(owner, data);
          runner.start();
          this.allActionRunners.push(runner);
          return runner;
        }
        /**
         * Create a new generic action runner (new system)
         */


        static createGenericActionRunner(owner, actionType, targetValue, duration = 0, easing = (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
          error: Error()
        }), eEasing) : eEasing).Linear, isRandom = false, minValue, maxValue) {
          try {
            const runner = new (_crd && GenericActionRunner === void 0 ? (_reportPossibleCrUseOfGenericActionRunner({
              error: Error()
            }), GenericActionRunner) : GenericActionRunner)(owner, actionType, duration, easing, targetValue, isRandom, minValue, maxValue);
            runner.start();
            this.allGenericActionRunners.push(runner);
            return runner;
          } catch (error) {
            console.error('Failed to create generic action runner:', error);
            return null;
          }
        }
        /**
         * Create action runner using the appropriate system based on configuration
         */


        static createActionRunnerAuto(owner, data) {
          if (this.useNewActionSystem) {
            // Use new system
            const targetValue = data.isRandom ? (data.minValue + data.maxValue) / 2 : // Use average for random values
            data.boolValue ? 1 : data.minValue; // Use boolValue or minValue

            return this.createGenericActionRunner(owner, data.actionType, targetValue, data.duration, data.easing, data.isRandom, data.minValue, data.maxValue);
          } else {
            // Use legacy system
            return this.createActionRunner(owner, data);
          }
        }
        /**
         * Get debug information about active action runners
         */


        static getActionRunnersDebugInfo() {
          const legacyCount = this.allActionRunners.length;
          const newCount = this.allGenericActionRunners.length;
          const systemInUse = this.useNewActionSystem ? 'New' : 'Legacy';
          return `Action Runners Debug Info:
System in use: ${systemInUse}
Legacy runners: ${legacyCount}
Generic runners: ${newCount}
Total active: ${legacyCount + newCount}`;
        }

      });

      /**
       * All active bullets
       */
      BulletSystem.allBullets = [];

      /**
       * All active emitters
       */
      BulletSystem.allEmitters = [];

      /**
       * All active action runners (legacy)
       */
      BulletSystem.allActionRunners = [];

      /**
       * All active generic action runners (new system)
       */
      BulletSystem.allGenericActionRunners = [];

      /**
       * Flag to control which action system to use
       * true = use new generic system, false = use legacy system
       */
      BulletSystem.useNewActionSystem = false;
      BulletSystem.bulletParentPath = 'Canvas/GameUI/bullet_root';
      // public static isEmitterEnabled: boolean = true;
      // public static isBulletEnabled: boolean = true;
      BulletSystem.bulletParent = void 0;
      BulletSystem.allConditions = new Map();
      // 条件监听者
      BulletSystem.conditionListeners = new Map();

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=d3569cffdbfaed7f8f4aa2c7af6d38f08da83d19.js.map