{"version": 3, "sources": ["cce:/internal/x/prerequisite-imports"], "names": ["requests", "request", "_err"], "mappings": ";;;;;;AACA;AAEA,YAAM,CAAC,YAAY;AACf,cAAMA,QAAQ,GAAG,CAAC,uCAAD,EAA4J,uCAA5J,EAA4T,uCAA5T,EAAge,uCAAhe,EAAioB,uCAAjoB,EAA4xB,uCAA5xB,EAAg7B,uCAAh7B,EAAwhC,uCAAxhC,EAAioC,uCAAjoC,EAAquC,uCAAruC,EAAi1C,wCAAj1C,EAA47C,wCAA57C,EAA4hD,wCAA5hD,EAAioD,wCAAjoD,EAAyuD,wCAAzuD,EAA+0D,wCAA/0D,EAAo7D,wCAAp7D,EAAwhE,wCAAxhE,EAAkoE,wCAAloE,EAAkvE,wCAAlvE,EAA61E,wCAA71E,EAA48E,wCAA58E,EAA0jF,wCAA1jF,EAAorF,wCAAprF,EAA2yF,wCAA3yF,EAA26F,wCAA36F,EAA4iG,wCAA5iG,EAA2qG,wCAA3qG,EAAqyG,wCAAryG,EAAk5G,wCAAl5G,EAA8/G,wCAA9/G,EAAymH,wCAAzmH,EAAwtH,wCAAxtH,EAAo0H,wCAAp0H,EAA86H,wCAA96H,EAAiiI,wCAAjiI,EAA2oI,wCAA3oI,EAAuvI,wCAAvvI,EAAw2I,wCAAx2I,EAAq9I,wCAAr9I,EAAgkJ,wCAAhkJ,EAA2qJ,wCAA3qJ,EAA4xJ,wCAA5xJ,EAAg5J,wCAAh5J,EAAggK,wCAAhgK,EAA6mK,wCAA7mK,EAAutK,wCAAvtK,EAAq0K,wCAAr0K,EAAk7K,wCAAl7K,EAA6hL,wCAA7hL,EAAwoL,wCAAxoL,EAAovL,wCAApvL,EAAg2L,wCAAh2L,EAAk9L,wCAAl9L,EAAkkM,wCAAlkM,EAAwrM,wCAAxrM,EAAwyM,wCAAxyM,EAA05M,wCAA15M,EAA8gN,wCAA9gN,EAAooN,wCAApoN,EAAyvN,wCAAzvN,EAA02N,wCAA12N,EAA29N,wCAA39N,EAA+kO,wCAA/kO,EAAmsO,wCAAnsO,EAAyzO,wCAAzzO,EAAy6O,wCAAz6O,EAAyhP,wCAAzhP,EAAyoP,wCAAzoP,EAA8vP,wCAA9vP,EAA+2P,wCAA/2P,EAAi+P,wCAAj+P,EAAklQ,wCAAllQ,EAA2sQ,wCAA3sQ,EAA4zQ,wCAA5zQ,EAA66Q,wCAA76Q,EAAkiR,wCAAliR,EAAkpR,wCAAlpR,EAAuwR,wCAAvwR,EAAi3R,wCAAj3R,EAA09R,wCAA19R,EAAskS,wCAAtkS,EAAurS,wCAAvrS,EAAqyS,wCAAryS,EAAq5S,wCAAr5S,EAAkgT,wCAAlgT,EAAgnT,wCAAhnT,EAAiuT,wCAAjuT,EAAk1T,wCAAl1T,EAAi8T,wCAAj8T,EAA+iU,wCAA/iU,EAA0pU,wCAA1pU,EAA0wU,wCAA1wU,EAAs3U,wCAAt3U,EAAm+U,wCAAn+U,EAAqlV,wCAArlV,EAAksV,wCAAlsV,EAAkzV,wCAAlzV,EAAg6V,wCAAh6V,EAAmhW,wCAAnhW,EAAqoW,yCAAroW,EAA+uW,yCAA/uW,EAAq2W,yCAAr2W,EAAk9W,yCAAl9W,EAAkkX,yCAAlkX,EAAsrX,yCAAtrX,EAAuyX,yCAAvyX,EAA25X,yCAA35X,EAAshY,yCAAthY,EAAqpY,yCAArpY,EAA2wY,yCAA3wY,EAAm4Y,yCAAn4Y,EAA+/Y,yCAA//Y,EAAqnZ,yCAArnZ,EAA2uZ,yCAA3uZ,EAA41Z,yCAA51Z,EAA68Z,yCAA78Z,EAA2ja,yCAA3ja,EAAwqa,yCAAxqa,EAAmxa,yCAAnxa,EAAo4a,yCAAp4a,EAAu/a,yCAAv/a,EAA8mb,yCAA9mb,EAAmub,yCAAnub,EAAs1b,yCAAt1b,EAAy8b,yCAAz8b,EAAgkc,yCAAhkc,EAAqrc,yCAArrc,EAAizc,yCAAjzc,EAA+6c,yCAA/6c,EAA6id,yCAA7id,EAA2qd,yCAA3qd,EAAgyd,yCAAhyd,EAA05d,yCAA15d,EAAqhe,yCAArhe,EAAipe,yCAAjpe,EAAwwe,yCAAxwe,EAA83e,yCAA93e,EAAw/e,yCAAx/e,EAAunf,yCAAvnf,EAAivf,yCAAjvf,EAA02f,yCAA12f,EAAw+f,yCAAx+f,EAAqmgB,yCAArmgB,EAAutgB,yCAAvtgB,EAAg0gB,yCAAh0gB,EAAw6gB,yCAAx6gB,EAA4ghB,yCAA5ghB,EAAgnhB,yCAAhnhB,EAA0thB,yCAA1thB,EAA+zhB,yCAA/zhB,EAA06hB,yCAA16hB,EAAkhiB,yCAAlhiB,EAAioiB,yCAAjoiB,EAAuuiB,yCAAvuiB,EAA40iB,yCAA50iB,EAAo7iB,yCAAp7iB,EAAwhjB,yCAAxhjB,EAA8njB,yCAA9njB,EAA0ujB,yCAA1ujB,EAA61jB,yCAA71jB,EAAu9jB,yCAAv9jB,EAA2kkB,yCAA3kkB,EAA8rkB,yCAA9rkB,EAAkzkB,yCAAlzkB,EAA66kB,yCAA76kB,EAAmilB,yCAAnilB,EAAqplB,yCAArplB,EAAwwlB,yCAAxwlB,EAAy3lB,yCAAz3lB,EAAq+lB,yCAAr+lB,EAAolmB,yCAAplmB,EAA6smB,yCAA7smB,EAA8zmB,yCAA9zmB,EAAw7mB,yCAAx7mB,EAA8inB,yCAA9inB,EAAuqnB,yCAAvqnB,EAA4xnB,yCAA5xnB,EAAg5nB,yCAAh5nB,EAAigoB,yCAAjgoB,EAAknoB,yCAAlnoB,EAA4uoB,yCAA5uoB,EAA61oB,yCAA71oB,EAA68oB,yCAA78oB,EAA8jpB,yCAA9jpB,EAAyrpB,yCAAzrpB,EAAgzpB,yCAAhzpB,EAA+6pB,yCAA/6pB,EAAmjqB,yCAAnjqB,EAAisqB,yCAAjsqB,EAAo1qB,yCAAp1qB,EAAm+qB,yCAAn+qB,EAAymrB,yCAAzmrB,EAAkurB,yCAAlurB,EAAq1rB,yCAAr1rB,EAA48rB,yCAA58rB,EAAwisB,yCAAxisB,EAA8osB,yCAA9osB,EAA4usB,yCAA5usB,EAAk1sB,yCAAl1sB,EAA87sB,yCAA97sB,EAA8itB,yCAA9itB,EAA8ptB,yCAA9ptB,EAAywtB,yCAAzwtB,EAAo3tB,yCAAp3tB,EAAi+tB,yCAAj+tB,EAA+kuB,yCAA/kuB,EAAmruB,yCAAnruB,EAAkyuB,yCAAlyuB,EAA44uB,yCAA54uB,EAAu/uB,yCAAv/uB,EAA4lvB,yCAA5lvB,EAAgsvB,yCAAhsvB,EAAgyvB,yCAAhyvB,EAAg5vB,yCAAh5vB,EAAigwB,yCAAjgwB,EAAiowB,yCAAjowB,EAAqwwB,yCAArwwB,EAAm4wB,yCAAn4wB,EAAigxB,yCAAjgxB,EAAunxB,yCAAvnxB,EAAivxB,yCAAjvxB,EAAy1xB,yCAAz1xB,EAAi8xB,yCAAj8xB,EAAuiyB,yCAAviyB,EAAkpyB,yCAAlpyB,EAA0vyB,yCAA1vyB,EAA+1yB,yCAA/1yB,EAAq9yB,yCAAr9yB,EAAqkzB,yCAArkzB,EAAqrzB,yCAArrzB,EAAkyzB,yCAAlyzB,EAAo6zB,yCAAp6zB,EAAsi0B,yCAAti0B,EAAir0B,yCAAjr0B,EAAgz0B,yCAAhz0B,EAAq70B,yCAAr70B,CAAjB;;AACA,aAAK,MAAMC,OAAX,IAAsBD,QAAtB,EAAgC;AAC5B,cAAI;AACA,kBAAMC,OAAO,EAAb;AACH,WAFD,CAEE,OAAOC,IAAP,EAAa,CACX;AACH;AACJ;AACJ,OATK,GAAN", "sourcesContent": ["\n// Auto generated represents the prerequisite imports of project modules.\n\nawait (async () => {\n    const requests = [() => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelBaseUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelLayerUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.js\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Data/Bag.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Data/BaseInfo.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Data/DataManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Data/GameLevel.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameFunc.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/Bullet.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/BulletSystem.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/Emitter.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/EventRunner.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/ObjectPool.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/actions/ActionRegistry.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/actions/ActionValue.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/actions/BulletActionHandlers.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/actions/EmitterActionHandlers.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/actions/GenericActionRunner.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/actions/IActionHandler.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameConfig.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameConst.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameEnum.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/ResourceList.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/BattleData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/BeanData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/BossChallengeData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/BossData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/BulletData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/BulletEventData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/EmitterData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/EnemyData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/EnemyWave.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/EventActionData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/EventConditionData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/EventGroupData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/GameMapData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/MainData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/MainStarData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/MapItemData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/StageData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/TrackData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/configData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/event/GameEvent.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/BattleManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/BeanManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/BossBattleManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/BossManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/BulletManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/ColliderManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/ConfigDataManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/CopyPlaneManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/EnemyManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/EventManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/GameDataManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/GameRuleManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/HurtEffectManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/ItemManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/LoadManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/LootManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/MainPlaneManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/PlaneManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/PrefabManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/SceneManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/SceneResourceManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/SkillManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/StageManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/WarAttackManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/WaveManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/move/DefaultMoveModifier.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/move/IMovable.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/move/Movable.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/scenes/GameMain.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/ParticleComponent.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/AngleComp.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/AttackPoint.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/BaseComp.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/BlastComp.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/ColliderArea.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/ColliderComp.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/Controller.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/CopyPlane.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/Entity.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/ExchangeMap.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/HDSpine.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/HDSprite.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/ImageSequence.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/NodeMove.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/PfFrameAnim.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/ScaleComp.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/TrackComponent.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/UIAnimMethods.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/XYFly.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/BoomerangBullet.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/BulletFly.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/CircleZoomFly.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/FireBullet.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/LJLaserBullet.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/AimCircleScreen.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/AimSingleLineScreen.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/BaseScreen.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/CircleScreen.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/CircleZoomScreen.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/GridScreen.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/LoftScreen.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/layer/BattleLayer.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/layer/EffectLayer.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/map/GameMapRun.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/map/loadingUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/Plane.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/ShadowPlane.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossBase.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossCollider.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossEntity.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossHurt.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossUnit.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossUnitBase.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAnim.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrBaseCom.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrComponent.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrDoctorCom.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrShieldCom.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyBase.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyComponent.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEffectComp.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEffectLayer.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEntity.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyPlane.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyPlaneRole.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyShootComponent.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/FireShells.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainRelifeComp.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainSkillBase.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/prefabs/PropEntity.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/utils/Helper.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/utils/Tools.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Anim.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Background.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Enemy.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/EnemyBullet.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/GameOver.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/GamePersistNode.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Global.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Goods.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/MainGame.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Menu.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Player.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/PlayerBullet.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/factroy/AnimFactory.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/factroy/EnemyBulletFactory.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/factroy/EnemyFactory.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/factroy/GameFactory.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/factroy/GoodsFactory.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/factroy/PlayerBulletFactory.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/gizmos/EmitterArcGizmo.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/gizmos/GizmoDrawer.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/gizmos/GizmoManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/gizmos/GizmoUtils.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/gizmos/index.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/Bootstrap.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/WorldInitializeData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/Entity.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/EntityContainer.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/EventAction.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/EventCondition.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/EventGroup.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/Messaging.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/Object.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/System.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/SystemContainer.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/TypeID.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/World.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/Level.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/LevelEventGroup.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/LevelSystem.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/bg_layer/Background.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/bg_layer/BackgroundLayer.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/event_group/ChangeBackgroundAction.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/event_group/ChangeBackgroundSpeedAction.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/event_group/LevelTimeEventCondition.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/event_group/LogEventAction.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/player/PlayerSystem.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/weapon/Weapon.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/weapon/WeaponSlot.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/PlatformSDK/DevLogin.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/PlatformSDK/DevLoginData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/PlatformSDK/IPlatformSDK.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/PlatformSDK/WXLogin.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/RootPersist.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/audioManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Utils/Logger.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/core/base/SingletonBase.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/event/EventManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/leveldata.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/DevLoginUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/LoadingUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/UIMgr.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/ClickControlUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/TopBlockInputUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/components/SelectList/uiSelect.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/components/SelectList/uiSelectItem.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/components/button/ButtonPlus.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/components/button/DragButton.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/components/list/List.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/components/list/ListItem.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/BattleUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/BottomUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/ShopUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/SkyIslandUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/TalentUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/TopUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/PlaneEquipInfoUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/PlaneEvent.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/PlaneTypes.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/PlaneUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/BagGrid.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/BagItem.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/SortTypeDropdown.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/Tabs.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/components/display/EquipDisplay.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/components/display/MergeDisplay.ts\")];\n    for (const request of requests) {\n        try {\n            await request();\n        } catch (_err) {\n            // The error should have been caught by executor.\n        }\n    }\n})();\n    "]}