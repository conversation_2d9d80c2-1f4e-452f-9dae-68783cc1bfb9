{"version": 3, "sources": ["cce:/internal/x/prerequisite-imports"], "names": ["requests", "request", "_err"], "mappings": ";;;;;;AACA;AAEA,YAAM,CAAC,YAAY;AACf,cAAMA,QAAQ,GAAG,CAAC,uCAAD,EAA4J,uCAA5J,EAA4T,uCAA5T,EAAge,uCAAhe,EAAioB,uCAAjoB,EAA4xB,uCAA5xB,EAAg7B,uCAAh7B,EAAwhC,uCAAxhC,EAAioC,uCAAjoC,EAAquC,uCAAruC,EAAi1C,wCAAj1C,EAA47C,wCAA57C,EAA4hD,wCAA5hD,EAAioD,wCAAjoD,EAAyuD,wCAAzuD,EAA+0D,wCAA/0D,EAAo7D,wCAAp7D,EAAwhE,wCAAxhE,EAAkoE,wCAAloE,EAAkvE,wCAAlvE,EAA61E,wCAA71E,EAA48E,wCAA58E,EAA0jF,wCAA1jF,EAAuqF,wCAAvqF,EAAmxF,wCAAnxF,EAA83F,wCAA93F,EAA6+F,wCAA7+F,EAAylG,wCAAzlG,EAAmsG,wCAAnsG,EAAszG,wCAAtzG,EAAg6G,wCAAh6G,EAA4gH,wCAA5gH,EAA6nH,wCAA7nH,EAA0uH,wCAA1uH,EAAq1H,wCAAr1H,EAAg8H,wCAAh8H,EAAijI,wCAAjjI,EAAqqI,wCAArqI,EAAqxI,wCAArxI,EAAk4I,wCAAl4I,EAA4+I,wCAA5+I,EAA0lJ,wCAA1lJ,EAAusJ,wCAAvsJ,EAAkzJ,wCAAlzJ,EAA65J,wCAA75J,EAAygK,wCAAzgK,EAAqnK,wCAArnK,EAAuuK,wCAAvuK,EAAu1K,wCAAv1K,EAA68K,wCAA78K,EAA6jL,wCAA7jL,EAA+qL,wCAA/qL,EAAmyL,wCAAnyL,EAAy5L,wCAAz5L,EAA8gM,wCAA9gM,EAA+nM,wCAA/nM,EAAgvM,wCAAhvM,EAAo2M,wCAAp2M,EAAw9M,wCAAx9M,EAA8kN,wCAA9kN,EAA8rN,wCAA9rN,EAA8yN,wCAA9yN,EAA85N,wCAA95N,EAAmhO,wCAAnhO,EAAooO,wCAApoO,EAAsvO,wCAAtvO,EAAu2O,wCAAv2O,EAAg+O,wCAAh+O,EAAilP,wCAAjlP,EAAksP,wCAAlsP,EAAuzP,wCAAvzP,EAAu6P,wCAAv6P,EAA4hQ,wCAA5hQ,EAAsoQ,wCAAtoQ,EAA+uQ,wCAA/uQ,EAA21Q,wCAA31Q,EAA48Q,wCAA58Q,EAA0jR,wCAA1jR,EAA0qR,wCAA1qR,EAAuxR,wCAAvxR,EAAq4R,wCAAr4R,EAAs/R,wCAAt/R,EAAumS,wCAAvmS,EAAstS,wCAAttS,EAAo0S,wCAAp0S,EAA+6S,wCAA/6S,EAA+hT,wCAA/hT,EAA2oT,wCAA3oT,EAAwvT,wCAAxvT,EAA02T,wCAA12T,EAAu9T,wCAAv9T,EAAukU,wCAAvkU,EAAqrU,wCAArrU,EAAwyU,wCAAxyU,EAA05U,wCAA15U,EAAogV,wCAApgV,EAA0nV,wCAA1nV,EAAuuV,wCAAvuV,EAAu1V,wCAAv1V,EAA28V,wCAA38V,EAA4jW,yCAA5jW,EAAgrW,yCAAhrW,EAA2yW,yCAA3yW,EAA06W,yCAA16W,EAAgiX,yCAAhiX,EAAwpX,yCAAxpX,EAAoxX,yCAApxX,EAA04X,yCAA14X,EAAggY,yCAAhgY,EAAinY,yCAAjnY,EAAkuY,yCAAluY,EAAg1Y,yCAAh1Y,EAA67Y,yCAA77Y,EAAwiZ,yCAAxiZ,EAAypZ,yCAAzpZ,EAA4wZ,yCAA5wZ,EAAm4Z,yCAAn4Z,EAAw/Z,yCAAx/Z,EAA2ma,yCAA3ma,EAA8ta,yCAA9ta,EAAq1a,yCAAr1a,EAA08a,yCAA18a,EAAskb,yCAAtkb,EAAosb,yCAApsb,EAAk0b,yCAAl0b,EAAg8b,yCAAh8b,EAAqjc,yCAArjc,EAA+qc,yCAA/qc,EAA0yc,yCAA1yc,EAAs6c,yCAAt6c,EAA6hd,yCAA7hd,EAAmpd,yCAAnpd,EAA6wd,yCAA7wd,EAA44d,yCAA54d,EAAsge,yCAAtge,EAA+ne,yCAA/ne,EAA6ve,yCAA7ve,EAA03e,yCAA13e,EAA4+e,yCAA5+e,EAAqlf,yCAArlf,EAA6rf,yCAA7rf,EAAiyf,yCAAjyf,EAAq4f,yCAAr4f,EAA++f,yCAA/+f,EAAolgB,yCAAplgB,EAA+rgB,yCAA/rgB,EAAuygB,yCAAvygB,EAAs5gB,yCAAt5gB,EAA4/gB,yCAA5/gB,EAAimhB,yCAAjmhB,EAAyshB,yCAAzshB,EAA6yhB,yCAA7yhB,EAAm5hB,yCAAn5hB,EAA+/hB,yCAA//hB,EAAkniB,yCAAlniB,EAA4uiB,yCAA5uiB,EAAg2iB,yCAAh2iB,EAAm9iB,yCAAn9iB,EAAukjB,yCAAvkjB,EAAksjB,yCAAlsjB,EAAwzjB,yCAAxzjB,EAA06jB,yCAA16jB,EAA6hkB,yCAA7hkB,EAA8okB,yCAA9okB,EAA0vkB,yCAA1vkB,EAAy2kB,yCAAz2kB,EAAk+kB,yCAAl+kB,EAAmllB,yCAAnllB,EAA6slB,yCAA7slB,EAAm0lB,yCAAn0lB,EAA47lB,yCAA57lB,EAAijmB,yCAAjjmB,EAAqqmB,yCAArqmB,EAAsxmB,yCAAtxmB,EAAu4mB,yCAAv4mB,EAAignB,yCAAjgnB,EAAknnB,yCAAlnnB,EAAkunB,yCAAlunB,EAAm1nB,yCAAn1nB,EAA88nB,yCAA98nB,EAAqkoB,yCAArkoB,EAAosoB,yCAApsoB,EAAw0oB,yCAAx0oB,EAAs9oB,yCAAt9oB,EAAympB,yCAAzmpB,EAAwvpB,yCAAxvpB,EAA83pB,yCAA93pB,EAAu/pB,yCAAv/pB,EAA0mqB,yCAA1mqB,EAAiuqB,yCAAjuqB,EAA6zqB,yCAA7zqB,EAAm6qB,yCAAn6qB,EAAigrB,yCAAjgrB,EAAumrB,yCAAvmrB,EAAmtrB,yCAAntrB,EAAm0rB,yCAAn0rB,EAAm7rB,yCAAn7rB,EAA8hsB,yCAA9hsB,EAAyosB,yCAAzosB,EAAsvsB,yCAAtvsB,EAAo2sB,yCAAp2sB,EAAw8sB,yCAAx8sB,EAAujtB,yCAAvjtB,EAAiqtB,yCAAjqtB,EAA4wtB,yCAA5wtB,EAAi3tB,yCAAj3tB,EAAq9tB,yCAAr9tB,EAAqjuB,yCAArjuB,EAAqquB,yCAArquB,EAAsxuB,yCAAtxuB,EAAs5uB,yCAAt5uB,EAA0hvB,yCAA1hvB,EAAwpvB,yCAAxpvB,EAAsxvB,yCAAtxvB,EAA44vB,yCAA54vB,EAAsgwB,yCAAtgwB,EAA8mwB,yCAA9mwB,EAAstwB,yCAAttwB,EAA4zwB,yCAA5zwB,EAAu6wB,yCAAv6wB,EAA+gxB,yCAA/gxB,EAAonxB,yCAApnxB,EAA0uxB,yCAA1uxB,EAA01xB,yCAA11xB,EAA08xB,yCAA18xB,EAAujyB,yCAAvjyB,EAAyryB,yCAAzryB,EAA2zyB,yCAA3zyB,EAAs8yB,yCAAt8yB,EAAqkzB,yCAArkzB,EAA0szB,yCAA1szB,CAAjB;;AACA,aAAK,MAAMC,OAAX,IAAsBD,QAAtB,EAAgC;AAC5B,cAAI;AACA,kBAAMC,OAAO,EAAb;AACH,WAFD,CAEE,OAAOC,IAAP,EAAa,CACX;AACH;AACJ;AACJ,OATK,GAAN", "sourcesContent": ["\n// Auto generated represents the prerequisite imports of project modules.\n\nawait (async () => {\n    const requests = [() => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelBaseUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelLayerUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/editor/level/LevelUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.js\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Data/Bag.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Data/BaseInfo.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Data/DataManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Data/GameLevel.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameFunc.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/GameIns.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/Bullet.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/BulletSystem.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/Emitter.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/EventRunner.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/ObjectPool.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameConfig.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameConst.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/GameEnum.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/const/ResourceList.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/BattleData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/BeanData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/BossChallengeData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/BossData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/BulletData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/BulletEventData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/EmitterData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/EnemyData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/EnemyWave.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/EventActionData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/EventConditionData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/EventGroupData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/GameMapData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/MainData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/MainStarData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/MapItemData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/StageData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/TrackData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/data/configData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/event/GameEvent.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/BattleManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/BeanManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/BossBattleManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/BossManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/BulletManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/ColliderManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/ConfigDataManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/CopyPlaneManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/EnemyManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/EventManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/GameDataManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/GameRuleManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/HurtEffectManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/ItemManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/LoadManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/LootManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/MainPlaneManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/PlaneManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/PrefabManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/SceneManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/SceneResourceManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/SkillManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/StageManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/WarAttackManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/manager/WaveManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/move/DefaultMoveModifier.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/move/IMovable.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/move/Movable.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/scenes/GameMain.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/ParticleComponent.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/AngleComp.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/AttackPoint.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/BaseComp.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/BlastComp.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/ColliderArea.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/ColliderComp.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/Controller.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/CopyPlane.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/Entity.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/ExchangeMap.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/HDSpine.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/HDSprite.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/ImageSequence.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/NodeMove.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/PfFrameAnim.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/ScaleComp.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/TrackComponent.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/UIAnimMethods.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/base/XYFly.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/BoomerangBullet.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/BulletFly.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/CircleZoomFly.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/FireBullet.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bullet/LJLaserBullet.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/AimCircleScreen.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/AimSingleLineScreen.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/BaseScreen.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/CircleScreen.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/CircleZoomScreen.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/GridScreen.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/bulletDanmu/LoftScreen.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/layer/BattleLayer.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/layer/EffectLayer.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/map/GameMapRun.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/map/loadingUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/Plane.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/ShadowPlane.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossBase.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossCollider.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossEntity.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossHurt.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossUnit.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossUnitBase.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAnim.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrBaseCom.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrComponent.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrDoctorCom.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyAttrShieldCom.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyBase.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyComponent.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEffectComp.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEffectLayer.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyEntity.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyPlane.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyPlaneRole.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyShootComponent.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/FireShells.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainRelifeComp.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainSkillBase.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/ui/prefabs/PropEntity.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/utils/Helper.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/utils/Tools.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameInstance.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Anim.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Background.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Enemy.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/EnemyBullet.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/GameOver.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/GamePersistNode.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Global.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Goods.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/MainGame.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Menu.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/Player.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/PlayerBullet.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/factroy/AnimFactory.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/factroy/EnemyBulletFactory.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/factroy/EnemyFactory.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/factroy/GameFactory.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/factroy/GoodsFactory.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/factroy/PlayerBulletFactory.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/gizmos/EmitterArcGizmo.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/gizmos/GizmoDrawer.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/gizmos/GizmoManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/gizmos/GizmoUtils.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/gizmos/index.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/Bootstrap.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/WorldInitializeData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/Entity.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/EntityContainer.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/EventAction.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/EventCondition.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/EventGroup.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/Messaging.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/Object.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/System.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/SystemContainer.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/TypeID.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/base/World.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/Level.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/LevelEventGroup.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/LevelSystem.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/bg_layer/Background.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/bg_layer/BackgroundLayer.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/event_group/ChangeBackgroundAction.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/event_group/ChangeBackgroundSpeedAction.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/event_group/LevelTimeEventCondition.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/level/event_group/LogEventAction.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/player/PlayerSystem.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/weapon/Weapon.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/GameOld/world/weapon/WeaponSlot.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/IMgr.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Luban/LubanMgr.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/MainUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Network/NetMgr.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/PlatformSDK/DevLogin.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/PlatformSDK/DevLoginData.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/PlatformSDK/IPlatformSDK.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/PlatformSDK/WXLogin.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/RootPersist.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ResUpdate/audioManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Utils/Logger.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/core/base/SingletonBase.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/event/EventManager.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/leveldata/leveldata.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/DevLoginUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/LoadingUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/UIMgr.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/ClickControlUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/TopBlockInputUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/components/SelectList/uiSelect.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/components/SelectList/uiSelectItem.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/components/button/ButtonPlus.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/components/button/DragButton.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/components/list/List.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/common/components/list/ListItem.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/BattleUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/BottomUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/ShopUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/SkyIslandUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/TalentUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/TopUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/PlaneEquipInfoUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/PlaneEvent.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/PlaneTypes.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/PlaneUI.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/BagGrid.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/BagItem.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/SortTypeDropdown.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/components/back_pack/Tabs.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/components/display/EquipDisplay.ts\"), () => import(\"file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/ui/main/plane/components/display/MergeDisplay.ts\")];\n    for (const request of requests) {\n        try {\n            await request();\n        } catch (_err) {\n            // The error should have been caught by executor.\n        }\n    }\n})();\n    "]}