var VueCompilerDOM=function(e){"use strict";function t(e,t){const n=Object.create(null),o=e.split(",");for(let r=0;r<o.length;r++)n[o[r]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}const n={},o=()=>{},r=()=>!1,s=/^on[^a-z]/,i=e=>s.test(e),c=Object.assign,l=Array.isArray,a=e=>"string"==typeof e,p=e=>"symbol"==typeof e,u=e=>null!==e&&"object"==typeof e,f=t(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),d=t("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),h=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},m=/-(\w)/g,g=h((e=>e.replace(m,((e,t)=>t?t.toUpperCase():"")))),y=/\B([A-Z])/g,v=h((e=>e.replace(y,"-$1").toLowerCase())),S=h((e=>e.charAt(0).toUpperCase()+e.slice(1))),E=h((e=>e?`on${S(e)}`:""));const b=/;(?![^(]*\))/g,T=/:([^]+)/,x=/\/\*[^]*?\*\//g;const N=t("html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot"),_=t("svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view"),k=t("area,base,br,col,embed,hr,img,input,link,meta,param,source,track,wbr");function O(e){throw e}function C(e){}function I(e,t,n,o){const r=new SyntaxError(String(e));return r.code=e,r.loc=t,r}const M=Symbol(""),R=Symbol(""),P=Symbol(""),w=Symbol(""),$=Symbol(""),L=Symbol(""),V=Symbol(""),A=Symbol(""),D=Symbol(""),B=Symbol(""),F=Symbol(""),j=Symbol(""),H=Symbol(""),W=Symbol(""),K=Symbol(""),U=Symbol(""),J=Symbol(""),G=Symbol(""),z=Symbol(""),Y=Symbol(""),Z=Symbol(""),q=Symbol(""),X=Symbol(""),Q=Symbol(""),ee=Symbol(""),te=Symbol(""),ne=Symbol(""),oe=Symbol(""),re=Symbol(""),se=Symbol(""),ie=Symbol(""),ce=Symbol(""),le=Symbol(""),ae=Symbol(""),pe=Symbol(""),ue=Symbol(""),fe=Symbol(""),de=Symbol(""),he=Symbol(""),me={[M]:"Fragment",[R]:"Teleport",[P]:"Suspense",[w]:"KeepAlive",[$]:"BaseTransition",[L]:"openBlock",[V]:"createBlock",[A]:"createElementBlock",[D]:"createVNode",[B]:"createElementVNode",[F]:"createCommentVNode",[j]:"createTextVNode",[H]:"createStaticVNode",[W]:"resolveComponent",[K]:"resolveDynamicComponent",[U]:"resolveDirective",[J]:"resolveFilter",[G]:"withDirectives",[z]:"renderList",[Y]:"renderSlot",[Z]:"createSlots",[q]:"toDisplayString",[X]:"mergeProps",[Q]:"normalizeClass",[ee]:"normalizeStyle",[te]:"normalizeProps",[ne]:"guardReactiveProps",[oe]:"toHandlers",[re]:"camelize",[se]:"capitalize",[ie]:"toHandlerKey",[ce]:"setBlockTracking",[le]:"pushScopeId",[ae]:"popScopeId",[pe]:"withCtx",[ue]:"unref",[fe]:"isRef",[de]:"withMemo",[he]:"isMemoSame"};function ge(e){Object.getOwnPropertySymbols(e).forEach((t=>{me[t]=e[t]}))}const ye={source:"",start:{line:1,column:1,offset:0},end:{line:1,column:1,offset:0}};function ve(e,t=ye){return{type:0,children:e,helpers:new Set,components:[],directives:[],hoists:[],imports:[],cached:0,temps:0,codegenNode:void 0,loc:t}}function Se(e,t,n,o,r,s,i,c=!1,l=!1,a=!1,p=ye){return e&&(c?(e.helper(L),e.helper(Re(e.inSSR,a))):e.helper(Me(e.inSSR,a)),i&&e.helper(G)),{type:13,tag:t,props:n,children:o,patchFlag:r,dynamicProps:s,directives:i,isBlock:c,disableTracking:l,isComponent:a,loc:p}}function Ee(e,t=ye){return{type:17,loc:t,elements:e}}function be(e,t=ye){return{type:15,loc:t,properties:e}}function Te(e,t){return{type:16,loc:ye,key:a(e)?xe(e,!0):e,value:t}}function xe(e,t=!1,n=ye,o=0){return{type:4,loc:n,content:e,isStatic:t,constType:t?3:o}}function Ne(e,t=ye){return{type:8,loc:t,children:e}}function _e(e,t=[],n=ye){return{type:14,loc:n,callee:e,arguments:t}}function ke(e,t,n=!1,o=!1,r=ye){return{type:18,params:e,returns:t,newline:n,isSlot:o,loc:r}}function Oe(e,t,n,o=!0){return{type:19,test:e,consequent:t,alternate:n,newline:o,loc:ye}}function Ce(e,t,n=!1){return{type:20,index:e,value:t,isVNode:n,loc:ye}}function Ie(e){return{type:21,body:e,loc:ye}}function Me(e,t){return e||t?D:B}function Re(e,t){return e||t?V:A}function Pe(e,{helper:t,removeHelper:n,inSSR:o}){e.isBlock||(e.isBlock=!0,n(Me(o,e.isComponent)),t(L),t(Re(o,e.isComponent)))}const we=e=>4===e.type&&e.isStatic,$e=(e,t)=>e===t||e===v(t);function Le(e){return $e(e,"Teleport")?R:$e(e,"Suspense")?P:$e(e,"KeepAlive")?w:$e(e,"BaseTransition")?$:void 0}const Ve=/^\d|[^\$\w]/,Ae=e=>!Ve.test(e),De=/[A-Za-z_$\xA0-\uFFFF]/,Be=/[\.\?\w$\xA0-\uFFFF]/,Fe=/\s+[.[]\s*|\s*[.[]\s+/g,je=e=>{e=e.trim().replace(Fe,(e=>e.trim()));let t=0,n=[],o=0,r=0,s=null;for(let i=0;i<e.length;i++){const c=e.charAt(i);switch(t){case 0:if("["===c)n.push(t),t=1,o++;else if("("===c)n.push(t),t=2,r++;else if(!(0===i?De:Be).test(c))return!1;break;case 1:"'"===c||'"'===c||"`"===c?(n.push(t),t=3,s=c):"["===c?o++:"]"===c&&(--o||(t=n.pop()));break;case 2:if("'"===c||'"'===c||"`"===c)n.push(t),t=3,s=c;else if("("===c)r++;else if(")"===c){if(i===e.length-1)return!1;--r||(t=n.pop())}break;case 3:c===s&&(t=n.pop(),s=null)}}return!o&&!r},He=o,We=je;function Ke(e,t,n){const o={source:e.source.slice(t,t+n),start:Ue(e.start,e.source,t),end:e.end};return null!=n&&(o.end=Ue(e.start,e.source,t+n)),o}function Ue(e,t,n=t.length){return Je(c({},e),t,n)}function Je(e,t,n=t.length){let o=0,r=-1;for(let s=0;s<n;s++)10===t.charCodeAt(s)&&(o++,r=s);return e.offset+=n,e.line+=o,e.column=-1===r?e.column+n:n-r,e}function Ge(e,t,n=!1){for(let o=0;o<e.props.length;o++){const r=e.props[o];if(7===r.type&&(n||r.exp)&&(a(t)?r.name===t:t.test(r.name)))return r}}function ze(e,t,n=!1,o=!1){for(let r=0;r<e.props.length;r++){const s=e.props[r];if(6===s.type){if(n)continue;if(s.name===t&&(s.value||o))return s}else if("bind"===s.name&&(s.exp||o)&&Ye(s.arg,t))return s}}function Ye(e,t){return!(!e||!we(e)||e.content!==t)}function Ze(e){return e.props.some((e=>!(7!==e.type||"bind"!==e.name||e.arg&&4===e.arg.type&&e.arg.isStatic)))}function qe(e){return 5===e.type||2===e.type}function Xe(e){return 7===e.type&&"slot"===e.name}function Qe(e){return 1===e.type&&3===e.tagType}function et(e){return 1===e.type&&2===e.tagType}const tt=new Set([te,ne]);function nt(e,t=[]){if(e&&!a(e)&&14===e.type){const n=e.callee;if(!a(n)&&tt.has(n))return nt(e.arguments[0],t.concat(e))}return[e,t]}function ot(e,t,n){let o,r,s=13===e.type?e.props:e.arguments[2],i=[];if(s&&!a(s)&&14===s.type){const e=nt(s);s=e[0],i=e[1],r=i[i.length-1]}if(null==s||a(s))o=be([t]);else if(14===s.type){const e=s.arguments[0];a(e)||15!==e.type?s.callee===oe?o=_e(n.helper(X),[be([t]),s]):s.arguments.unshift(be([t])):rt(t,e)||e.properties.unshift(t),!o&&(o=s)}else 15===s.type?(rt(t,s)||s.properties.unshift(t),o=s):(o=_e(n.helper(X),[be([t]),s]),r&&r.callee===ne&&(r=i[i.length-2]));13===e.type?r?r.arguments[0]=o:e.props=o:r?r.arguments[0]=o:e.arguments[2]=o}function rt(e,t){let n=!1;if(4===e.key.type){const o=e.key.content;n=t.properties.some((e=>4===e.key.type&&e.key.content===o))}return n}function st(e,t){return`_${t}_${e.replace(/[^\w]/g,((t,n)=>"-"===t?"_":e.charCodeAt(n).toString()))}`}function it(e){return 14===e.type&&e.callee===de?e.arguments[1].returns:e}const ct={COMPILER_IS_ON_ELEMENT:{message:'Platform-native elements with "is" prop will no longer be treated as components in Vue 3 unless the "is" value is explicitly prefixed with "vue:".',link:"https://v3-migration.vuejs.org/breaking-changes/custom-elements-interop.html"},COMPILER_V_BIND_SYNC:{message:e=>`.sync modifier for v-bind has been removed. Use v-model with argument instead. \`v-bind:${e}.sync\` should be changed to \`v-model:${e}\`.`,link:"https://v3-migration.vuejs.org/breaking-changes/v-model.html"},COMPILER_V_BIND_PROP:{message:".prop modifier for v-bind has been removed and no longer necessary. Vue 3 will automatically set a binding as DOM property when appropriate."},COMPILER_V_BIND_OBJECT_ORDER:{message:'v-bind="obj" usage is now order sensitive and behaves like JavaScript object spread: it will now overwrite an existing non-mergeable attribute that appears before v-bind in the case of conflict. To retain 2.x behavior, move v-bind to make it the first attribute. You can also suppress this warning if the usage is intended.',link:"https://v3-migration.vuejs.org/breaking-changes/v-bind.html"},COMPILER_V_ON_NATIVE:{message:".native modifier for v-on has been removed as is no longer necessary.",link:"https://v3-migration.vuejs.org/breaking-changes/v-on-native-modifier-removed.html"},COMPILER_V_IF_V_FOR_PRECEDENCE:{message:"v-if / v-for precedence when used on the same element has changed in Vue 3: v-if now takes higher precedence and will no longer have access to v-for scope variables. It is best to avoid the ambiguity with <template> tags or use a computed property that filters v-for data source.",link:"https://v3-migration.vuejs.org/breaking-changes/v-if-v-for.html"},COMPILER_NATIVE_TEMPLATE:{message:"<template> with no special directives will render as a native template element instead of its inner content in Vue 3."},COMPILER_INLINE_TEMPLATE:{message:'"inline-template" has been removed in Vue 3.',link:"https://v3-migration.vuejs.org/breaking-changes/inline-template-attribute.html"},COMPILER_FILTER:{message:'filters have been removed in Vue 3. The "|" symbol will be treated as native JavaScript bitwise OR operator. Use method calls or computed properties instead.',link:"https://v3-migration.vuejs.org/breaking-changes/filters.html"}};function lt(e,t){const n=t.options?t.options.compatConfig:t.compatConfig,o=n&&n[e];return"MODE"===e?o||3:o}function at(e,t){const n=lt("MODE",t),o=lt(e,t);return 3===n?!0===o:!1!==o}function pt(e,t,n,...o){return at(e,t)}const ut=/&(gt|lt|amp|apos|quot);/g,ft={gt:">",lt:"<",amp:"&",apos:"'",quot:'"'},dt={delimiters:["{{","}}"],getNamespace:()=>0,getTextMode:()=>0,isVoidTag:r,isPreTag:r,isCustomElement:r,decodeEntities:e=>e.replace(ut,((e,t)=>ft[t])),onError:O,onWarn:C,comments:!1};function ht(e,t={}){const n=function(e,t){const n=c({},dt);let o;for(o in t)n[o]=void 0===t[o]?dt[o]:t[o];return{options:n,column:1,line:1,offset:0,originalSource:e,source:e,inPre:!1,inVPre:!1,onWarn:n.onWarn}}(e,t),o=It(n);return ve(mt(n,0,[]),Mt(n,o))}function mt(e,t,n){const o=Rt(n),r=o?o.ns:0,s=[];for(;!At(e,t,n);){const i=e.source;let c;if(0===t||1===t)if(!e.inVPre&&Pt(i,e.options.delimiters[0]))c=kt(e,t);else if(0===t&&"<"===i[0])if(1===i.length);else if("!"===i[1])c=Pt(i,"\x3c!--")?vt(e):Pt(i,"<!DOCTYPE")?St(e):Pt(i,"<![CDATA[")&&0!==r?yt(e,n):St(e);else if("/"===i[1])if(2===i.length);else{if(">"===i[2]){wt(e,3);continue}if(/[a-z]/i.test(i[2])){xt(e,bt.End,o);continue}Vt(e,12,2),c=St(e)}else/[a-z]/i.test(i[1])?(c=Et(e,n),at("COMPILER_NATIVE_TEMPLATE",e)&&c&&"template"===c.tag&&!c.props.some((e=>7===e.type&&Tt(e.name)))&&(c=c.children)):"?"===i[1]&&(Vt(e,21,1),c=St(e));if(c||(c=Ot(e,t)),l(c))for(let e=0;e<c.length;e++)gt(s,c[e]);else gt(s,c)}let i=!1;if(2!==t&&1!==t){const t="preserve"!==e.options.whitespace;for(let n=0;n<s.length;n++){const o=s[n];if(2===o.type)if(e.inPre)o.content=o.content.replace(/\r\n/g,"\n");else if(/[^\t\r\n\f ]/.test(o.content))t&&(o.content=o.content.replace(/[\t\r\n\f ]+/g," "));else{const e=s[n-1],r=s[n+1];!e||!r||t&&(3===e.type&&3===r.type||3===e.type&&1===r.type||1===e.type&&3===r.type||1===e.type&&1===r.type&&/[\r\n]/.test(o.content))?(i=!0,s[n]=null):o.content=" "}else 3!==o.type||e.options.comments||(i=!0,s[n]=null)}if(e.inPre&&o&&e.options.isPreTag(o.tag)){const e=s[0];e&&2===e.type&&(e.content=e.content.replace(/^\r?\n/,""))}}return i?s.filter(Boolean):s}function gt(e,t){if(2===t.type){const n=Rt(e);if(n&&2===n.type&&n.loc.end.offset===t.loc.start.offset)return n.content+=t.content,n.loc.end=t.loc.end,void(n.loc.source+=t.loc.source)}e.push(t)}function yt(e,t){wt(e,9);const n=mt(e,3,t);return 0===e.source.length||wt(e,3),n}function vt(e){const t=It(e);let n;const o=/--(\!)?>/.exec(e.source);if(o){n=e.source.slice(4,o.index);const t=e.source.slice(0,o.index);let r=1,s=0;for(;-1!==(s=t.indexOf("\x3c!--",r));)wt(e,s-r+1),r=s+1;wt(e,o.index+o[0].length-r+1)}else n=e.source.slice(4),wt(e,e.source.length);return{type:3,content:n,loc:Mt(e,t)}}function St(e){const t=It(e),n="?"===e.source[1]?1:2;let o;const r=e.source.indexOf(">");return-1===r?(o=e.source.slice(n),wt(e,e.source.length)):(o=e.source.slice(n,r),wt(e,r+1)),{type:3,content:o,loc:Mt(e,t)}}function Et(e,t){const n=e.inPre,o=e.inVPre,r=Rt(t),s=xt(e,bt.Start,r),i=e.inPre&&!n,c=e.inVPre&&!o;if(s.isSelfClosing||e.options.isVoidTag(s.tag))return i&&(e.inPre=!1),c&&(e.inVPre=!1),s;t.push(s);const l=e.options.getTextMode(s,r),a=mt(e,l,t);t.pop();{const t=s.props.find((e=>6===e.type&&"inline-template"===e.name));if(t&&pt("COMPILER_INLINE_TEMPLATE",e)){const n=Mt(e,s.loc.end);t.value={type:2,content:n.source,loc:n}}}if(s.children=a,Dt(e.source,s.tag))xt(e,bt.End,r);else if(0===e.source.length&&"script"===s.tag.toLowerCase()){const e=a[0];e&&Pt(e.loc.source,"\x3c!--")}return s.loc=Mt(e,s.loc.start),i&&(e.inPre=!1),c&&(e.inVPre=!1),s}var bt=(e=>(e[e.Start=0]="Start",e[e.End=1]="End",e))(bt||{});const Tt=t("if,else,else-if,for,slot");function xt(e,t,n){const o=It(e),r=/^<\/?([a-z][^\t\r\n\f />]*)/i.exec(e.source),s=r[1],i=e.options.getNamespace(s,n);wt(e,r[0].length),$t(e);const l=It(e),a=e.source;e.options.isPreTag(s)&&(e.inPre=!0);let p=Nt(e,t);0===t&&!e.inVPre&&p.some((e=>7===e.type&&"pre"===e.name))&&(e.inVPre=!0,c(e,l),e.source=a,p=Nt(e,t).filter((e=>"v-pre"!==e.name)));let u=!1;if(0===e.source.length||(u=Pt(e.source,"/>"),wt(e,u?2:1)),1===t)return;let f=0;return e.inVPre||("slot"===s?f=2:"template"===s?p.some((e=>7===e.type&&Tt(e.name)))&&(f=3):function(e,t,n){const o=n.options;if(o.isCustomElement(e))return!1;if("component"===e||/^[A-Z]/.test(e)||Le(e)||o.isBuiltInComponent&&o.isBuiltInComponent(e)||o.isNativeTag&&!o.isNativeTag(e))return!0;for(let r=0;r<t.length;r++){const e=t[r];if(6===e.type){if("is"===e.name&&e.value){if(e.value.content.startsWith("vue:"))return!0;if(pt("COMPILER_IS_ON_ELEMENT",n))return!0}}else{if("is"===e.name)return!0;if("bind"===e.name&&Ye(e.arg,"is")&&pt("COMPILER_IS_ON_ELEMENT",n))return!0}}}(s,p,e)&&(f=1)),{type:1,ns:i,tag:s,tagType:f,props:p,isSelfClosing:u,children:[],loc:Mt(e,o),codegenNode:void 0}}function Nt(e,t){const n=[],o=new Set;for(;e.source.length>0&&!Pt(e.source,">")&&!Pt(e.source,"/>");){if(Pt(e.source,"/")){wt(e,1),$t(e);continue}const r=_t(e,o);6===r.type&&r.value&&"class"===r.name&&(r.value.content=r.value.content.replace(/\s+/g," ").trim()),0===t&&n.push(r),/^[^\t\r\n\f />]/.test(e.source),$t(e)}return n}function _t(e,t){var n;const o=It(e),r=/^[^\t\r\n\f />][^\t\r\n\f />=]*/.exec(e.source)[0];t.has(r),t.add(r);{const t=/["'<]/g;let n;for(;n=t.exec(r);)Vt(e,17,n.index)}let s;wt(e,r.length),/^[\t\r\n\f ]*=/.test(e.source)&&($t(e),wt(e,1),$t(e),s=function(e){const t=It(e);let n;const o=e.source[0],r='"'===o||"'"===o;if(r){wt(e,1);const t=e.source.indexOf(o);-1===t?n=Ct(e,e.source.length,4):(n=Ct(e,t,4),wt(e,1))}else{const t=/^[^\t\r\n\f >]+/.exec(e.source);if(!t)return;const o=/["'<=`]/g;let r;for(;r=o.exec(t[0]);)Vt(e,18,r.index);n=Ct(e,t[0].length,4)}return{content:n,isQuoted:r,loc:Mt(e,t)}}(e));const i=Mt(e,o);if(!e.inVPre&&/^(v-[A-Za-z0-9-]|:|\.|@|#)/.test(r)){const t=/(?:^v-([a-z0-9-]+))?(?:(?::|^\.|^@|^#)(\[[^\]]+\]|[^\.]+))?(.+)?$/i.exec(r);let c,l=Pt(r,"."),a=t[1]||(l||Pt(r,":")?"bind":Pt(r,"@")?"on":"slot");if(t[2]){const s="slot"===a,i=r.lastIndexOf(t[2],r.length-((null==(n=t[3])?void 0:n.length)||0)),l=Mt(e,Lt(e,o,i),Lt(e,o,i+t[2].length+(s&&t[3]||"").length));let p=t[2],u=!0;p.startsWith("[")?(u=!1,p.endsWith("]")?p=p.slice(1,p.length-1):(Vt(e,27),p=p.slice(1))):s&&(p+=t[3]||""),c={type:4,content:p,isStatic:u,constType:u?3:0,loc:l}}if(s&&s.isQuoted){const e=s.loc;e.start.offset++,e.start.column++,e.end=Ue(e.start,s.content),e.source=e.source.slice(1,-1)}const p=t[3]?t[3].slice(1).split("."):[];return l&&p.push("prop"),"bind"===a&&c&&p.includes("sync")&&pt("COMPILER_V_BIND_SYNC",e,0)&&(a="model",p.splice(p.indexOf("sync"),1)),{type:7,name:a,exp:s&&{type:4,content:s.content,isStatic:!1,constType:0,loc:s.loc},arg:c,modifiers:p,loc:i}}return!e.inVPre&&Pt(r,"v-"),{type:6,name:r,value:s&&{type:2,content:s.content,loc:s.loc},loc:i}}function kt(e,t){const[n,o]=e.options.delimiters,r=e.source.indexOf(o,n.length);if(-1===r)return;const s=It(e);wt(e,n.length);const i=It(e),c=It(e),l=r-n.length,a=e.source.slice(0,l),p=Ct(e,l,t),u=p.trim(),f=p.indexOf(u);f>0&&Je(i,a,f);return Je(c,a,l-(p.length-u.length-f)),wt(e,o.length),{type:5,content:{type:4,isStatic:!1,constType:0,content:u,loc:Mt(e,i,c)},loc:Mt(e,s)}}function Ot(e,t){const n=3===t?["]]>"]:["<",e.options.delimiters[0]];let o=e.source.length;for(let s=0;s<n.length;s++){const t=e.source.indexOf(n[s],1);-1!==t&&o>t&&(o=t)}const r=It(e);return{type:2,content:Ct(e,o,t),loc:Mt(e,r)}}function Ct(e,t,n){const o=e.source.slice(0,t);return wt(e,t),2!==n&&3!==n&&o.includes("&")?e.options.decodeEntities(o,4===n):o}function It(e){const{column:t,line:n,offset:o}=e;return{column:t,line:n,offset:o}}function Mt(e,t,n){return{start:t,end:n=n||It(e),source:e.originalSource.slice(t.offset,n.offset)}}function Rt(e){return e[e.length-1]}function Pt(e,t){return e.startsWith(t)}function wt(e,t){const{source:n}=e;Je(e,n,t),e.source=n.slice(t)}function $t(e){const t=/^[\t\r\n\f ]+/.exec(e.source);t&&wt(e,t[0].length)}function Lt(e,t,n){return Ue(t,e.originalSource.slice(t.offset,n),n)}function Vt(e,t,n,o=It(e)){n&&(o.offset+=n,o.column+=n),e.options.onError(I(t,{start:o,end:o,source:""}))}function At(e,t,n){const o=e.source;switch(t){case 0:if(Pt(o,"</"))for(let e=n.length-1;e>=0;--e)if(Dt(o,n[e].tag))return!0;break;case 1:case 2:{const e=Rt(n);if(e&&Dt(o,e.tag))return!0;break}case 3:if(Pt(o,"]]>"))return!0}return!o}function Dt(e,t){return Pt(e,"</")&&e.slice(2,2+t.length).toLowerCase()===t.toLowerCase()&&/[\t\r\n\f />]/.test(e[2+t.length]||">")}function Bt(e,t){jt(e,t,Ft(e,e.children[0]))}function Ft(e,t){const{children:n}=e;return 1===n.length&&1===t.type&&!et(t)}function jt(e,t,n=!1){const{children:o}=e,r=o.length;let s=0;for(let i=0;i<o.length;i++){const e=o[i];if(1===e.type&&0===e.tagType){const o=n?0:Ht(e,t);if(o>0){if(o>=2){e.codegenNode.patchFlag="-1",e.codegenNode=t.hoist(e.codegenNode),s++;continue}}else{const n=e.codegenNode;if(13===n.type){const o=Gt(n);if((!o||512===o||1===o)&&Ut(e,t)>=2){const o=Jt(e);o&&(n.props=t.hoist(o))}n.dynamicProps&&(n.dynamicProps=t.hoist(n.dynamicProps))}}}if(1===e.type){const n=1===e.tagType;n&&t.scopes.vSlot++,jt(e,t),n&&t.scopes.vSlot--}else if(11===e.type)jt(e,t,1===e.children.length);else if(9===e.type)for(let n=0;n<e.branches.length;n++)jt(e.branches[n],t,1===e.branches[n].children.length)}s&&t.transformHoist&&t.transformHoist(o,t,e),s&&s===r&&1===e.type&&0===e.tagType&&e.codegenNode&&13===e.codegenNode.type&&l(e.codegenNode.children)&&(e.codegenNode.children=t.hoist(Ee(e.codegenNode.children)))}function Ht(e,t){const{constantCache:n}=t;switch(e.type){case 1:if(0!==e.tagType)return 0;const o=n.get(e);if(void 0!==o)return o;const r=e.codegenNode;if(13!==r.type)return 0;if(r.isBlock&&"svg"!==e.tag&&"foreignObject"!==e.tag)return 0;if(Gt(r))return n.set(e,0),0;{let o=3;const s=Ut(e,t);if(0===s)return n.set(e,0),0;s<o&&(o=s);for(let r=0;r<e.children.length;r++){const s=Ht(e.children[r],t);if(0===s)return n.set(e,0),0;s<o&&(o=s)}if(o>1)for(let r=0;r<e.props.length;r++){const s=e.props[r];if(7===s.type&&"bind"===s.name&&s.exp){const r=Ht(s.exp,t);if(0===r)return n.set(e,0),0;r<o&&(o=r)}}if(r.isBlock){for(let t=0;t<e.props.length;t++){if(7===e.props[t].type)return n.set(e,0),0}t.removeHelper(L),t.removeHelper(Re(t.inSSR,r.isComponent)),r.isBlock=!1,t.helper(Me(t.inSSR,r.isComponent))}return n.set(e,o),o}case 2:case 3:return 3;case 9:case 11:case 10:default:return 0;case 5:case 12:return Ht(e.content,t);case 4:return e.constType;case 8:let s=3;for(let n=0;n<e.children.length;n++){const o=e.children[n];if(a(o)||p(o))continue;const r=Ht(o,t);if(0===r)return 0;r<s&&(s=r)}return s}}const Wt=new Set([Q,ee,te,ne]);function Kt(e,t){if(14===e.type&&!a(e.callee)&&Wt.has(e.callee)){const n=e.arguments[0];if(4===n.type)return Ht(n,t);if(14===n.type)return Kt(n,t)}return 0}function Ut(e,t){let n=3;const o=Jt(e);if(o&&15===o.type){const{properties:e}=o;for(let o=0;o<e.length;o++){const{key:r,value:s}=e[o],i=Ht(r,t);if(0===i)return i;let c;if(i<n&&(n=i),c=4===s.type?Ht(s,t):14===s.type?Kt(s,t):0,0===c)return c;c<n&&(n=c)}}return n}function Jt(e){const t=e.codegenNode;if(13===t.type)return t.props}function Gt(e){const t=e.patchFlag;return t?parseInt(t,10):void 0}function zt(e,{filename:t="",prefixIdentifiers:r=!1,hoistStatic:s=!1,cacheHandlers:i=!1,nodeTransforms:c=[],directiveTransforms:l={},transformHoist:p=null,isBuiltInComponent:u=o,isCustomElement:f=o,expressionPlugins:d=[],scopeId:h=null,slotted:m=!0,ssr:y=!1,inSSR:v=!1,ssrCssVars:E="",bindingMetadata:b=n,inline:T=!1,isTS:x=!1,onError:N=O,onWarn:_=C,compatConfig:k}){const I=t.replace(/\?.*$/,"").match(/([^/\\]+)\.\w+$/),M={selfName:I&&S(g(I[1])),prefixIdentifiers:r,hoistStatic:s,cacheHandlers:i,nodeTransforms:c,directiveTransforms:l,transformHoist:p,isBuiltInComponent:u,isCustomElement:f,expressionPlugins:d,scopeId:h,slotted:m,ssr:y,inSSR:v,ssrCssVars:E,bindingMetadata:b,inline:T,isTS:x,onError:N,onWarn:_,compatConfig:k,root:e,helpers:new Map,components:new Set,directives:new Set,hoists:[],imports:[],constantCache:new Map,temps:0,cached:0,identifiers:Object.create(null),scopes:{vFor:0,vSlot:0,vPre:0,vOnce:0},parent:null,currentNode:e,childIndex:0,inVOnce:!1,helper(e){const t=M.helpers.get(e)||0;return M.helpers.set(e,t+1),e},removeHelper(e){const t=M.helpers.get(e);if(t){const n=t-1;n?M.helpers.set(e,n):M.helpers.delete(e)}},helperString:e=>`_${me[M.helper(e)]}`,replaceNode(e){M.parent.children[M.childIndex]=M.currentNode=e},removeNode(e){const t=e?M.parent.children.indexOf(e):M.currentNode?M.childIndex:-1;e&&e!==M.currentNode?M.childIndex>t&&(M.childIndex--,M.onNodeRemoved()):(M.currentNode=null,M.onNodeRemoved()),M.parent.children.splice(t,1)},onNodeRemoved:()=>{},addIdentifiers(e){},removeIdentifiers(e){},hoist(e){a(e)&&(e=xe(e)),M.hoists.push(e);const t=xe(`_hoisted_${M.hoists.length}`,!1,e.loc,2);return t.hoisted=e,t},cache:(e,t=!1)=>Ce(M.cached++,e,t)};return M.filters=new Set,M}function Yt(e,t){const n=zt(e,t);Zt(e,n),t.hoistStatic&&Bt(e,n),t.ssr||function(e,t){const{helper:n}=t,{children:o}=e;if(1===o.length){const n=o[0];if(Ft(e,n)&&n.codegenNode){const o=n.codegenNode;13===o.type&&Pe(o,t),e.codegenNode=o}else e.codegenNode=n}else if(o.length>1){let o=64;e.codegenNode=Se(t,n(M),void 0,e.children,o+"",void 0,void 0,!0,void 0,!1)}}(e,n),e.helpers=new Set([...n.helpers.keys()]),e.components=[...n.components],e.directives=[...n.directives],e.imports=n.imports,e.hoists=n.hoists,e.temps=n.temps,e.cached=n.cached,e.filters=[...n.filters]}function Zt(e,t){t.currentNode=e;const{nodeTransforms:n}=t,o=[];for(let s=0;s<n.length;s++){const r=n[s](e,t);if(r&&(l(r)?o.push(...r):o.push(r)),!t.currentNode)return;e=t.currentNode}switch(e.type){case 3:t.ssr||t.helper(F);break;case 5:t.ssr||t.helper(q);break;case 9:for(let n=0;n<e.branches.length;n++)Zt(e.branches[n],t);break;case 10:case 11:case 1:case 0:!function(e,t){let n=0;const o=()=>{n--};for(;n<e.children.length;n++){const r=e.children[n];a(r)||(t.parent=e,t.childIndex=n,t.onNodeRemoved=o,Zt(r,t))}}(e,t)}t.currentNode=e;let r=o.length;for(;r--;)o[r]()}function qt(e,t){const n=a(e)?t=>t===e:t=>e.test(t);return(e,o)=>{if(1===e.type){const{props:r}=e;if(3===e.tagType&&r.some(Xe))return;const s=[];for(let i=0;i<r.length;i++){const c=r[i];if(7===c.type&&n(c.name)){r.splice(i,1),i--;const n=t(e,c,o);n&&s.push(n)}}return s}}}const Xt="/*#__PURE__*/",Qt=e=>`${me[e]}: _${me[e]}`;function en(e,{mode:t="function",prefixIdentifiers:n="module"===t,sourceMap:o=!1,filename:r="template.vue.html",scopeId:s=null,optimizeImports:i=!1,runtimeGlobalName:c="Vue",runtimeModuleName:l="vue",ssrRuntimeModuleName:a="vue/server-renderer",ssr:p=!1,isTS:u=!1,inSSR:f=!1}){const d={mode:t,prefixIdentifiers:n,sourceMap:o,filename:r,scopeId:s,optimizeImports:i,runtimeGlobalName:c,runtimeModuleName:l,ssrRuntimeModuleName:a,ssr:p,isTS:u,inSSR:f,source:e.loc.source,code:"",column:1,line:1,offset:0,indentLevel:0,pure:!1,map:void 0,helper:e=>`_${me[e]}`,push(e,t){d.code+=e},indent(){h(++d.indentLevel)},deindent(e=!1){e?--d.indentLevel:h(--d.indentLevel)},newline(){h(d.indentLevel)}};function h(e){d.push("\n"+"  ".repeat(e))}return d}function tn(e,t={}){const n=en(e,t);t.onContextCreated&&t.onContextCreated(n);const{mode:o,push:r,prefixIdentifiers:s,indent:i,deindent:c,newline:l,ssr:a}=n,p=Array.from(e.helpers),u=p.length>0,f=!s&&"module"!==o,d=n;!function(e,t){const{push:n,newline:o,runtimeGlobalName:r}=t,s=r,i=Array.from(e.helpers);if(i.length>0&&(n(`const _Vue = ${s}\n`),e.hoists.length)){n(`const { ${[D,B,F,j,H].filter((e=>i.includes(e))).map(Qt).join(", ")} } = _Vue\n`)}(function(e,t){if(!e.length)return;t.pure=!0;const{push:n,newline:o}=t;o();for(let r=0;r<e.length;r++){const s=e[r];s&&(n(`const _hoisted_${r+1} = `),sn(s,t),o())}t.pure=!1})(e.hoists,t),o(),n("return ")}(e,d);if(r(`function ${a?"ssrRender":"render"}(${(a?["_ctx","_push","_parent","_attrs"]:["_ctx","_cache"]).join(", ")}) {`),i(),f&&(r("with (_ctx) {"),i(),u&&(r(`const { ${p.map(Qt).join(", ")} } = _Vue`),r("\n"),l())),e.components.length&&(nn(e.components,"component",n),(e.directives.length||e.temps>0)&&l()),e.directives.length&&(nn(e.directives,"directive",n),e.temps>0&&l()),e.filters&&e.filters.length&&(l(),nn(e.filters,"filter",n),l()),e.temps>0){r("let ");for(let t=0;t<e.temps;t++)r(`${t>0?", ":""}_temp${t}`)}return(e.components.length||e.directives.length||e.temps)&&(r("\n"),l()),a||r("return "),e.codegenNode?sn(e.codegenNode,n):r("null"),f&&(c(),r("}")),c(),r("}"),{ast:e,code:n.code,preamble:"",map:n.map?n.map.toJSON():void 0}}function nn(e,t,{helper:n,push:o,newline:r,isTS:s}){const i=n("filter"===t?J:"component"===t?W:U);for(let c=0;c<e.length;c++){let n=e[c];const l=n.endsWith("__self");l&&(n=n.slice(0,-6)),o(`const ${st(n,t)} = ${i}(${JSON.stringify(n)}${l?", true":""})${s?"!":""}`),c<e.length-1&&r()}}function on(e,t){const n=e.length>3||!1;t.push("["),n&&t.indent(),rn(e,t,n),n&&t.deindent(),t.push("]")}function rn(e,t,n=!1,o=!0){const{push:r,newline:s}=t;for(let i=0;i<e.length;i++){const c=e[i];a(c)?r(c):l(c)?on(c,t):sn(c,t),i<e.length-1&&(n?(o&&r(","),s()):o&&r(", "))}}function sn(e,t){if(a(e))t.push(e);else if(p(e))t.push(t.helper(e));else switch(e.type){case 1:case 9:case 11:case 12:sn(e.codegenNode,t);break;case 2:!function(e,t){t.push(JSON.stringify(e.content),e)}(e,t);break;case 4:cn(e,t);break;case 5:!function(e,t){const{push:n,helper:o,pure:r}=t;r&&n(Xt);n(`${o(q)}(`),sn(e.content,t),n(")")}(e,t);break;case 8:ln(e,t);break;case 3:!function(e,t){const{push:n,helper:o,pure:r}=t;r&&n(Xt);n(`${o(F)}(${JSON.stringify(e.content)})`,e)}(e,t);break;case 13:!function(e,t){const{push:n,helper:o,pure:r}=t,{tag:s,props:i,children:c,patchFlag:l,dynamicProps:a,directives:p,isBlock:u,disableTracking:f,isComponent:d}=e;p&&n(o(G)+"(");u&&n(`(${o(L)}(${f?"true":""}), `);r&&n(Xt);const h=u?Re(t.inSSR,d):Me(t.inSSR,d);n(o(h)+"(",e),rn(function(e){let t=e.length;for(;t--&&null==e[t];);return e.slice(0,t+1).map((e=>e||"null"))}([s,i,c,l,a]),t),n(")"),u&&n(")");p&&(n(", "),sn(p,t),n(")"))}(e,t);break;case 14:!function(e,t){const{push:n,helper:o,pure:r}=t,s=a(e.callee)?e.callee:o(e.callee);r&&n(Xt);n(s+"(",e),rn(e.arguments,t),n(")")}(e,t);break;case 15:!function(e,t){const{push:n,indent:o,deindent:r,newline:s}=t,{properties:i}=e;if(!i.length)return void n("{}",e);const c=i.length>1||!1;n(c?"{":"{ "),c&&o();for(let l=0;l<i.length;l++){const{key:e,value:o}=i[l];an(e,t),n(": "),sn(o,t),l<i.length-1&&(n(","),s())}c&&r(),n(c?"}":" }")}(e,t);break;case 17:!function(e,t){on(e.elements,t)}(e,t);break;case 18:!function(e,t){const{push:n,indent:o,deindent:r}=t,{params:s,returns:i,body:c,newline:a,isSlot:p}=e;p&&n(`_${me[pe]}(`);n("(",e),l(s)?rn(s,t):s&&sn(s,t);n(") => "),(a||c)&&(n("{"),o());i?(a&&n("return "),l(i)?on(i,t):sn(i,t)):c&&sn(c,t);(a||c)&&(r(),n("}"));p&&(e.isNonScopedSlot&&n(", undefined, true"),n(")"))}(e,t);break;case 19:!function(e,t){const{test:n,consequent:o,alternate:r,newline:s}=e,{push:i,indent:c,deindent:l,newline:a}=t;if(4===n.type){const e=!Ae(n.content);e&&i("("),cn(n,t),e&&i(")")}else i("("),sn(n,t),i(")");s&&c(),t.indentLevel++,s||i(" "),i("? "),sn(o,t),t.indentLevel--,s&&a(),s||i(" "),i(": ");const p=19===r.type;p||t.indentLevel++;sn(r,t),p||t.indentLevel--;s&&l(!0)}(e,t);break;case 20:!function(e,t){const{push:n,helper:o,indent:r,deindent:s,newline:i}=t;n(`_cache[${e.index}] || (`),e.isVNode&&(r(),n(`${o(ce)}(-1),`),i());n(`_cache[${e.index}] = `),sn(e.value,t),e.isVNode&&(n(","),i(),n(`${o(ce)}(1),`),i(),n(`_cache[${e.index}]`),s());n(")")}(e,t);break;case 21:rn(e.body,t,!0,!1)}}function cn(e,t){const{content:n,isStatic:o}=e;t.push(o?JSON.stringify(n):n,e)}function ln(e,t){for(let n=0;n<e.children.length;n++){const o=e.children[n];a(o)?t.push(o):sn(o,t)}}function an(e,t){const{push:n}=t;if(8===e.type)n("["),ln(e,t),n("]");else if(e.isStatic){n(Ae(e.content)?e.content:JSON.stringify(e.content),e)}else n(`[${e.content}]`,e)}function pn(e,t=[]){switch(e.type){case"Identifier":t.push(e);break;case"MemberExpression":let n=e;for(;"MemberExpression"===n.type;)n=n.object;t.push(n);break;case"ObjectPattern":for(const o of e.properties)pn("RestElement"===o.type?o.argument:o.value,t);break;case"ArrayPattern":e.elements.forEach((e=>{e&&pn(e,t)}));break;case"RestElement":pn(e.argument,t);break;case"AssignmentPattern":pn(e.left,t)}return t}const un=e=>e&&("ObjectProperty"===e.type||"ObjectMethod"===e.type)&&!e.computed;function fn(e,t,n=!1,o=!1,r=Object.create(t.identifiers)){return e}const dn=qt(/^(if|else|else-if)$/,((e,t,n)=>hn(e,t,n,((e,t,o)=>{const r=n.parent.children;let s=r.indexOf(e),i=0;for(;s-- >=0;){const e=r[s];e&&9===e.type&&(i+=e.branches.length)}return()=>{if(o)e.codegenNode=gn(t,i,n);else{const o=function(e){for(;;)if(19===e.type){if(19!==e.alternate.type)return e;e=e.alternate}else 20===e.type&&(e=e.value)}(e.codegenNode);o.alternate=gn(t,i+e.branches.length-1,n)}}}))));function hn(e,t,n,o){if(!("else"===t.name||t.exp&&t.exp.content.trim())){const o=t.exp?t.exp.loc:e.loc;n.onError(I(28,t.loc)),t.exp=xe("true",!1,o)}if("if"===t.name){const r=mn(e,t),s={type:9,loc:e.loc,branches:[r]};if(n.replaceNode(s),o)return o(s,r,!0)}else{const r=n.parent.children;let s=r.indexOf(e);for(;s-- >=-1;){const i=r[s];if(i&&3===i.type)n.removeNode(i);else{if(!i||2!==i.type||i.content.trim().length){if(i&&9===i.type){"else-if"===t.name&&void 0===i.branches[i.branches.length-1].condition&&n.onError(I(30,e.loc)),n.removeNode();const r=mn(e,t);i.branches.push(r);const s=o&&o(i,r,!1);Zt(r,n),s&&s(),n.currentNode=null}else n.onError(I(30,e.loc));break}n.removeNode(i)}}}}function mn(e,t){const n=3===e.tagType;return{type:10,loc:e.loc,condition:"else"===t.name?void 0:t.exp,children:n&&!Ge(e,"for")?e.children:[e],userKey:ze(e,"key"),isTemplateIf:n}}function gn(e,t,n){return e.condition?Oe(e.condition,yn(e,t,n),_e(n.helper(F),['""',"true"])):yn(e,t,n)}function yn(e,t,n){const{helper:o}=n,r=Te("key",xe(`${t}`,!1,ye,2)),{children:s}=e,i=s[0];if(1!==s.length||1!==i.type){if(1===s.length&&11===i.type){const e=i.codegenNode;return ot(e,r,n),e}{let t=64;return Se(n,o(M),be([r]),s,t+"",void 0,void 0,!0,!1,!1,e.loc)}}{const e=i.codegenNode,t=it(e);return 13===t.type&&Pe(t,n),ot(t,r,n),e}}const vn=qt("for",((e,t,n)=>{const{helper:o,removeHelper:r}=n;return Sn(e,t,n,(t=>{const s=_e(o(z),[t.source]),i=Qe(e),c=Ge(e,"memo"),l=ze(e,"key"),a=l&&(6===l.type?xe(l.value.content,!0):l.exp),p=l?Te("key",a):null,u=4===t.source.type&&t.source.constType>0,f=u?64:l?128:256;return t.codegenNode=Se(n,o(M),void 0,s,f+"",void 0,void 0,!0,!u,!1,e.loc),()=>{let l;const{children:f}=t,d=1!==f.length||1!==f[0].type,h=et(e)?e:i&&1===e.children.length&&et(e.children[0])?e.children[0]:null;if(h?(l=h.codegenNode,i&&p&&ot(l,p,n)):d?l=Se(n,o(M),p?be([p]):void 0,e.children,"64",void 0,void 0,!0,void 0,!1):(l=f[0].codegenNode,i&&p&&ot(l,p,n),l.isBlock!==!u&&(l.isBlock?(r(L),r(Re(n.inSSR,l.isComponent))):r(Me(n.inSSR,l.isComponent))),l.isBlock=!u,l.isBlock?(o(L),o(Re(n.inSSR,l.isComponent))):o(Me(n.inSSR,l.isComponent))),c){const e=ke(_n(t.parseResult,[xe("_cached")]));e.body=Ie([Ne(["const _memo = (",c.exp,")"]),Ne(["if (_cached",...a?[" && _cached.key === ",a]:[],` && ${n.helperString(he)}(_cached, _memo)) return _cached`]),Ne(["const _item = ",l]),xe("_item.memo = _memo"),xe("return _item")]),s.arguments.push(e,xe("_cache"),xe(String(n.cached++)))}else s.arguments.push(ke(_n(t.parseResult),l,!0))}}))}));function Sn(e,t,n,o){if(!t.exp)return void n.onError(I(31,t.loc));const r=xn(t.exp);if(!r)return void n.onError(I(32,t.loc));const{scopes:s}=n,{source:i,value:c,key:l,index:a}=r,p={type:11,loc:t.loc,source:i,valueAlias:c,keyAlias:l,objectIndexAlias:a,parseResult:r,children:Qe(e)?e.children:[e]};n.replaceNode(p),s.vFor++;const u=o&&o(p);return()=>{s.vFor--,u&&u()}}const En=/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/,bn=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,Tn=/^\(|\)$/g;function xn(e,t){const n=e.loc,o=e.content,r=o.match(En);if(!r)return;const[,s,i]=r,c={source:Nn(n,i.trim(),o.indexOf(i,s.length)),value:void 0,key:void 0,index:void 0};let l=s.trim().replace(Tn,"").trim();const a=s.indexOf(l),p=l.match(bn);if(p){l=l.replace(bn,"").trim();const e=p[1].trim();let t;if(e&&(t=o.indexOf(e,a+l.length),c.key=Nn(n,e,t)),p[2]){const r=p[2].trim();r&&(c.index=Nn(n,r,o.indexOf(r,c.key?t+e.length:a+l.length)))}}return l&&(c.value=Nn(n,l,a)),c}function Nn(e,t,n){return xe(t,!1,Ke(e,n,t.length))}function _n({value:e,key:t,index:n},o=[]){return function(e){let t=e.length;for(;t--&&!e[t];);return e.slice(0,t+1).map(((e,t)=>e||xe("_".repeat(t+1),!1)))}([e,t,n,...o])}const kn=xe("undefined",!1),On=(e,t)=>{if(1===e.type&&(1===e.tagType||3===e.tagType)){const n=Ge(e,"slot");if(n)return t.scopes.vSlot++,()=>{t.scopes.vSlot--}}},Cn=(e,t,n)=>ke(e,t,!1,!0,t.length?t[0].loc:n);function In(e,t,n=Cn){t.helper(pe);const{children:o,loc:r}=e,s=[],i=[];let c=t.scopes.vSlot>0||t.scopes.vFor>0;const l=Ge(e,"slot",!0);if(l){const{arg:e,exp:t}=l;e&&!we(e)&&(c=!0),s.push(Te(e||xe("default",!0),n(t,o,r)))}let a=!1,p=!1;const u=[],f=new Set;let d=0;for(let g=0;g<o.length;g++){const e=o[g];let r;if(!Qe(e)||!(r=Ge(e,"slot",!0))){3!==e.type&&u.push(e);continue}if(l){t.onError(I(37,r.loc));break}a=!0;const{children:h,loc:m}=e,{arg:y=xe("default",!0),exp:v,loc:S}=r;let E;we(y)?E=y?y.content:"default":c=!0;const b=n(v,h,m);let T,x,N;if(T=Ge(e,"if"))c=!0,i.push(Oe(T.exp,Mn(y,b,d++),kn));else if(x=Ge(e,/^else(-if)?$/,!0)){let e,n=g;for(;n--&&(e=o[n],3===e.type););if(e&&Qe(e)&&Ge(e,"if")){o.splice(g,1),g--;let e=i[i.length-1];for(;19===e.alternate.type;)e=e.alternate;e.alternate=x.exp?Oe(x.exp,Mn(y,b,d++),kn):Mn(y,b,d++)}else t.onError(I(30,x.loc))}else if(N=Ge(e,"for")){c=!0;const e=N.parseResult||xn(N.exp);e?i.push(_e(t.helper(z),[e.source,ke(_n(e),Mn(y,b),!0)])):t.onError(I(32,N.loc))}else{if(E){if(f.has(E)){t.onError(I(38,S));continue}f.add(E),"default"===E&&(p=!0)}s.push(Te(y,b))}}if(!l){const e=(e,o)=>{const s=n(e,o,r);return t.compatConfig&&(s.isNonScopedSlot=!0),Te("default",s)};a?u.length&&u.some((e=>Pn(e)))&&(p?t.onError(I(39,u[0].loc)):s.push(e(void 0,u))):s.push(e(void 0,o))}const h=c?2:Rn(e.children)?3:1;let m=be(s.concat(Te("_",xe(h+"",!1))),r);return i.length&&(m=_e(t.helper(Z),[m,Ee(i)])),{slots:m,hasDynamicSlots:c}}function Mn(e,t,n){const o=[Te("name",e),Te("fn",t)];return null!=n&&o.push(Te("key",xe(String(n),!0))),be(o)}function Rn(e){for(let t=0;t<e.length;t++){const n=e[t];switch(n.type){case 1:if(2===n.tagType||Rn(n.children))return!0;break;case 9:if(Rn(n.branches))return!0;break;case 10:case 11:if(Rn(n.children))return!0}}return!1}function Pn(e){return 2!==e.type&&12!==e.type||(2===e.type?!!e.content.trim():Pn(e.content))}const wn=new WeakMap,$n=(e,t)=>function(){if(1!==(e=t.currentNode).type||0!==e.tagType&&1!==e.tagType)return;const{tag:n,props:o}=e,r=1===e.tagType;let s=r?Ln(e,t):`"${n}"`;const i=u(s)&&s.callee===K;let c,l,a,p,f,d,h=0,m=i||s===R||s===P||!r&&("svg"===n||"foreignObject"===n);if(o.length>0){const n=Vn(e,t,void 0,r,i);c=n.props,h=n.patchFlag,f=n.dynamicPropNames;const o=n.directives;d=o&&o.length?Ee(o.map((e=>Bn(e,t)))):void 0,n.shouldUseBlock&&(m=!0)}if(e.children.length>0){s===w&&(m=!0,h|=1024);if(r&&s!==R&&s!==w){const{slots:n,hasDynamicSlots:o}=In(e,t);l=n,o&&(h|=1024)}else if(1===e.children.length&&s!==R){const n=e.children[0],o=n.type,r=5===o||8===o;r&&0===Ht(n,t)&&(h|=1),l=r||2===o?n:e.children}else l=e.children}0!==h&&(a=String(h),f&&f.length&&(p=function(e){let t="[";for(let n=0,o=e.length;n<o;n++)t+=JSON.stringify(e[n]),n<o-1&&(t+=", ");return t+"]"}(f))),e.codegenNode=Se(t,s,c,l,a,p,d,!!m,!1,r,e.loc)};function Ln(e,t,n=!1){let{tag:o}=e;const r=Fn(o),s=ze(e,"is");if(s)if(r||at("COMPILER_IS_ON_ELEMENT",t)){const e=6===s.type?s.value&&xe(s.value.content,!0):s.exp;if(e)return _e(t.helper(K),[e])}else 6===s.type&&s.value.content.startsWith("vue:")&&(o=s.value.content.slice(4));const i=!r&&Ge(e,"is");if(i&&i.exp)return _e(t.helper(K),[i.exp]);const c=Le(o)||t.isBuiltInComponent(o);return c?(n||t.helper(c),c):(t.helper(W),t.components.add(o),st(o,"component"))}function Vn(e,t,n=e.props,o,r,s=!1){const{tag:c,loc:l,children:a}=e;let u=[];const h=[],m=[],g=a.length>0;let y=!1,v=0,S=!1,E=!1,b=!1,T=!1,x=!1,N=!1;const _=[],k=e=>{u.length&&(h.push(be(An(u),l)),u=[]),e&&h.push(e)},O=({key:e,value:n})=>{if(we(e)){const s=e.content,c=i(s);if(!c||o&&!r||"onclick"===s.toLowerCase()||"onUpdate:modelValue"===s||f(s)||(T=!0),c&&f(s)&&(N=!0),20===n.type||(4===n.type||8===n.type)&&Ht(n,t)>0)return;"ref"===s?S=!0:"class"===s?E=!0:"style"===s?b=!0:"key"===s||_.includes(s)||_.push(s),!o||"class"!==s&&"style"!==s||_.includes(s)||_.push(s)}else x=!0};for(let i=0;i<n.length;i++){const r=n[i];if(6===r.type){const{loc:e,name:n,value:o}=r;let s=!0;if("ref"===n&&(S=!0,t.scopes.vFor>0&&u.push(Te(xe("ref_for",!0),xe("true")))),"is"===n&&(Fn(c)||o&&o.content.startsWith("vue:")||at("COMPILER_IS_ON_ELEMENT",t)))continue;u.push(Te(xe(n,!0,Ke(e,0,n.length)),xe(o?o.content:"",s,o?o.loc:e)))}else{const{name:n,arg:i,exp:a,loc:f}=r,v="bind"===n,S="on"===n;if("slot"===n){o||t.onError(I(40,f));continue}if("once"===n||"memo"===n)continue;if("is"===n||v&&Ye(i,"is")&&(Fn(c)||at("COMPILER_IS_ON_ELEMENT",t)))continue;if(S&&s)continue;if((v&&Ye(i,"key")||S&&g&&Ye(i,"vue:before-update"))&&(y=!0),v&&Ye(i,"ref")&&t.scopes.vFor>0&&u.push(Te(xe("ref_for",!0),xe("true"))),!i&&(v||S)){if(x=!0,a)if(v){if(k(),at("COMPILER_V_BIND_OBJECT_ORDER",t)){h.unshift(a);continue}h.push(a)}else k({type:14,loc:f,callee:t.helper(oe),arguments:o?[a]:[a,"true"]});else t.onError(I(v?34:35,f));continue}const E=t.directiveTransforms[n];if(E){const{props:n,needRuntime:o}=E(r,e,t);!s&&n.forEach(O),S&&i&&!we(i)?k(be(n,l)):u.push(...n),o&&(m.push(r),p(o)&&wn.set(r,o))}else d(n)||(m.push(r),g&&(y=!0))}}let C;if(h.length?(k(),C=h.length>1?_e(t.helper(X),h,l):h[0]):u.length&&(C=be(An(u),l)),x?v|=16:(E&&!o&&(v|=2),b&&!o&&(v|=4),_.length&&(v|=8),T&&(v|=32)),y||0!==v&&32!==v||!(S||N||m.length>0)||(v|=512),!t.inSSR&&C)switch(C.type){case 15:let e=-1,n=-1,o=!1;for(let t=0;t<C.properties.length;t++){const r=C.properties[t].key;we(r)?"class"===r.content?e=t:"style"===r.content&&(n=t):r.isHandlerKey||(o=!0)}const r=C.properties[e],s=C.properties[n];o?C=_e(t.helper(te),[C]):(r&&!we(r.value)&&(r.value=_e(t.helper(Q),[r.value])),s&&(b||4===s.value.type&&"["===s.value.content.trim()[0]||17===s.value.type)&&(s.value=_e(t.helper(ee),[s.value])));break;case 14:break;default:C=_e(t.helper(te),[_e(t.helper(ne),[C])])}return{props:C,directives:m,patchFlag:v,dynamicPropNames:_,shouldUseBlock:y}}function An(e){const t=new Map,n=[];for(let o=0;o<e.length;o++){const r=e[o];if(8===r.key.type||!r.key.isStatic){n.push(r);continue}const s=r.key.content,c=t.get(s);c?("style"===s||"class"===s||i(s))&&Dn(c,r):(t.set(s,r),n.push(r))}return n}function Dn(e,t){17===e.value.type?e.value.elements.push(t.value):e.value=Ee([e.value,t.value],e.loc)}function Bn(e,t){const n=[],o=wn.get(e);o?n.push(t.helperString(o)):(t.helper(U),t.directives.add(e.name),n.push(st(e.name,"directive")));const{loc:r}=e;if(e.exp&&n.push(e.exp),e.arg&&(e.exp||n.push("void 0"),n.push(e.arg)),Object.keys(e.modifiers).length){e.arg||(e.exp||n.push("void 0"),n.push("void 0"));const t=xe("true",!1,r);n.push(be(e.modifiers.map((e=>Te(e,t))),r))}return Ee(n,e.loc)}function Fn(e){return"component"===e||"Component"===e}const jn=(e,t)=>{if(et(e)){const{children:n,loc:o}=e,{slotName:r,slotProps:s}=Hn(e,t),i=[t.prefixIdentifiers?"_ctx.$slots":"$slots",r,"{}","undefined","true"];let c=2;s&&(i[2]=s,c=3),n.length&&(i[3]=ke([],n,!1,!1,o),c=4),t.scopeId&&!t.slotted&&(c=5),i.splice(c),e.codegenNode=_e(t.helper(Y),i,o)}};function Hn(e,t){let n,o='"default"';const r=[];for(let s=0;s<e.props.length;s++){const t=e.props[s];6===t.type?t.value&&("name"===t.name?o=JSON.stringify(t.value.content):(t.name=g(t.name),r.push(t))):"bind"===t.name&&Ye(t.arg,"name")?t.exp&&(o=t.exp):("bind"===t.name&&t.arg&&we(t.arg)&&(t.arg.content=g(t.arg.content)),r.push(t))}if(r.length>0){const{props:o,directives:s}=Vn(e,t,r,!1,!1);n=o,s.length&&t.onError(I(36,s[0].loc))}return{slotName:o,slotProps:n}}const Wn=/^\s*([\w$_]+|(async\s*)?\([^)]*?\))\s*(:[^=]+)?=>|^\s*(async\s+)?function(?:\s+[\w$]+)?\s*\(/,Kn=(e,t,n,o)=>{const{loc:r,modifiers:s,arg:i}=e;let c;if(4===i.type)if(i.isStatic){let e=i.content;e.startsWith("vue:")&&(e=`vnode-${e.slice(4)}`);c=xe(0!==t.tagType||e.startsWith("vnode")||!/[A-Z]/.test(e)?E(g(e)):`on:${e}`,!0,i.loc)}else c=Ne([`${n.helperString(ie)}(`,i,")"]);else c=i,c.children.unshift(`${n.helperString(ie)}(`),c.children.push(")");let l=e.exp;l&&!l.content.trim()&&(l=void 0);let a=n.cacheHandlers&&!l&&!n.inVOnce;if(l){const e=We(l.content),t=!(e||Wn.test(l.content)),n=l.content.includes(";");(t||a&&e)&&(l=Ne([`${t?"$event":"(...args)"} => ${n?"{":"("}`,l,n?"}":")"]))}let p={props:[Te(c,l||xe("() => {}",!1,r))]};return o&&(p=o(p)),a&&(p.props[0].value=n.cache(p.props[0].value)),p.props.forEach((e=>e.key.isHandlerKey=!0)),p},Un=(e,t,n)=>{const{exp:o,modifiers:r,loc:s}=e,i=e.arg;return 4!==i.type?(i.children.unshift("("),i.children.push(') || ""')):i.isStatic||(i.content=`${i.content} || ""`),r.includes("camel")&&(4===i.type?i.content=i.isStatic?g(i.content):`${n.helperString(re)}(${i.content})`:(i.children.unshift(`${n.helperString(re)}(`),i.children.push(")"))),n.inSSR||(r.includes("prop")&&Jn(i,"."),r.includes("attr")&&Jn(i,"^")),!o||4===o.type&&!o.content.trim()?{props:[Te(i,xe("",!0,s))]}:{props:[Te(i,o)]}},Jn=(e,t)=>{4===e.type?e.content=e.isStatic?t+e.content:`\`${t}\${${e.content}}\``:(e.children.unshift(`'${t}' + (`),e.children.push(")"))},Gn=(e,t)=>{if(0===e.type||1===e.type||11===e.type||10===e.type)return()=>{const n=e.children;let o,r=!1;for(let e=0;e<n.length;e++){const t=n[e];if(qe(t)){r=!0;for(let r=e+1;r<n.length;r++){const s=n[r];if(!qe(s)){o=void 0;break}o||(o=n[e]=Ne([t],t.loc)),o.children.push(" + ",s),n.splice(r,1),r--}}}if(r&&(1!==n.length||0!==e.type&&(1!==e.type||0!==e.tagType||e.props.find((e=>7===e.type&&!t.directiveTransforms[e.name]))||"template"===e.tag)))for(let e=0;e<n.length;e++){const o=n[e];if(qe(o)||8===o.type){const r=[];2===o.type&&" "===o.content||r.push(o),t.ssr||0!==Ht(o,t)||r.push("1"),n[e]={type:12,content:o,loc:o.loc,codegenNode:_e(t.helper(j),r)}}}}},zn=new WeakSet,Yn=(e,t)=>{if(1===e.type&&Ge(e,"once",!0)){if(zn.has(e)||t.inVOnce||t.inSSR)return;return zn.add(e),t.inVOnce=!0,t.helper(ce),()=>{t.inVOnce=!1;const e=t.currentNode;e.codegenNode&&(e.codegenNode=t.cache(e.codegenNode,!0))}}},Zn=(e,t,n)=>{const{exp:o,arg:r}=e;if(!o)return n.onError(I(41,e.loc)),qn();const s=o.loc.source,i=4===o.type?o.content:s,c=n.bindingMetadata[s];if("props"===c||"props-aliased"===c)return qn();if(!i.trim()||!We(i))return n.onError(I(42,o.loc)),qn();const l=r||xe("modelValue",!0),a=r?we(r)?`onUpdate:${g(r.content)}`:Ne(['"onUpdate:" + ',r]):"onUpdate:modelValue";let p;p=Ne([`${n.isTS?"($event: any)":"$event"} => ((`,o,") = $event)"]);const u=[Te(l,e.exp),Te(a,p)];if(e.modifiers.length&&1===t.tagType){const t=e.modifiers.map((e=>(Ae(e)?e:JSON.stringify(e))+": true")).join(", "),n=r?we(r)?`${r.content}Modifiers`:Ne([r,' + "Modifiers"']):"modelModifiers";u.push(Te(n,xe(`{ ${t} }`,!1,e.loc,2)))}return qn(u)};function qn(e=[]){return{props:e}}const Xn=/[\w).+\-_$\]]/,Qn=(e,t)=>{at("COMPILER_FILTER",t)&&(5===e.type&&eo(e.content,t),1===e.type&&e.props.forEach((e=>{7===e.type&&"for"!==e.name&&e.exp&&eo(e.exp,t)})))};function eo(e,t){if(4===e.type)to(e,t);else for(let n=0;n<e.children.length;n++){const o=e.children[n];"object"==typeof o&&(4===o.type?to(o,t):8===o.type?eo(e,t):5===o.type&&eo(o.content,t))}}function to(e,t){const n=e.content;let o,r,s,i,c=!1,l=!1,a=!1,p=!1,u=0,f=0,d=0,h=0,m=[];for(s=0;s<n.length;s++)if(r=o,o=n.charCodeAt(s),c)39===o&&92!==r&&(c=!1);else if(l)34===o&&92!==r&&(l=!1);else if(a)96===o&&92!==r&&(a=!1);else if(p)47===o&&92!==r&&(p=!1);else if(124!==o||124===n.charCodeAt(s+1)||124===n.charCodeAt(s-1)||u||f||d){switch(o){case 34:l=!0;break;case 39:c=!0;break;case 96:a=!0;break;case 40:d++;break;case 41:d--;break;case 91:f++;break;case 93:f--;break;case 123:u++;break;case 125:u--}if(47===o){let e,t=s-1;for(;t>=0&&(e=n.charAt(t)," "===e);t--);e&&Xn.test(e)||(p=!0)}}else void 0===i?(h=s+1,i=n.slice(0,s).trim()):g();function g(){m.push(n.slice(h,s).trim()),h=s+1}if(void 0===i?i=n.slice(0,s).trim():0!==h&&g(),m.length){for(s=0;s<m.length;s++)i=no(i,m[s],t);e.content=i}}function no(e,t,n){n.helper(J);const o=t.indexOf("(");if(o<0)return n.filters.add(t),`${st(t,"filter")}(${e})`;{const r=t.slice(0,o),s=t.slice(o+1);return n.filters.add(r),`${st(r,"filter")}(${e}${")"!==s?","+s:s}`}}const oo=new WeakSet,ro=(e,t)=>{if(1===e.type){const n=Ge(e,"memo");if(!n||oo.has(e))return;return oo.add(e),()=>{const o=e.codegenNode||t.currentNode.codegenNode;o&&13===o.type&&(1!==e.tagType&&Pe(o,t),e.codegenNode=_e(t.helper(de),[n.exp,ke(void 0,o),"_cache",String(t.cached++)]))}}};function so(e){return[[Yn,dn,ro,vn,Qn,jn,$n,On,Gn],{on:Kn,bind:Un,model:Zn}]}function io(e,t={}){const n=t.onError||O,o="module"===t.mode;!0===t.prefixIdentifiers?n(I(47)):o&&n(I(48));t.cacheHandlers&&n(I(49)),t.scopeId&&!o&&n(I(50));const r=a(e)?ht(e,t):e,[s,i]=so();return Yt(r,c({},t,{prefixIdentifiers:false,nodeTransforms:[...s,...t.nodeTransforms||[]],directiveTransforms:c({},i,t.directiveTransforms||{})})),tn(r,c({},t,{prefixIdentifiers:false}))}const co=()=>({props:[]}),lo=Symbol(""),ao=Symbol(""),po=Symbol(""),uo=Symbol(""),fo=Symbol(""),ho=Symbol(""),mo=Symbol(""),go=Symbol(""),yo=Symbol(""),vo=Symbol("");let So;ge({[lo]:"vModelRadio",[ao]:"vModelCheckbox",[po]:"vModelText",[uo]:"vModelSelect",[fo]:"vModelDynamic",[ho]:"withModifiers",[mo]:"withKeys",[go]:"vShow",[yo]:"Transition",[vo]:"TransitionGroup"});const Eo=t("style,iframe,script,noscript",!0),bo={isVoidTag:k,isNativeTag:e=>N(e)||_(e),isPreTag:e=>"pre"===e,decodeEntities:function(e,t=!1){return So||(So=document.createElement("div")),t?(So.innerHTML=`<div foo="${e.replace(/"/g,"&quot;")}">`,So.children[0].getAttribute("foo")):(So.innerHTML=e,So.textContent)},isBuiltInComponent:e=>$e(e,"Transition")?yo:$e(e,"TransitionGroup")?vo:void 0,getNamespace(e,t){let n=t?t.ns:0;if(t&&2===n)if("annotation-xml"===t.tag){if("svg"===e)return 1;t.props.some((e=>6===e.type&&"encoding"===e.name&&null!=e.value&&("text/html"===e.value.content||"application/xhtml+xml"===e.value.content)))&&(n=0)}else/^m(?:[ions]|text)$/.test(t.tag)&&"mglyph"!==e&&"malignmark"!==e&&(n=0);else t&&1===n&&("foreignObject"!==t.tag&&"desc"!==t.tag&&"title"!==t.tag||(n=0));if(0===n){if("svg"===e)return 1;if("math"===e)return 2}return n},getTextMode({tag:e,ns:t}){if(0===t){if("textarea"===e||"title"===e)return 1;if(Eo(e))return 2}return 0}},To=e=>{1===e.type&&e.props.forEach(((t,n)=>{6===t.type&&"style"===t.name&&t.value&&(e.props[n]={type:7,name:"bind",arg:xe("style",!0,t.loc),exp:xo(t.value.content,t.loc),modifiers:[],loc:t.loc})}))},xo=(e,t)=>{const n=function(e){const t={};return e.replace(x,"").split(b).forEach((e=>{if(e){const n=e.split(T);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}(e);return xe(JSON.stringify(n),!1,t,3)};function No(e,t){return I(e,t)}const _o=t("passive,once,capture"),ko=t("stop,prevent,self,ctrl,shift,alt,meta,exact,middle"),Oo=t("left,right"),Co=t("onkeyup,onkeydown,onkeypress",!0),Io=(e,t)=>we(e)&&"onclick"===e.content.toLowerCase()?xe(t,!0):4!==e.type?Ne(["(",e,`) === "onClick" ? "${t}" : (`,e,")"]):e,Mo=(e,t)=>{1!==e.type||0!==e.tagType||"script"!==e.tag&&"style"!==e.tag||t.removeNode()},Ro=[To],Po={cloak:co,html:(e,t,n)=>{const{exp:o,loc:r}=e;return o||n.onError(No(53,r)),t.children.length&&(n.onError(No(54,r)),t.children.length=0),{props:[Te(xe("innerHTML",!0,r),o||xe("",!0))]}},text:(e,t,n)=>{const{exp:o,loc:r}=e;return o||n.onError(No(55,r)),t.children.length&&(n.onError(No(56,r)),t.children.length=0),{props:[Te(xe("textContent",!0),o?Ht(o,n)>0?o:_e(n.helperString(q),[o],r):xe("",!0))]}},model:(e,t,n)=>{const o=Zn(e,t,n);if(!o.props.length||1===t.tagType)return o;e.arg&&n.onError(No(58,e.arg.loc));const{tag:r}=t,s=n.isCustomElement(r);if("input"===r||"textarea"===r||"select"===r||s){let i=po,c=!1;if("input"===r||s){const o=ze(t,"type");if(o){if(7===o.type)i=fo;else if(o.value)switch(o.value.content){case"radio":i=lo;break;case"checkbox":i=ao;break;case"file":c=!0,n.onError(No(59,e.loc))}}else Ze(t)&&(i=fo)}else"select"===r&&(i=uo);c||(o.needRuntime=n.helper(i))}else n.onError(No(57,e.loc));return o.props=o.props.filter((e=>!(4===e.key.type&&"modelValue"===e.key.content))),o},on:(e,t,n)=>Kn(e,t,n,(t=>{const{modifiers:o}=e;if(!o.length)return t;let{key:r,value:s}=t.props[0];const{keyModifiers:i,nonKeyModifiers:c,eventOptionModifiers:l}=((e,t,n,o)=>{const r=[],s=[],i=[];for(let c=0;c<t.length;c++){const o=t[c];"native"===o&&pt("COMPILER_V_ON_NATIVE",n)||_o(o)?i.push(o):Oo(o)?we(e)?Co(e.content)?r.push(o):s.push(o):(r.push(o),s.push(o)):ko(o)?s.push(o):r.push(o)}return{keyModifiers:r,nonKeyModifiers:s,eventOptionModifiers:i}})(r,o,n);if(c.includes("right")&&(r=Io(r,"onContextmenu")),c.includes("middle")&&(r=Io(r,"onMouseup")),c.length&&(s=_e(n.helper(ho),[s,JSON.stringify(c)])),!i.length||we(r)&&!Co(r.content)||(s=_e(n.helper(mo),[s,JSON.stringify(i)])),l.length){const e=l.map(S).join("");r=we(r)?xe(`${r.content}${e}`,!0):Ne(["(",r,`) + "${e}"`])}return{props:[Te(r,s)]}})),show:(e,t,n)=>{const{exp:o,loc:r}=e;return o||n.onError(No(61,r)),{props:[],needRuntime:n.helper(go)}}};return e.BASE_TRANSITION=$,e.CAMELIZE=re,e.CAPITALIZE=se,e.CREATE_BLOCK=V,e.CREATE_COMMENT=F,e.CREATE_ELEMENT_BLOCK=A,e.CREATE_ELEMENT_VNODE=B,e.CREATE_SLOTS=Z,e.CREATE_STATIC=H,e.CREATE_TEXT=j,e.CREATE_VNODE=D,e.DOMDirectiveTransforms=Po,e.DOMNodeTransforms=Ro,e.FRAGMENT=M,e.GUARD_REACTIVE_PROPS=ne,e.IS_MEMO_SAME=he,e.IS_REF=fe,e.KEEP_ALIVE=w,e.MERGE_PROPS=X,e.NORMALIZE_CLASS=Q,e.NORMALIZE_PROPS=te,e.NORMALIZE_STYLE=ee,e.OPEN_BLOCK=L,e.POP_SCOPE_ID=ae,e.PUSH_SCOPE_ID=le,e.RENDER_LIST=z,e.RENDER_SLOT=Y,e.RESOLVE_COMPONENT=W,e.RESOLVE_DIRECTIVE=U,e.RESOLVE_DYNAMIC_COMPONENT=K,e.RESOLVE_FILTER=J,e.SET_BLOCK_TRACKING=ce,e.SUSPENSE=P,e.TELEPORT=R,e.TO_DISPLAY_STRING=q,e.TO_HANDLERS=oe,e.TO_HANDLER_KEY=ie,e.TRANSITION=yo,e.TRANSITION_GROUP=vo,e.TS_NODE_TYPES=["TSAsExpression","TSTypeAssertion","TSNonNullExpression","TSInstantiationExpression","TSSatisfiesExpression"],e.UNREF=ue,e.V_MODEL_CHECKBOX=ao,e.V_MODEL_DYNAMIC=fo,e.V_MODEL_RADIO=lo,e.V_MODEL_SELECT=uo,e.V_MODEL_TEXT=po,e.V_ON_WITH_KEYS=mo,e.V_ON_WITH_MODIFIERS=ho,e.V_SHOW=go,e.WITH_CTX=pe,e.WITH_DIRECTIVES=G,e.WITH_MEMO=de,e.advancePositionWithClone=Ue,e.advancePositionWithMutation=Je,e.assert=function(e,t){if(!e)throw new Error(t||"unexpected compiler condition")},e.baseCompile=io,e.baseParse=ht,e.buildDirectiveArgs=Bn,e.buildProps=Vn,e.buildSlots=In,e.checkCompatEnabled=pt,e.compile=function(e,t={}){return io(e,c({},bo,t,{nodeTransforms:[Mo,...Ro,...t.nodeTransforms||[]],directiveTransforms:c({},Po,t.directiveTransforms||{}),transformHoist:null}))},e.convertToBlock=Pe,e.createArrayExpression=Ee,e.createAssignmentExpression=function(e,t){return{type:24,left:e,right:t,loc:ye}},e.createBlockStatement=Ie,e.createCacheExpression=Ce,e.createCallExpression=_e,e.createCompilerError=I,e.createCompoundExpression=Ne,e.createConditionalExpression=Oe,e.createDOMCompilerError=No,e.createForLoopParams=_n,e.createFunctionExpression=ke,e.createIfStatement=function(e,t,n){return{type:23,test:e,consequent:t,alternate:n,loc:ye}},e.createInterpolation=function(e,t){return{type:5,loc:t,content:a(e)?xe(e,!1,t):e}},e.createObjectExpression=be,e.createObjectProperty=Te,e.createReturnStatement=function(e){return{type:26,returns:e,loc:ye}},e.createRoot=ve,e.createSequenceExpression=function(e){return{type:25,expressions:e,loc:ye}},e.createSimpleExpression=xe,e.createStructuralDirectiveTransform=qt,e.createTemplateLiteral=function(e){return{type:22,elements:e,loc:ye}},e.createTransformContext=zt,e.createVNodeCall=Se,e.extractIdentifiers=pn,e.findDir=Ge,e.findProp=ze,e.generate=tn,e.generateCodeFrame=function(e,t=0,n=e.length){let o=e.split(/(\r?\n)/);const r=o.filter(((e,t)=>t%2==1));o=o.filter(((e,t)=>t%2==0));let s=0;const i=[];for(let c=0;c<o.length;c++)if(s+=o[c].length+(r[c]&&r[c].length||0),s>=t){for(let e=c-2;e<=c+2||n>s;e++){if(e<0||e>=o.length)continue;const l=e+1;i.push(`${l}${" ".repeat(Math.max(3-String(l).length,0))}|  ${o[e]}`);const a=o[e].length,p=r[e]&&r[e].length||0;if(e===c){const e=t-(s-(a+p)),o=Math.max(1,n>s?a-e:n-t);i.push("   |  "+" ".repeat(e)+"^".repeat(o))}else if(e>c){if(n>s){const e=Math.max(Math.min(n-s,a),1);i.push("   |  "+"^".repeat(e))}s+=a+p}}break}return i.join("\n")},e.getBaseTransformPreset=so,e.getConstantType=Ht,e.getInnerRange=Ke,e.getMemoedVNodeCall=it,e.getVNodeBlockHelper=Re,e.getVNodeHelper=Me,e.hasDynamicKeyVBind=Ze,e.hasScopeRef=function e(t,n){if(!t||0===Object.keys(n).length)return!1;switch(t.type){case 1:for(let o=0;o<t.props.length;o++){const r=t.props[o];if(7===r.type&&(e(r.arg,n)||e(r.exp,n)))return!0}return t.children.some((t=>e(t,n)));case 11:return!!e(t.source,n)||t.children.some((t=>e(t,n)));case 9:return t.branches.some((t=>e(t,n)));case 10:return!!e(t.condition,n)||t.children.some((t=>e(t,n)));case 4:return!t.isStatic&&Ae(t.content)&&!!n[t.content];case 8:return t.children.some((t=>u(t)&&e(t,n)));case 5:case 12:return e(t.content,n);default:return!1}},e.helperNameMap=me,e.injectProp=ot,e.isBuiltInType=$e,e.isCoreComponent=Le,e.isFunctionType=e=>/Function(?:Expression|Declaration)$|Method$/.test(e.type),e.isInDestructureAssignment=function(e,t){if(e&&("ObjectProperty"===e.type||"ArrayPattern"===e.type)){let e=t.length;for(;e--;){const n=t[e];if("AssignmentExpression"===n.type)return!0;if("ObjectProperty"!==n.type&&!n.type.endsWith("Pattern"))break}}return!1},e.isMemberExpression=We,e.isMemberExpressionBrowser=je,e.isMemberExpressionNode=He,e.isReferencedIdentifier=function(e,t,n){return!1},e.isSimpleIdentifier=Ae,e.isSlotOutlet=et,e.isStaticArgOf=Ye,e.isStaticExp=we,e.isStaticProperty=un,e.isStaticPropertyKey=(e,t)=>un(t)&&t.key===e,e.isTemplateNode=Qe,e.isText=qe,e.isVSlot=Xe,e.locStub=ye,e.noopDirectiveTransform=co,e.parse=function(e,t={}){return ht(e,c({},bo,t))},e.parserOptions=bo,e.processExpression=fn,e.processFor=Sn,e.processIf=hn,e.processSlotOutlet=Hn,e.registerRuntimeHelpers=ge,e.resolveComponentType=Ln,e.stringifyExpression=function e(t){return a(t)?t:4===t.type?t.content:t.children.map(e).join("")},e.toValidAssetId=st,e.trackSlotScopes=On,e.trackVForSlotScopes=(e,t)=>{let n;if(Qe(e)&&e.props.some(Xe)&&(n=Ge(e,"for"))){const e=n.parseResult=xn(n.exp);if(e){const{value:n,key:o,index:r}=e,{addIdentifiers:s,removeIdentifiers:i}=t;return n&&s(n),o&&s(o),r&&s(r),()=>{n&&i(n),o&&i(o),r&&i(r)}}}},e.transform=Yt,e.transformBind=Un,e.transformElement=$n,e.transformExpression=(e,t)=>{if(5===e.type)e.content=fn(e.content,t);else if(1===e.type)for(let n=0;n<e.props.length;n++){const o=e.props[n];if(7===o.type&&"for"!==o.name){const e=o.exp,n=o.arg;!e||4!==e.type||"on"===o.name&&n||(o.exp=fn(e,t,"slot"===o.name)),n&&4===n.type&&!n.isStatic&&(o.arg=fn(n,t))}}},e.transformModel=Zn,e.transformOn=Kn,e.transformStyle=To,e.traverseNode=Zt,e.walkBlockDeclarations=function(e,t){for(const n of e.body)if("VariableDeclaration"===n.type){if(n.declare)continue;for(const e of n.declarations)for(const n of pn(e.id))t(n)}else if("FunctionDeclaration"===n.type||"ClassDeclaration"===n.type){if(n.declare||!n.id)continue;t(n.id)}},e.walkFunctionParams=function(e,t){for(const n of e.params)for(const e of pn(n))t(e)},e.walkIdentifiers=function(e,t,n=!1,o=[],r=Object.create(null)){},e.warnDeprecation=function(e,t,n,...o){if("suppress-warning"===lt(e,t))return;const{message:r,link:s}=ct[e],i=`(deprecation ${e}) ${"function"==typeof r?r(...o):r}${s?`\n  Details: ${s}`:""}`,c=new SyntaxError(i);c.code=e,n&&(c.loc=n),t.onWarn(c)},e}({});
