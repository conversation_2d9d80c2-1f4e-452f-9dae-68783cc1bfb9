{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/actions/IActionHandler.ts"], "names": ["BaseActionHandler", "_decorator", "ccclass", "constructor", "actionType", "valueType", "displayName", "getDisplayName", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "target", "safeGetProperty", "propertyPath", "parts", "split", "current", "part", "undefined", "error", "console", "warn", "safeSetProperty", "value", "i", "length", "finalProperty"], "mappings": ";;;2FAwCsBA,iB;;;;;;;;;;;;;;;AAxCbC,MAAAA,U,OAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA;AAAF,O,GAAcD,U;AAEpB;AACA;AACA;;AA+BA;AACA;AACA;mCACsBD,iB,GAAf,MAAeA,iBAAf,CAA+E;AAClFG,QAAAA,WAAW,CACSC,UADT,EAESC,SAFT,EAGCC,WAHD,EAIT;AAAA,eAHkBF,UAGlB,GAHkBA,UAGlB;AAAA,eAFkBC,SAElB,GAFkBA,SAElB;AAAA,eADUC,WACV,GADUA,WACV;AAAE;;AAMJC,QAAAA,cAAc,GAAW;AACrB,iBAAO,KAAKD,WAAL,gBAA8B,KAAKF,UAA1C;AACH;;AAEDI,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC;AACA,iBAAOA,MAAM,IAAI,IAAjB;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,eAAe,CAACD,MAAD,EAAcE,YAAd,EAAyC;AAC9D,cAAI;AACA,gBAAMC,KAAK,GAAGD,YAAY,CAACE,KAAb,CAAmB,GAAnB,CAAd;AACA,gBAAIC,OAAO,GAAGL,MAAd;;AACA,iBAAK,IAAMM,IAAX,IAAmBH,KAAnB,EAA0B;AACtB,kBAAIE,OAAO,IAAI,IAAf,EAAqB,OAAOE,SAAP;AACrBF,cAAAA,OAAO,GAAGA,OAAO,CAACC,IAAD,CAAjB;AACH;;AACD,mBAAOD,OAAP;AACH,WARD,CAQE,OAAOG,KAAP,EAAc;AACZC,YAAAA,OAAO,CAACC,IAAR,6BAAuCR,YAAvC,oBAAoEM,KAApE;AACA,mBAAOD,SAAP;AACH;AACJ;AAED;AACJ;AACA;;;AACcI,QAAAA,eAAe,CAACX,MAAD,EAAcE,YAAd,EAAoCU,KAApC,EAAyD;AAC9E,cAAI;AACA,gBAAMT,KAAK,GAAGD,YAAY,CAACE,KAAb,CAAmB,GAAnB,CAAd;AACA,gBAAIC,OAAO,GAAGL,MAAd,CAFA,CAIA;;AACA,iBAAK,IAAIa,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGV,KAAK,CAACW,MAAN,GAAe,CAAnC,EAAsCD,CAAC,EAAvC,EAA2C;AACvC,kBAAIR,OAAO,IAAI,IAAf,EAAqB,OAAO,KAAP;AACrBA,cAAAA,OAAO,GAAGA,OAAO,CAACF,KAAK,CAACU,CAAD,CAAN,CAAjB;AACH;;AAED,gBAAIR,OAAO,IAAI,IAAf,EAAqB,OAAO,KAAP,CAVrB,CAYA;;AACA,gBAAMU,aAAa,GAAGZ,KAAK,CAACA,KAAK,CAACW,MAAN,GAAe,CAAhB,CAA3B;AACAT,YAAAA,OAAO,CAACU,aAAD,CAAP,GAAyBH,KAAzB;AACA,mBAAO,IAAP;AACH,WAhBD,CAgBE,OAAOJ,KAAP,EAAc;AACZC,YAAAA,OAAO,CAACC,IAAR,6BAAuCR,YAAvC,kBAAkEM,KAAlE;AACA,mBAAO,KAAP;AACH;AACJ;;AA9DiF,O", "sourcesContent": ["import { _decorator } from 'cc';\nimport { ActionValue } from './ActionValue';\nconst { ccclass } = _decorator;\n\n/**\n * Interface for handling specific action types\n */\nexport interface IActionHandler<T = ActionValue> {\n    readonly actionType: number;\n    readonly valueType: 'number' | 'boolean' | 'string' | 'rpn';\n    \n    /**\n     * Get the current value from the target object\n     */\n    getValue(target: any): T;\n    \n    /**\n     * Set the value on the target object\n     */\n    setValue(target: any, value: T): void;\n    \n    /**\n     * Whether this action type supports smooth interpolation\n     */\n    canInterpolate(): boolean;\n    \n    /**\n     * Optional: Get a human-readable name for this action\n     */\n    getDisplayName?(): string;\n    \n    /**\n     * Optional: Validate if the target object is compatible with this handler\n     */\n    isValidTarget?(target: any): boolean;\n}\n\n/**\n * Base class for action handlers providing common functionality\n */\nexport abstract class BaseActionHandler<T = ActionValue> implements IActionHandler<T> {\n    constructor(\n        public readonly actionType: number,\n        public readonly valueType: 'number' | 'boolean' | 'string' | 'rpn',\n        private displayName?: string\n    ) {}\n    \n    abstract getValue(target: any): T;\n    abstract setValue(target: any, value: T): void;\n    abstract canInterpolate(): boolean;\n    \n    getDisplayName(): string {\n        return this.displayName || `Action_${this.actionType}`;\n    }\n    \n    isValidTarget(target: any): boolean {\n        // Default implementation - can be overridden by specific handlers\n        return target != null;\n    }\n    \n    /**\n     * Helper method to safely get a property from target\n     */\n    protected safeGetProperty(target: any, propertyPath: string): any {\n        try {\n            const parts = propertyPath.split('.');\n            let current = target;\n            for (const part of parts) {\n                if (current == null) return undefined;\n                current = current[part];\n            }\n            return current;\n        } catch (error) {\n            console.warn(`Failed to get property ${propertyPath} from target:`, error);\n            return undefined;\n        }\n    }\n    \n    /**\n     * Helper method to safely set a property on target\n     */\n    protected safeSetProperty(target: any, propertyPath: string, value: any): boolean {\n        try {\n            const parts = propertyPath.split('.');\n            let current = target;\n            \n            // Navigate to the parent object\n            for (let i = 0; i < parts.length - 1; i++) {\n                if (current == null) return false;\n                current = current[parts[i]];\n            }\n            \n            if (current == null) return false;\n            \n            // Set the final property\n            const finalProperty = parts[parts.length - 1];\n            current[finalProperty] = value;\n            return true;\n        } catch (error) {\n            console.warn(`Failed to set property ${propertyPath} on target:`, error);\n            return false;\n        }\n    }\n}\n"]}