System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, eEmitterActionType, eBulletActionType, eConditionGroupOp, eEventConditionOp, eEmitterStatus, eEasing, BulletSystem, EventActionRunner, EventGroupRunner, _crd;

  function _reportPossibleCrUseOfeEmitterActionType(extras) {
    _reporterNs.report("eEmitterActionType", "../data/EventActionData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeBulletActionType(extras) {
    _reporterNs.report("eBulletActionType", "../data/EventActionData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitterActionData(extras) {
    _reporterNs.report("EmitterActionData", "../data/EventActionData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletActionData(extras) {
    _reporterNs.report("BulletActionData", "../data/EventActionData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeConditionGroupOp(extras) {
    _reporterNs.report("eConditionGroupOp", "../data/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitterGroupData(extras) {
    _reporterNs.report("EmitterGroupData", "../data/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletGroupData(extras) {
    _reporterNs.report("BulletGroupData", "../data/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEventConditionOp(extras) {
    _reporterNs.report("eEventConditionOp", "../data/EventConditionData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBullet(extras) {
    _reporterNs.report("Bullet", "./Bullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEmitterStatus(extras) {
    _reporterNs.report("eEmitterStatus", "./Emitter", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitter(extras) {
    _reporterNs.report("Emitter", "./Emitter", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEasing(extras) {
    _reporterNs.report("eEasing", "../move/IMovable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletSystem(extras) {
    _reporterNs.report("BulletSystem", "./BulletSystem", _context.meta, extras);
  }

  _export({
    EventActionRunner: void 0,
    EventGroupRunner: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
    }, function (_unresolved_2) {
      eEmitterActionType = _unresolved_2.eEmitterActionType;
      eBulletActionType = _unresolved_2.eBulletActionType;
    }, function (_unresolved_3) {
      eConditionGroupOp = _unresolved_3.eConditionGroupOp;
    }, function (_unresolved_4) {
      eEventConditionOp = _unresolved_4.eEventConditionOp;
    }, function (_unresolved_5) {
      eEmitterStatus = _unresolved_5.eEmitterStatus;
    }, function (_unresolved_6) {
      eEasing = _unresolved_6.eEasing;
    }, function (_unresolved_7) {
      BulletSystem = _unresolved_7.BulletSystem;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "8327aG2o0xBM6isOQ2nVnSY", "EventRunner", undefined);

      __checkObsolete__(['_decorator']);

      /**
       * Legacy EventActionRunner - simplified version for backward compatibility
       * Use GenericActionRunner for new features
       */
      _export("EventActionRunner", EventActionRunner = class EventActionRunner {
        constructor(owner, data) {
          this.startValue = 0;
          this.targetValue = 0;
          this.elapsedTime = 0;
          this._isCompleted = false;
          this.data = void 0;
          this.owner = null;
          this.owner = owner;
          this.data = data;
        }

        get isCompleted() {
          return this._isCompleted;
        }

        getOwnerAsEmitter() {
          return this.owner;
        }

        getOwnerAsBullet() {
          return this.owner;
        }

        static canLerp(actionType) {
          // Numeric properties that can be interpolated
          var lerpableEmitterActions = [(_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
            error: Error()
          }), eEmitterActionType) : eEmitterActionType).Emitter_InitialDelay, (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
            error: Error()
          }), eEmitterActionType) : eEmitterActionType).Emitter_PrewarmDuration, (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
            error: Error()
          }), eEmitterActionType) : eEmitterActionType).Emitter_Duration, (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
            error: Error()
          }), eEmitterActionType) : eEmitterActionType).Emitter_ElapsedTime, (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
            error: Error()
          }), eEmitterActionType) : eEmitterActionType).Emitter_LoopInterval, (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
            error: Error()
          }), eEmitterActionType) : eEmitterActionType).Emitter_PerEmitInterval, (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
            error: Error()
          }), eEmitterActionType) : eEmitterActionType).Emitter_PerEmitCount, (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
            error: Error()
          }), eEmitterActionType) : eEmitterActionType).Emitter_PerEmitOffsetX, (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
            error: Error()
          }), eEmitterActionType) : eEmitterActionType).Emitter_Angle, (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
            error: Error()
          }), eEmitterActionType) : eEmitterActionType).Emitter_Count];
          var lerpableBulletActions = [(_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_Duration, (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_ElapsedTime, (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_PosX, (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_PosY, (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_Damage, (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_Speed, (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_SpeedAngle, (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_Acceleration, (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_AccelerationAngle, (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_Scale, (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_ColorR, (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_ColorG, (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_ColorB, (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_ColorA];
          return lerpableEmitterActions.indexOf(actionType) !== -1 || lerpableBulletActions.indexOf(actionType) !== -1;
        }

        getInitialValue() {
          // Simplified initial value getter - uses existing property access patterns
          // For new features, use GenericActionRunner which has better value handling
          try {
            if (this.data.actionType < (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_Duration) {
              // Emitter actions
              var emitter = this.getOwnerAsEmitter();

              switch (this.data.actionType) {
                case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
                  error: Error()
                }), eEmitterActionType) : eEmitterActionType).Emitter_Active:
                  return emitter.status != (_crd && eEmitterStatus === void 0 ? (_reportPossibleCrUseOfeEmitterStatus({
                    error: Error()
                  }), eEmitterStatus) : eEmitterStatus).None ? 1 : 0;

                case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
                  error: Error()
                }), eEmitterActionType) : eEmitterActionType).Emitter_InitialDelay:
                  return emitter.initialDelay;

                case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
                  error: Error()
                }), eEmitterActionType) : eEmitterActionType).Emitter_Duration:
                  return emitter.emitDuration;

                case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
                  error: Error()
                }), eEmitterActionType) : eEmitterActionType).Emitter_PerEmitInterval:
                  return emitter.perEmitInterval;

                case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
                  error: Error()
                }), eEmitterActionType) : eEmitterActionType).Emitter_Angle:
                  return emitter.angle;
                // Add other commonly used emitter properties as needed

                default:
                  return 0;
              }
            } else {
              // Bullet actions
              var bullet = this.getOwnerAsBullet();

              switch (this.data.actionType) {
                case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
                  error: Error()
                }), eBulletActionType) : eBulletActionType).Bullet_Duration:
                  return bullet.duration;

                case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
                  error: Error()
                }), eBulletActionType) : eBulletActionType).Bullet_Speed:
                  return bullet.mover.speed;

                case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
                  error: Error()
                }), eBulletActionType) : eBulletActionType).Bullet_PosX:
                  return bullet.node.getPosition().x;

                case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
                  error: Error()
                }), eBulletActionType) : eBulletActionType).Bullet_PosY:
                  return bullet.node.getPosition().y;

                case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
                  error: Error()
                }), eBulletActionType) : eBulletActionType).Bullet_ColorA:
                  return bullet.bulletSprite.color.a;
                // Add other commonly used bullet properties as needed

                default:
                  return 0;
              }
            }
          } catch (error) {
            console.warn('EventActionRunner: Failed to get initial value for action type', this.data.actionType, error);
            return 0;
          }
        }

        getTargetValue() {
          if (EventActionRunner.canLerp(this.data.actionType)) {
            if (this.data.isRandom) {
              return Math.random() * (this.data.maxValue - this.data.minValue) + this.data.minValue;
            }

            return this.data.minValue;
          }

          return this.data.boolValue ? 1 : 0;
        }

        start() {
          if (!this.data) {
            console.error('EventActionRunner: data is null');
            return;
          }

          this.startValue = this.getInitialValue();
          this.targetValue = this.getTargetValue();
          this.elapsedTime = 0;
          this._isCompleted = false;
        }

        tick(dt) {
          this.elapsedTime += dt;

          if (this.elapsedTime >= this.data.duration) {
            this._isCompleted = true;
            this.apply(this.targetValue);
          } else if (EventActionRunner.canLerp(this.data.actionType)) {
            var currentValue = this.lerpValue(this.startValue, this.targetValue);
            this.apply(currentValue);
          }
        }

        lerpValue(startValue, targetValue) {
          var progress = Math.min(1.0, this.elapsedTime / this.data.duration);
          var easedProgress = EventActionRunner.applyEasing(this.data.easing, progress);
          return startValue + (targetValue - startValue) * easedProgress;
        }

        apply(currentValue) {
          try {
            if (this.data.actionType < (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_Duration) {
              this.getOwnerAsEmitter().applyAction(this.data.actionType, currentValue);
            } else {
              this.getOwnerAsBullet().applyAction(this.data.actionType, currentValue);
            }
          } catch (error) {
            console.warn('EventActionRunner: Failed to apply action', this.data.actionType, currentValue, error);
            this._isCompleted = true; // Stop on error
          }
        }

        static applyEasing(easing, t) {
          switch (easing) {
            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).Linear:
              return t;

            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).InSine:
              return 1 - Math.cos(t * Math.PI / 2);

            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).OutSine:
              return Math.sin(t * Math.PI / 2);

            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).InOutSine:
              return -(Math.cos(Math.PI * t) - 1) / 2;

            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).InQuad:
              return t * t;

            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).OutQuad:
              return 1 - (1 - t) * (1 - t);

            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).InOutQuad:
              return t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2;

            default:
              return t;
          }
        }

      });

      _export("EventGroupRunner", EventGroupRunner = class EventGroupRunner {
        constructor(data) {
          this.data = void 0;
          this.owner = null;
          this.isTriggered = false;
          this.isConditionAMet = false;
          this.isConditionBMet = false;
          this.data = data;
        }

        init(owner) {
          this.owner = owner;
          this.isTriggered = this.isConditionAMet = this.isConditionBMet = false;
          this.registerListeners();
        }

        tryTrigger() {
          switch (this.data.groupOp) {
            case (_crd && eConditionGroupOp === void 0 ? (_reportPossibleCrUseOfeConditionGroupOp({
              error: Error()
            }), eConditionGroupOp) : eConditionGroupOp).And:
              this.isTriggered = this.isConditionAMet && this.isConditionBMet;
              break;

            case (_crd && eConditionGroupOp === void 0 ? (_reportPossibleCrUseOfeConditionGroupOp({
              error: Error()
            }), eConditionGroupOp) : eConditionGroupOp).Or:
              this.isTriggered = this.isConditionAMet || this.isConditionBMet;
              break;
          }

          if (this.isTriggered) {
            for (var action of this.data.actions) {
              (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
                error: Error()
              }), BulletSystem) : BulletSystem).createActionRunner(this.owner, action);
            }

            this.unRegisterListeners();
          }
        }

        registerListeners() {
          if (this.data.conditionA) {
            this.isConditionAMet = false;
            (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).on(this.data.conditionA.eventType, this.onConditionAChanged.bind(this));
          } else {
            this.isConditionAMet = true;
          }

          if (this.data.conditionB) {
            this.isConditionBMet = false;
            (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).on(this.data.conditionB.eventType, this.onConditionBChanged.bind(this));
          } else {
            this.isConditionBMet = true;
          }
        }

        unRegisterListeners() {
          if (this.data.conditionA) {
            (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).off(this.data.conditionA.eventType, this.onConditionAChanged.bind(this));
          }

          if (this.data.conditionB) {
            (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).off(this.data.conditionB.eventType, this.onConditionBChanged.bind(this));
          }
        }

        onConditionAChanged(value) {
          // Handle condition A change
          this.isConditionAMet = EventGroupRunner.compareValue(this.data.conditionA.eventOp, value, this.data.conditionA.targetValue);
          this.tryTrigger();
        }

        onConditionBChanged(value) {
          // Handle condition B change
          this.isConditionBMet = EventGroupRunner.compareValue(this.data.conditionB.eventOp, value, this.data.conditionB.targetValue);
          this.tryTrigger();
        }

        static compareValue(op, a, b) {
          switch (op) {
            case (_crd && eEventConditionOp === void 0 ? (_reportPossibleCrUseOfeEventConditionOp({
              error: Error()
            }), eEventConditionOp) : eEventConditionOp).Equal:
              return a === b;

            case (_crd && eEventConditionOp === void 0 ? (_reportPossibleCrUseOfeEventConditionOp({
              error: Error()
            }), eEventConditionOp) : eEventConditionOp).NotEqual:
              return a !== b;

            case (_crd && eEventConditionOp === void 0 ? (_reportPossibleCrUseOfeEventConditionOp({
              error: Error()
            }), eEventConditionOp) : eEventConditionOp).Greater:
              return a > b;

            case (_crd && eEventConditionOp === void 0 ? (_reportPossibleCrUseOfeEventConditionOp({
              error: Error()
            }), eEventConditionOp) : eEventConditionOp).Less:
              return a < b;

            case (_crd && eEventConditionOp === void 0 ? (_reportPossibleCrUseOfeEventConditionOp({
              error: Error()
            }), eEventConditionOp) : eEventConditionOp).GreaterEqual:
              return a >= b;

            case (_crd && eEventConditionOp === void 0 ? (_reportPossibleCrUseOfeEventConditionOp({
              error: Error()
            }), eEventConditionOp) : eEventConditionOp).LessEqual:
              return a <= b;

            default:
              return false;
          }
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c94d2dfc57fee725b0a5b484279cd92b068cee1e.js.map