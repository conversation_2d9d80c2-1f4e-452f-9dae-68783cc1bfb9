System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, eEmitterActionType, eBulletActionType, eConditionGroupOp, eEventConditionOp, eEmitterStatus, eEasing, BulletSystem, EventActionRunner, EventGroupRunner, _crd;

  function _reportPossibleCrUseOfeEmitterActionType(extras) {
    _reporterNs.report("eEmitterActionType", "../data/EventActionData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeBulletActionType(extras) {
    _reporterNs.report("eBulletActionType", "../data/EventActionData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitterActionData(extras) {
    _reporterNs.report("EmitterActionData", "../data/EventActionData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletActionData(extras) {
    _reporterNs.report("BulletActionData", "../data/EventActionData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeConditionGroupOp(extras) {
    _reporterNs.report("eConditionGroupOp", "../data/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitterGroupData(extras) {
    _reporterNs.report("EmitterGroupData", "../data/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletGroupData(extras) {
    _reporterNs.report("BulletGroupData", "../data/EventGroupData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEventConditionOp(extras) {
    _reporterNs.report("eEventConditionOp", "../data/EventConditionData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBullet(extras) {
    _reporterNs.report("Bullet", "./Bullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEmitterStatus(extras) {
    _reporterNs.report("eEmitterStatus", "./Emitter", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitter(extras) {
    _reporterNs.report("Emitter", "./Emitter", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEasing(extras) {
    _reporterNs.report("eEasing", "../move/IMovable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletSystem(extras) {
    _reporterNs.report("BulletSystem", "./BulletSystem", _context.meta, extras);
  }

  _export({
    EventActionRunner: void 0,
    EventGroupRunner: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
    }, function (_unresolved_2) {
      eEmitterActionType = _unresolved_2.eEmitterActionType;
      eBulletActionType = _unresolved_2.eBulletActionType;
    }, function (_unresolved_3) {
      eConditionGroupOp = _unresolved_3.eConditionGroupOp;
    }, function (_unresolved_4) {
      eEventConditionOp = _unresolved_4.eEventConditionOp;
    }, function (_unresolved_5) {
      eEmitterStatus = _unresolved_5.eEmitterStatus;
    }, function (_unresolved_6) {
      eEasing = _unresolved_6.eEasing;
    }, function (_unresolved_7) {
      BulletSystem = _unresolved_7.BulletSystem;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "8327aG2o0xBM6isOQ2nVnSY", "EventRunner", undefined);

      __checkObsolete__(['_decorator', 'misc', 'Component', 'Enum', 'Vec3']);

      _export("EventActionRunner", EventActionRunner = class EventActionRunner {
        get isCompleted() {
          return this._isCompleted;
        }

        get isRunning() {
          return !this._isCompleted && this.elapsedTime > 0;
        } // readonly EventActionData


        constructor(owner, data) {
          this.startValue = 0;
          this.targetValue = 0;
          this.elapsedTime = 0;
          this._isCompleted = false;
          this.data = void 0;
          this.owner = null;
          this.owner = owner;
          this.data = data;
        }

        getOwnerAsEmitter() {
          return this.owner;
        }

        getOwnerAsBullet() {
          return this.owner;
        }

        static canLerp(actionType) {
          if (actionType < (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_Duration) {
            switch (actionType) {
              case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
                error: Error()
              }), eEmitterActionType) : eEmitterActionType).Emitter_InitialDelay:
              case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
                error: Error()
              }), eEmitterActionType) : eEmitterActionType).Emitter_PrewarmDuration:
              case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
                error: Error()
              }), eEmitterActionType) : eEmitterActionType).Emitter_Duration:
              case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
                error: Error()
              }), eEmitterActionType) : eEmitterActionType).Emitter_ElapsedTime:
              case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
                error: Error()
              }), eEmitterActionType) : eEmitterActionType).Emitter_LoopInterval:
              case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
                error: Error()
              }), eEmitterActionType) : eEmitterActionType).Emitter_PerEmitInterval:
              case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
                error: Error()
              }), eEmitterActionType) : eEmitterActionType).Emitter_PerEmitCount:
              case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
                error: Error()
              }), eEmitterActionType) : eEmitterActionType).Emitter_PerEmitOffsetX:
              case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
                error: Error()
              }), eEmitterActionType) : eEmitterActionType).Emitter_Angle:
                return true;

              default:
                return false;
            }
          } else {
            switch (actionType) {
              case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
                error: Error()
              }), eBulletActionType) : eBulletActionType).Bullet_Duration:
              case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
                error: Error()
              }), eBulletActionType) : eBulletActionType).Bullet_ElapsedTime:
              case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
                error: Error()
              }), eBulletActionType) : eBulletActionType).Bullet_PosX:
              case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
                error: Error()
              }), eBulletActionType) : eBulletActionType).Bullet_PosY:
              case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
                error: Error()
              }), eBulletActionType) : eBulletActionType).Bullet_Damage:
              case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
                error: Error()
              }), eBulletActionType) : eBulletActionType).Bullet_Speed:
              case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
                error: Error()
              }), eBulletActionType) : eBulletActionType).Bullet_SpeedAngle:
              case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
                error: Error()
              }), eBulletActionType) : eBulletActionType).Bullet_Acceleration:
              case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
                error: Error()
              }), eBulletActionType) : eBulletActionType).Bullet_AccelerationAngle:
              case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
                error: Error()
              }), eBulletActionType) : eBulletActionType).Bullet_Scale:
              case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
                error: Error()
              }), eBulletActionType) : eBulletActionType).Bullet_ColorR:
              case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
                error: Error()
              }), eBulletActionType) : eBulletActionType).Bullet_ColorG:
              case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
                error: Error()
              }), eBulletActionType) : eBulletActionType).Bullet_ColorB:
              case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
                error: Error()
              }), eBulletActionType) : eBulletActionType).Bullet_ColorA:
                return true;

              default:
                return false;
            }
          }
        }

        getInitialValue() {
          switch (this.data.actionType) {
            case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
              error: Error()
            }), eEmitterActionType) : eEmitterActionType).Emitter_Active:
              return this.getOwnerAsEmitter().status != (_crd && eEmitterStatus === void 0 ? (_reportPossibleCrUseOfeEmitterStatus({
                error: Error()
              }), eEmitterStatus) : eEmitterStatus).None ? 1 : 0;

            case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
              error: Error()
            }), eEmitterActionType) : eEmitterActionType).Emitter_InitialDelay:
              return this.getOwnerAsEmitter().initialDelay;

            case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
              error: Error()
            }), eEmitterActionType) : eEmitterActionType).Emitter_PrewarmDuration:
              return this.getOwnerAsEmitter().preWarmDuration;

            case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
              error: Error()
            }), eEmitterActionType) : eEmitterActionType).Emitter_Duration:
              return this.getOwnerAsEmitter().emitDuration;

            case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
              error: Error()
            }), eEmitterActionType) : eEmitterActionType).Emitter_ElapsedTime:
              return this.getOwnerAsEmitter().totalElapsedTime;

            case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
              error: Error()
            }), eEmitterActionType) : eEmitterActionType).Emitter_LoopInterval:
              return this.getOwnerAsEmitter().loopInterval;

            case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
              error: Error()
            }), eEmitterActionType) : eEmitterActionType).Emitter_PerEmitInterval:
              return this.getOwnerAsEmitter().perEmitInterval;

            case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
              error: Error()
            }), eEmitterActionType) : eEmitterActionType).Emitter_PerEmitCount:
              return this.getOwnerAsEmitter().perEmitCount;

            case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
              error: Error()
            }), eEmitterActionType) : eEmitterActionType).Emitter_PerEmitOffsetX:
              return this.getOwnerAsEmitter().perEmitOffsetX;

            case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
              error: Error()
            }), eEmitterActionType) : eEmitterActionType).Emitter_Angle:
              return this.getOwnerAsEmitter().angle;

            case (_crd && eEmitterActionType === void 0 ? (_reportPossibleCrUseOfeEmitterActionType({
              error: Error()
            }), eEmitterActionType) : eEmitterActionType).Emitter_Count:
              return this.getOwnerAsEmitter().count;
            // 子弹部分

            case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_Duration:
              return this.getOwnerAsBullet().duration;

            case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_ElapsedTime:
              return this.getOwnerAsBullet().elapsedTime;

            case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_PosX:
              return this.getOwnerAsBullet().node.getPosition().x;

            case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_PosY:
              return this.getOwnerAsBullet().node.getPosition().y;

            case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_Damage:
              return this.getOwnerAsBullet().damage;

            case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_Speed:
              return this.getOwnerAsBullet().mover.speed;

            case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_SpeedAngle:
              return this.getOwnerAsBullet().mover.speedAngle;

            case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_Acceleration:
              return this.getOwnerAsBullet().mover.acceleration;

            case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_AccelerationAngle:
              return this.getOwnerAsBullet().mover.accelerationAngle;

            case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_Scale:
              return this.getOwnerAsBullet().node.getScale().x;

            case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_ColorR:
              return this.getOwnerAsBullet().bulletSprite.color.r;

            case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_ColorG:
              return this.getOwnerAsBullet().bulletSprite.color.g;

            case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_ColorB:
              return this.getOwnerAsBullet().bulletSprite.color.b;

            case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_ColorA:
              return this.getOwnerAsBullet().bulletSprite.color.a;

            case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_FaceMovingDir:
              return this.getOwnerAsBullet().mover.isFacingMoveDir ? 1 : 0;

            case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_TrackingTarget:
              return this.getOwnerAsBullet().mover.isTrackingTarget ? 1 : 0;

            case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_Destructive:
              return this.getOwnerAsBullet().isDestructive ? 1 : 0;

            case (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
              error: Error()
            }), eBulletActionType) : eBulletActionType).Bullet_DestructiveOnHit:
              return this.getOwnerAsBullet().isDestructiveOnHit ? 1 : 0;

            default:
              break;
          }

          return 0;
        }

        getTargetValue() {
          if (EventActionRunner.canLerp(this.data.actionType)) {
            if (this.data.isRandom) {
              return Math.random() * (this.data.maxValue - this.data.minValue) + this.data.minValue;
            }

            return this.data.minValue;
          }

          return this.data.boolValue ? 1 : 0;
        }

        start() {
          if (!this.data) {
            console.error('EventActionRunner: data is null');
            return;
          }

          this.startValue = this.getInitialValue();
          this.targetValue = this.getTargetValue();
          this.elapsedTime = 0;
          this._isCompleted = false;
        }

        tick(dt) {
          // Update current time
          this.elapsedTime += dt;

          if (this.elapsedTime >= this.data.duration) {
            this._isCompleted = true;
            this.apply(this.targetValue);
          } else {
            if (EventActionRunner.canLerp(this.data.actionType)) {
              var currentValue = this.lerpValue(this.startValue, this.targetValue);
              this.apply(currentValue);
            }
          }
        }

        lerpValue(startValue, targetValue) {
          // Calculate progress ratio (0 to 1)
          var progress = Math.min(1.0, this.elapsedTime / this.data.duration); // Apply easing function

          var easedProgress = EventActionRunner.applyEasing(this.data.easing, progress); // Interpolate between start value and target value

          return startValue + (targetValue - startValue) * easedProgress;
        }

        apply(currentValue) {
          if (this.data.actionType < (_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_Duration) {
            this.getOwnerAsEmitter().applyAction(this.data.actionType, currentValue);
          } else {
            this.getOwnerAsBullet().applyAction(this.data.actionType, currentValue);
          }
        }

        static applyEasing(easing, t) {
          switch (easing) {
            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).Linear:
              return t;

            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).InSine:
              return 1 - Math.cos(t * Math.PI / 2);

            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).OutSine:
              return Math.sin(t * Math.PI / 2);

            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).InOutSine:
              return -(Math.cos(Math.PI * t) - 1) / 2;

            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).InQuad:
              return t * t;

            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).OutQuad:
              return 1 - (1 - t) * (1 - t);

            case (_crd && eEasing === void 0 ? (_reportPossibleCrUseOfeEasing({
              error: Error()
            }), eEasing) : eEasing).InOutQuad:
              return t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2;

            default:
              return t;
          }
        }

      });

      _export("EventGroupRunner", EventGroupRunner = class EventGroupRunner {
        constructor(data) {
          this.data = void 0;
          this.owner = null;
          this.isTriggered = false;
          this.isConditionAMet = false;
          this.isConditionBMet = false;
          this.data = data;
        }

        init(owner) {
          this.owner = owner;
          this.isTriggered = this.isConditionAMet = this.isConditionBMet = false;
          this.registerListeners();
        }

        tryTrigger() {
          switch (this.data.groupOp) {
            case (_crd && eConditionGroupOp === void 0 ? (_reportPossibleCrUseOfeConditionGroupOp({
              error: Error()
            }), eConditionGroupOp) : eConditionGroupOp).And:
              this.isTriggered = this.isConditionAMet && this.isConditionBMet;
              break;

            case (_crd && eConditionGroupOp === void 0 ? (_reportPossibleCrUseOfeConditionGroupOp({
              error: Error()
            }), eConditionGroupOp) : eConditionGroupOp).Or:
              this.isTriggered = this.isConditionAMet || this.isConditionBMet;
              break;
          }

          if (this.isTriggered) {
            for (var action of this.data.actions) {
              (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
                error: Error()
              }), BulletSystem) : BulletSystem).createActionRunner(this.owner, action);
            }

            this.unRegisterListeners();
          }
        }

        registerListeners() {
          if (this.data.conditionA) {
            this.isConditionAMet = false;
            (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).on(this.data.conditionA.eventType, this.onConditionAChanged.bind(this));
          } else {
            this.isConditionAMet = true;
          }

          if (this.data.conditionB) {
            this.isConditionBMet = false;
            (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).on(this.data.conditionB.eventType, this.onConditionBChanged.bind(this));
          } else {
            this.isConditionBMet = true;
          }
        }

        unRegisterListeners() {
          if (this.data.conditionA) {
            (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).off(this.data.conditionA.eventType, this.onConditionAChanged.bind(this));
          }

          if (this.data.conditionB) {
            (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).off(this.data.conditionB.eventType, this.onConditionBChanged.bind(this));
          }
        }

        onConditionAChanged(value) {
          // Handle condition A change
          this.isConditionAMet = EventGroupRunner.compareValue(this.data.conditionA.eventOp, value, this.data.conditionA.targetValue);
          this.tryTrigger();
        }

        onConditionBChanged(value) {
          // Handle condition B change
          this.isConditionBMet = EventGroupRunner.compareValue(this.data.conditionB.eventOp, value, this.data.conditionB.targetValue);
          this.tryTrigger();
        }

        static compareValue(op, a, b) {
          switch (op) {
            case (_crd && eEventConditionOp === void 0 ? (_reportPossibleCrUseOfeEventConditionOp({
              error: Error()
            }), eEventConditionOp) : eEventConditionOp).Equal:
              return a === b;

            case (_crd && eEventConditionOp === void 0 ? (_reportPossibleCrUseOfeEventConditionOp({
              error: Error()
            }), eEventConditionOp) : eEventConditionOp).NotEqual:
              return a !== b;

            case (_crd && eEventConditionOp === void 0 ? (_reportPossibleCrUseOfeEventConditionOp({
              error: Error()
            }), eEventConditionOp) : eEventConditionOp).Greater:
              return a > b;

            case (_crd && eEventConditionOp === void 0 ? (_reportPossibleCrUseOfeEventConditionOp({
              error: Error()
            }), eEventConditionOp) : eEventConditionOp).Less:
              return a < b;

            case (_crd && eEventConditionOp === void 0 ? (_reportPossibleCrUseOfeEventConditionOp({
              error: Error()
            }), eEventConditionOp) : eEventConditionOp).GreaterEqual:
              return a >= b;

            case (_crd && eEventConditionOp === void 0 ? (_reportPossibleCrUseOfeEventConditionOp({
              error: Error()
            }), eEventConditionOp) : eEventConditionOp).LessEqual:
              return a <= b;

            default:
              return false;
          }
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c94d2dfc57fee725b0a5b484279cd92b068cee1e.js.map