{"name": "level-editor", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "level-editor", "version": "1.0.0", "hasInstallScript": true, "dependencies": {"fs-extra": "^10.0.0", "vue": "^3.1.4"}, "devDependencies": {"@cocos/creator-types": "^3.8.6", "@types/fs-extra": "^9.0.5", "@types/node": "^18.17.1", "typescript": "^5.8.2"}}, "node_modules/@babel/parser": {"version": "7.23.0", "license": "MIT", "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@cocos/creator-types": {"version": "3.8.6", "resolved": "https://registry.npmjs.org/@cocos/creator-types/-/creator-types-3.8.6.tgz", "integrity": "sha512-hyZ4aoqqLxoRtKbBLSJM5RgtK3oGOlTEryHDcyH4znq3h9cFk+MSbQC2aJHvK5/bMlJzsZ641/hD77RGSrvo8Q==", "dev": true, "license": "ISC"}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.4.15", "license": "MIT"}, "node_modules/@types/fs-extra": {"version": "9.0.13", "resolved": "https://registry.npmjs.org/@types/fs-extra/-/fs-extra-9.0.13.tgz", "integrity": "sha512-nEnwB++1u5lVDM2UI4c1+5R+FYaKfaAzS4OococimjVm3nQw3TuzH5UNsocrcTBbhnerblyHj4A49qXbIiZdpA==", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/node": {"version": "18.19.122", "resolved": "https://registry.npmjs.org/@types/node/-/node-18.19.122.tgz", "integrity": "sha512-yzegtT82dwTNEe/9y+CM8cgb42WrUfMMCg2QqSddzO1J6uPmBD7qKCZ7dOHZP2Yrpm/kb0eqdNMn2MUyEiqBmA==", "dev": true, "license": "MIT", "dependencies": {"undici-types": "~5.26.4"}}, "node_modules/@vue/compiler-core": {"version": "3.3.4", "license": "MIT", "dependencies": {"@babel/parser": "^7.21.3", "@vue/shared": "3.3.4", "estree-walker": "^2.0.2", "source-map-js": "^1.0.2"}}, "node_modules/@vue/compiler-dom": {"version": "3.3.4", "license": "MIT", "dependencies": {"@vue/compiler-core": "3.3.4", "@vue/shared": "3.3.4"}}, "node_modules/@vue/compiler-sfc": {"version": "3.3.4", "license": "MIT", "dependencies": {"@babel/parser": "^7.20.15", "@vue/compiler-core": "3.3.4", "@vue/compiler-dom": "3.3.4", "@vue/compiler-ssr": "3.3.4", "@vue/reactivity-transform": "3.3.4", "@vue/shared": "3.3.4", "estree-walker": "^2.0.2", "magic-string": "^0.30.0", "postcss": "^8.1.10", "source-map-js": "^1.0.2"}}, "node_modules/@vue/compiler-ssr": {"version": "3.3.4", "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.3.4", "@vue/shared": "3.3.4"}}, "node_modules/@vue/reactivity": {"version": "3.3.4", "license": "MIT", "dependencies": {"@vue/shared": "3.3.4"}}, "node_modules/@vue/reactivity-transform": {"version": "3.3.4", "license": "MIT", "dependencies": {"@babel/parser": "^7.20.15", "@vue/compiler-core": "3.3.4", "@vue/shared": "3.3.4", "estree-walker": "^2.0.2", "magic-string": "^0.30.0"}}, "node_modules/@vue/runtime-core": {"version": "3.3.4", "license": "MIT", "dependencies": {"@vue/reactivity": "3.3.4", "@vue/shared": "3.3.4"}}, "node_modules/@vue/runtime-dom": {"version": "3.3.4", "license": "MIT", "dependencies": {"@vue/runtime-core": "3.3.4", "@vue/shared": "3.3.4", "csstype": "^3.1.1"}}, "node_modules/@vue/server-renderer": {"version": "3.3.4", "license": "MIT", "dependencies": {"@vue/compiler-ssr": "3.3.4", "@vue/shared": "3.3.4"}, "peerDependencies": {"vue": "3.3.4"}}, "node_modules/@vue/shared": {"version": "3.3.4", "license": "MIT"}, "node_modules/csstype": {"version": "3.1.2", "license": "MIT"}, "node_modules/estree-walker": {"version": "2.0.2", "license": "MIT"}, "node_modules/fs-extra": {"version": "10.1.0", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/graceful-fs": {"version": "4.2.11", "license": "ISC"}, "node_modules/jsonfile": {"version": "6.1.0", "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/magic-string": {"version": "0.30.3", "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "engines": {"node": ">=12"}}, "node_modules/nanoid": {"version": "3.3.6", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/picocolors": {"version": "1.0.0", "license": "ISC"}, "node_modules/postcss": {"version": "8.4.30", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.6", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/source-map-js": {"version": "1.0.2", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/typescript": {"version": "5.9.2", "resolved": "https://registry.npmjs.org/typescript/-/typescript-5.9.2.tgz", "integrity": "sha512-CWBzXQrc/qOkhidw1OzBTQuYRbfyxDXJMVJ1XNwUHGROVmuaeiEm3OslpZ1RV96d7SKKjZKrSJu3+t/xlw3R9A==", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/undici-types": {"version": "5.26.5", "resolved": "https://registry.npmjs.org/undici-types/-/undici-types-5.26.5.tgz", "integrity": "sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==", "dev": true, "license": "MIT"}, "node_modules/universalify": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/vue": {"version": "3.3.4", "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.3.4", "@vue/compiler-sfc": "3.3.4", "@vue/runtime-dom": "3.3.4", "@vue/server-renderer": "3.3.4", "@vue/shared": "3.3.4"}}}}