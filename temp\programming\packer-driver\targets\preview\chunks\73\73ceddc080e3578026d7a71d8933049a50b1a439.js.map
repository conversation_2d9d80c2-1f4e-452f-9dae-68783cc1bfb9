{"version": 3, "sources": ["file:///D:/Workspace/Projects/Moolego/M2Game/Client/assets/scripts/Game/bullet/actions/BulletActionHandlers.ts"], "names": ["BulletDurationHandler", "BulletElapsedTimeHandler", "BulletPosXHandler", "BulletPosYHandler", "BulletDamageHandler", "BulletSpeedHandler", "BulletSpeedAngleHandler", "BulletAccelerationHandler", "BulletAccelerationAngleHandler", "BulletScaleHandler", "BulletColorRHandler", "BulletColorGHandler", "BulletColorBHandler", "BulletColorAHandler", "BulletFaceMovingDirHandler", "BulletTrackingTargetHandler", "BulletDestructiveHandler", "BulletDestructiveOnHitHandler", "_decorator", "Color", "BaseActionHandler", "eBulletActionType", "Bullet", "ccclass", "constructor", "Bullet_Duration", "getValue", "bullet", "duration", "setValue", "value", "canInterpolate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "target", "Bullet_ElapsedTime", "elapsedTime", "Bullet_PosX", "node", "getPosition", "x", "pos", "setPosition", "y", "z", "Bullet_PosY", "Bullet_Damage", "damage", "Bullet_Speed", "mover", "speed", "Bullet_SpeedAngle", "speedAngle", "Bullet_Acceleration", "acceleration", "Bullet_AccelerationAngle", "accelerationAngle", "Bullet_Scale", "getScale", "setScale", "Bullet_ColorR", "bulletSprite", "color", "r", "g", "b", "a", "Bullet_ColorG", "Bullet_ColorB", "Bullet_ColorA", "Bullet_FaceMovingDir", "isFacingMoveDir", "Bullet_TrackingTarget", "isTrackingTarget", "Bullet_Destructive", "isDestructive", "Bullet_DestructiveOnHit", "isDestructiveOnHit"], "mappings": ";;;gJASaA,qB,EAyBAC,wB,EAyBAC,iB,EA0BAC,iB,EA0BAC,mB,EAyBAC,kB,EAyBAC,uB,EAyBAC,yB,EAyBAC,8B,EAyBAC,kB,EAyBAC,mB,EA0BAC,mB,EA0BAC,mB,EA0BAC,mB,EA0BAC,0B,EAyBAC,2B,EAyBAC,wB,EAyBAC,6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAxbJC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;;AACZC,MAAAA,iB,iBAAAA,iB;;AACAC,MAAAA,iB,iBAAAA,iB;;AACAC,MAAAA,M,iBAAAA,M;;;;;;;;;OACH;AAAEC,QAAAA;AAAF,O,GAAcL,U;AAEpB;AACA;AACA;;uCACalB,qB,GAAN,MAAMA,qBAAN;AAAA;AAAA,kDAA8D;AACjEwB,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,sDAAkBC,eAAxB,EAAyC,QAAzC,EAAmD,UAAnD;AACH;;AAEDC,QAAAA,QAAQ,CAACC,MAAD,EAAyB;AAC7B,iBAAOA,MAAM,CAACC,QAAd;AACH;;AAEDC,QAAAA,QAAQ,CAACF,MAAD,EAAiBG,KAAjB,EAAsC;AAC1CH,UAAAA,MAAM,CAACC,QAAP,GAAkBE,KAAlB;AACH;;AAEDC,QAAAA,cAAc,GAAY;AACtB,iBAAO,IAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,+BAAb;AACH;;AAnBgE,O;AAsBrE;AACA;AACA;;;0CACahC,wB,GAAN,MAAMA,wBAAN;AAAA;AAAA,kDAAiE;AACpEuB,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,sDAAkBU,kBAAxB,EAA4C,QAA5C,EAAsD,cAAtD;AACH;;AAEDR,QAAAA,QAAQ,CAACC,MAAD,EAAyB;AAC7B,iBAAOA,MAAM,CAACQ,WAAd;AACH;;AAEDN,QAAAA,QAAQ,CAACF,MAAD,EAAiBG,KAAjB,EAAsC;AAC1CH,UAAAA,MAAM,CAACQ,WAAP,GAAqBL,KAArB;AACH;;AAEDC,QAAAA,cAAc,GAAY;AACtB,iBAAO,IAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,+BAAb;AACH;;AAnBmE,O;AAsBxE;AACA;AACA;;;mCACa/B,iB,GAAN,MAAMA,iBAAN;AAAA;AAAA,kDAA0D;AAC7DsB,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,sDAAkBY,WAAxB,EAAqC,QAArC,EAA+C,YAA/C;AACH;;AAEDV,QAAAA,QAAQ,CAACC,MAAD,EAAyB;AAC7B,iBAAOA,MAAM,CAACU,IAAP,CAAYC,WAAZ,GAA0BC,CAAjC;AACH;;AAEDV,QAAAA,QAAQ,CAACF,MAAD,EAAiBG,KAAjB,EAAsC;AAC1C,cAAMU,GAAG,GAAGb,MAAM,CAACU,IAAP,CAAYC,WAAZ,EAAZ;AACAX,UAAAA,MAAM,CAACU,IAAP,CAAYI,WAAZ,CAAwBX,KAAxB,EAA+BU,GAAG,CAACE,CAAnC,EAAsCF,GAAG,CAACG,CAA1C;AACH;;AAEDZ,QAAAA,cAAc,GAAY;AACtB,iBAAO,IAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,+BAAb;AACH;;AApB4D,O;AAuBjE;AACA;AACA;;;mCACa9B,iB,GAAN,MAAMA,iBAAN;AAAA;AAAA,kDAA0D;AAC7DqB,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,sDAAkBoB,WAAxB,EAAqC,QAArC,EAA+C,YAA/C;AACH;;AAEDlB,QAAAA,QAAQ,CAACC,MAAD,EAAyB;AAC7B,iBAAOA,MAAM,CAACU,IAAP,CAAYC,WAAZ,GAA0BI,CAAjC;AACH;;AAEDb,QAAAA,QAAQ,CAACF,MAAD,EAAiBG,KAAjB,EAAsC;AAC1C,cAAMU,GAAG,GAAGb,MAAM,CAACU,IAAP,CAAYC,WAAZ,EAAZ;AACAX,UAAAA,MAAM,CAACU,IAAP,CAAYI,WAAZ,CAAwBD,GAAG,CAACD,CAA5B,EAA+BT,KAA/B,EAAsCU,GAAG,CAACG,CAA1C;AACH;;AAEDZ,QAAAA,cAAc,GAAY;AACtB,iBAAO,IAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,+BAAb;AACH;;AApB4D,O;AAuBjE;AACA;AACA;;;qCACa7B,mB,GAAN,MAAMA,mBAAN;AAAA;AAAA,kDAA4D;AAC/DoB,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,sDAAkBqB,aAAxB,EAAuC,QAAvC,EAAiD,QAAjD;AACH;;AAEDnB,QAAAA,QAAQ,CAACC,MAAD,EAAyB;AAC7B,iBAAOA,MAAM,CAACmB,MAAd;AACH;;AAEDjB,QAAAA,QAAQ,CAACF,MAAD,EAAiBG,KAAjB,EAAsC;AAC1CH,UAAAA,MAAM,CAACmB,MAAP,GAAgBhB,KAAhB;AACH;;AAEDC,QAAAA,cAAc,GAAY;AACtB,iBAAO,IAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,+BAAb;AACH;;AAnB8D,O;AAsBnE;AACA;AACA;;;oCACa5B,kB,GAAN,MAAMA,kBAAN;AAAA;AAAA,kDAA2D;AAC9DmB,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,sDAAkBuB,YAAxB,EAAsC,QAAtC,EAAgD,OAAhD;AACH;;AAEDrB,QAAAA,QAAQ,CAACC,MAAD,EAAyB;AAC7B,iBAAOA,MAAM,CAACqB,KAAP,CAAaC,KAApB;AACH;;AAEDpB,QAAAA,QAAQ,CAACF,MAAD,EAAiBG,KAAjB,EAAsC;AAC1CH,UAAAA,MAAM,CAACqB,KAAP,CAAaC,KAAb,GAAqBnB,KAArB;AACH;;AAEDC,QAAAA,cAAc,GAAY;AACtB,iBAAO,IAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,+BAAb;AACH;;AAnB6D,O;AAsBlE;AACA;AACA;;;yCACa3B,uB,GAAN,MAAMA,uBAAN;AAAA;AAAA,kDAAgE;AACnEkB,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,sDAAkB0B,iBAAxB,EAA2C,QAA3C,EAAqD,aAArD;AACH;;AAEDxB,QAAAA,QAAQ,CAACC,MAAD,EAAyB;AAC7B,iBAAOA,MAAM,CAACqB,KAAP,CAAaG,UAApB;AACH;;AAEDtB,QAAAA,QAAQ,CAACF,MAAD,EAAiBG,KAAjB,EAAsC;AAC1CH,UAAAA,MAAM,CAACqB,KAAP,CAAaG,UAAb,GAA0BrB,KAA1B;AACH;;AAEDC,QAAAA,cAAc,GAAY;AACtB,iBAAO,IAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,+BAAb;AACH;;AAnBkE,O;AAsBvE;AACA;AACA;;;2CACa1B,yB,GAAN,MAAMA,yBAAN;AAAA;AAAA,kDAAkE;AACrEiB,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,sDAAkB4B,mBAAxB,EAA6C,QAA7C,EAAuD,cAAvD;AACH;;AAED1B,QAAAA,QAAQ,CAACC,MAAD,EAAyB;AAC7B,iBAAOA,MAAM,CAACqB,KAAP,CAAaK,YAApB;AACH;;AAEDxB,QAAAA,QAAQ,CAACF,MAAD,EAAiBG,KAAjB,EAAsC;AAC1CH,UAAAA,MAAM,CAACqB,KAAP,CAAaK,YAAb,GAA4BvB,KAA5B;AACH;;AAEDC,QAAAA,cAAc,GAAY;AACtB,iBAAO,IAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,+BAAb;AACH;;AAnBoE,O;AAsBzE;AACA;AACA;;;gDACazB,8B,GAAN,MAAMA,8BAAN;AAAA;AAAA,kDAAuE;AAC1EgB,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,sDAAkB8B,wBAAxB,EAAkD,QAAlD,EAA4D,oBAA5D;AACH;;AAED5B,QAAAA,QAAQ,CAACC,MAAD,EAAyB;AAC7B,iBAAOA,MAAM,CAACqB,KAAP,CAAaO,iBAApB;AACH;;AAED1B,QAAAA,QAAQ,CAACF,MAAD,EAAiBG,KAAjB,EAAsC;AAC1CH,UAAAA,MAAM,CAACqB,KAAP,CAAaO,iBAAb,GAAiCzB,KAAjC;AACH;;AAEDC,QAAAA,cAAc,GAAY;AACtB,iBAAO,IAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,+BAAb;AACH;;AAnByE,O;AAsB9E;AACA;AACA;;;oCACaxB,kB,GAAN,MAAMA,kBAAN;AAAA;AAAA,kDAA2D;AAC9De,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,sDAAkBgC,YAAxB,EAAsC,QAAtC,EAAgD,OAAhD;AACH;;AAED9B,QAAAA,QAAQ,CAACC,MAAD,EAAyB;AAC7B,iBAAOA,MAAM,CAACU,IAAP,CAAYoB,QAAZ,GAAuBlB,CAA9B;AACH;;AAEDV,QAAAA,QAAQ,CAACF,MAAD,EAAiBG,KAAjB,EAAsC;AAC1CH,UAAAA,MAAM,CAACU,IAAP,CAAYqB,QAAZ,CAAqB5B,KAArB,EAA4BA,KAA5B,EAAmCA,KAAnC;AACH;;AAEDC,QAAAA,cAAc,GAAY;AACtB,iBAAO,IAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,+BAAb;AACH;;AAnB6D,O;AAsBlE;AACA;AACA;;;qCACavB,mB,GAAN,MAAMA,mBAAN;AAAA;AAAA,kDAA4D;AAC/Dc,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,sDAAkBmC,aAAxB,EAAuC,QAAvC,EAAiD,WAAjD;AACH;;AAEDjC,QAAAA,QAAQ,CAACC,MAAD,EAAyB;AAC7B,iBAAOA,MAAM,CAACiC,YAAP,CAAoBC,KAApB,CAA0BC,CAAjC;AACH;;AAEDjC,QAAAA,QAAQ,CAACF,MAAD,EAAiBG,KAAjB,EAAsC;AAC1C,cAAM+B,KAAK,GAAGlC,MAAM,CAACiC,YAAP,CAAoBC,KAAlC;AACAlC,UAAAA,MAAM,CAACiC,YAAP,CAAoBC,KAApB,GAA4B,IAAI1C,KAAJ,CAAUW,KAAV,EAAiB+B,KAAK,CAACE,CAAvB,EAA0BF,KAAK,CAACG,CAAhC,EAAmCH,KAAK,CAACI,CAAzC,CAA5B;AACH;;AAEDlC,QAAAA,cAAc,GAAY;AACtB,iBAAO,IAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,+BAAb;AACH;;AApB8D,O;AAuBnE;AACA;AACA;;;qCACatB,mB,GAAN,MAAMA,mBAAN;AAAA;AAAA,kDAA4D;AAC/Da,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,sDAAkB0C,aAAxB,EAAuC,QAAvC,EAAiD,aAAjD;AACH;;AAEDxC,QAAAA,QAAQ,CAACC,MAAD,EAAyB;AAC7B,iBAAOA,MAAM,CAACiC,YAAP,CAAoBC,KAApB,CAA0BE,CAAjC;AACH;;AAEDlC,QAAAA,QAAQ,CAACF,MAAD,EAAiBG,KAAjB,EAAsC;AAC1C,cAAM+B,KAAK,GAAGlC,MAAM,CAACiC,YAAP,CAAoBC,KAAlC;AACAlC,UAAAA,MAAM,CAACiC,YAAP,CAAoBC,KAApB,GAA4B,IAAI1C,KAAJ,CAAU0C,KAAK,CAACC,CAAhB,EAAmBhC,KAAnB,EAA0B+B,KAAK,CAACG,CAAhC,EAAmCH,KAAK,CAACI,CAAzC,CAA5B;AACH;;AAEDlC,QAAAA,cAAc,GAAY;AACtB,iBAAO,IAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,+BAAb;AACH;;AApB8D,O;AAuBnE;AACA;AACA;;;qCACarB,mB,GAAN,MAAMA,mBAAN;AAAA;AAAA,kDAA4D;AAC/DY,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,sDAAkB2C,aAAxB,EAAuC,QAAvC,EAAiD,YAAjD;AACH;;AAEDzC,QAAAA,QAAQ,CAACC,MAAD,EAAyB;AAC7B,iBAAOA,MAAM,CAACiC,YAAP,CAAoBC,KAApB,CAA0BG,CAAjC;AACH;;AAEDnC,QAAAA,QAAQ,CAACF,MAAD,EAAiBG,KAAjB,EAAsC;AAC1C,cAAM+B,KAAK,GAAGlC,MAAM,CAACiC,YAAP,CAAoBC,KAAlC;AACAlC,UAAAA,MAAM,CAACiC,YAAP,CAAoBC,KAApB,GAA4B,IAAI1C,KAAJ,CAAU0C,KAAK,CAACC,CAAhB,EAAmBD,KAAK,CAACE,CAAzB,EAA4BjC,KAA5B,EAAmC+B,KAAK,CAACI,CAAzC,CAA5B;AACH;;AAEDlC,QAAAA,cAAc,GAAY;AACtB,iBAAO,IAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,+BAAb;AACH;;AApB8D,O;AAuBnE;AACA;AACA;;;qCACapB,mB,GAAN,MAAMA,mBAAN;AAAA;AAAA,kDAA4D;AAC/DW,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,sDAAkB4C,aAAxB,EAAuC,QAAvC,EAAiD,aAAjD;AACH;;AAED1C,QAAAA,QAAQ,CAACC,MAAD,EAAyB;AAC7B,iBAAOA,MAAM,CAACiC,YAAP,CAAoBC,KAApB,CAA0BI,CAAjC;AACH;;AAEDpC,QAAAA,QAAQ,CAACF,MAAD,EAAiBG,KAAjB,EAAsC;AAC1C,cAAM+B,KAAK,GAAGlC,MAAM,CAACiC,YAAP,CAAoBC,KAAlC;AACAlC,UAAAA,MAAM,CAACiC,YAAP,CAAoBC,KAApB,GAA4B,IAAI1C,KAAJ,CAAU0C,KAAK,CAACC,CAAhB,EAAmBD,KAAK,CAACE,CAAzB,EAA4BF,KAAK,CAACG,CAAlC,EAAqClC,KAArC,CAA5B;AACH;;AAEDC,QAAAA,cAAc,GAAY;AACtB,iBAAO,IAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,+BAAb;AACH;;AApB8D,O;AAuBnE;AACA;AACA;;;4CACanB,0B,GAAN,MAAMA,0BAAN;AAAA;AAAA,kDAAoE;AACvEU,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,sDAAkB6C,oBAAxB,EAA8C,SAA9C,EAAyD,uBAAzD;AACH;;AAED3C,QAAAA,QAAQ,CAACC,MAAD,EAA0B;AAC9B,iBAAOA,MAAM,CAACqB,KAAP,CAAasB,eAApB;AACH;;AAEDzC,QAAAA,QAAQ,CAACF,MAAD,EAAiBG,KAAjB,EAAuC;AAC3CH,UAAAA,MAAM,CAACqB,KAAP,CAAasB,eAAb,GAA+BxC,KAA/B;AACH;;AAEDC,QAAAA,cAAc,GAAY;AACtB,iBAAO,KAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,+BAAb;AACH;;AAnBsE,O;AAsB3E;AACA;AACA;;;6CACalB,2B,GAAN,MAAMA,2BAAN;AAAA;AAAA,kDAAqE;AACxES,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,sDAAkB+C,qBAAxB,EAA+C,SAA/C,EAA0D,iBAA1D;AACH;;AAED7C,QAAAA,QAAQ,CAACC,MAAD,EAA0B;AAC9B,iBAAOA,MAAM,CAACqB,KAAP,CAAawB,gBAApB;AACH;;AAED3C,QAAAA,QAAQ,CAACF,MAAD,EAAiBG,KAAjB,EAAuC;AAC3CH,UAAAA,MAAM,CAACqB,KAAP,CAAawB,gBAAb,GAAgC1C,KAAhC;AACH;;AAEDC,QAAAA,cAAc,GAAY;AACtB,iBAAO,KAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,+BAAb;AACH;;AAnBuE,O;AAsB5E;AACA;AACA;;;0CACajB,wB,GAAN,MAAMA,wBAAN;AAAA;AAAA,kDAAkE;AACrEQ,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,sDAAkBiD,kBAAxB,EAA4C,SAA5C,EAAuD,aAAvD;AACH;;AAED/C,QAAAA,QAAQ,CAACC,MAAD,EAA0B;AAC9B,iBAAOA,MAAM,CAAC+C,aAAd;AACH;;AAED7C,QAAAA,QAAQ,CAACF,MAAD,EAAiBG,KAAjB,EAAuC;AAC3CH,UAAAA,MAAM,CAAC+C,aAAP,GAAuB5C,KAAvB;AACH;;AAEDC,QAAAA,cAAc,GAAY;AACtB,iBAAO,KAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,+BAAb;AACH;;AAnBoE,O;AAsBzE;AACA;AACA;;;+CACahB,6B,GAAN,MAAMA,6BAAN;AAAA;AAAA,kDAAuE;AAC1EO,QAAAA,WAAW,GAAG;AACV,gBAAM;AAAA;AAAA,sDAAkBmD,uBAAxB,EAAiD,SAAjD,EAA4D,oBAA5D;AACH;;AAEDjD,QAAAA,QAAQ,CAACC,MAAD,EAA0B;AAC9B,iBAAOA,MAAM,CAACiD,kBAAd;AACH;;AAED/C,QAAAA,QAAQ,CAACF,MAAD,EAAiBG,KAAjB,EAAuC;AAC3CH,UAAAA,MAAM,CAACiD,kBAAP,GAA4B9C,KAA5B;AACH;;AAEDC,QAAAA,cAAc,GAAY;AACtB,iBAAO,KAAP;AACH;;AAEDC,QAAAA,aAAa,CAACC,MAAD,EAAuB;AAChC,iBAAOA,MAAM;AAAA;AAAA,+BAAb;AACH;;AAnByE,O", "sourcesContent": ["import { _decorator, Color, Vec3 } from 'cc';\nimport { BaseActionHandler } from './IActionHandler';\nimport { eBulletActionType } from '../../data/EventActionData';\nimport { Bullet } from '../Bullet';\nconst { ccclass } = _decorator;\n\n/**\n * Handler for Bullet Duration\n */\nexport class BulletDurationHandler extends BaseActionHandler<number> {\n    constructor() {\n        super(eBulletActionType.Bullet_Duration, 'number', 'Duration');\n    }\n    \n    getValue(bullet: Bullet): number {\n        return bullet.duration;\n    }\n    \n    setValue(bullet: Bullet, value: number): void {\n        bullet.duration = value;\n    }\n    \n    canInterpolate(): boolean {\n        return true;\n    }\n    \n    isValidTarget(target: any): boolean {\n        return target instanceof Bullet;\n    }\n}\n\n/**\n * Handler for Bullet Elapsed Time\n */\nexport class BulletElapsedTimeHandler extends BaseActionHandler<number> {\n    constructor() {\n        super(eBulletActionType.Bullet_ElapsedTime, 'number', 'Elapsed Time');\n    }\n    \n    getValue(bullet: Bullet): number {\n        return bullet.elapsedTime;\n    }\n    \n    setValue(bullet: Bullet, value: number): void {\n        bullet.elapsedTime = value;\n    }\n    \n    canInterpolate(): boolean {\n        return true;\n    }\n    \n    isValidTarget(target: any): boolean {\n        return target instanceof Bullet;\n    }\n}\n\n/**\n * Handler for Bullet Position X\n */\nexport class BulletPosXHandler extends BaseActionHandler<number> {\n    constructor() {\n        super(eBulletActionType.Bullet_PosX, 'number', 'Position X');\n    }\n    \n    getValue(bullet: Bullet): number {\n        return bullet.node.getPosition().x;\n    }\n    \n    setValue(bullet: Bullet, value: number): void {\n        const pos = bullet.node.getPosition();\n        bullet.node.setPosition(value, pos.y, pos.z);\n    }\n    \n    canInterpolate(): boolean {\n        return true;\n    }\n    \n    isValidTarget(target: any): boolean {\n        return target instanceof Bullet;\n    }\n}\n\n/**\n * Handler for Bullet Position Y\n */\nexport class BulletPosYHandler extends BaseActionHandler<number> {\n    constructor() {\n        super(eBulletActionType.Bullet_PosY, 'number', 'Position Y');\n    }\n    \n    getValue(bullet: Bullet): number {\n        return bullet.node.getPosition().y;\n    }\n    \n    setValue(bullet: Bullet, value: number): void {\n        const pos = bullet.node.getPosition();\n        bullet.node.setPosition(pos.x, value, pos.z);\n    }\n    \n    canInterpolate(): boolean {\n        return true;\n    }\n    \n    isValidTarget(target: any): boolean {\n        return target instanceof Bullet;\n    }\n}\n\n/**\n * Handler for Bullet Damage\n */\nexport class BulletDamageHandler extends BaseActionHandler<number> {\n    constructor() {\n        super(eBulletActionType.Bullet_Damage, 'number', 'Damage');\n    }\n    \n    getValue(bullet: Bullet): number {\n        return bullet.damage;\n    }\n    \n    setValue(bullet: Bullet, value: number): void {\n        bullet.damage = value;\n    }\n    \n    canInterpolate(): boolean {\n        return true;\n    }\n    \n    isValidTarget(target: any): boolean {\n        return target instanceof Bullet;\n    }\n}\n\n/**\n * Handler for Bullet Speed\n */\nexport class BulletSpeedHandler extends BaseActionHandler<number> {\n    constructor() {\n        super(eBulletActionType.Bullet_Speed, 'number', 'Speed');\n    }\n    \n    getValue(bullet: Bullet): number {\n        return bullet.mover.speed;\n    }\n    \n    setValue(bullet: Bullet, value: number): void {\n        bullet.mover.speed = value;\n    }\n    \n    canInterpolate(): boolean {\n        return true;\n    }\n    \n    isValidTarget(target: any): boolean {\n        return target instanceof Bullet;\n    }\n}\n\n/**\n * Handler for Bullet Speed Angle\n */\nexport class BulletSpeedAngleHandler extends BaseActionHandler<number> {\n    constructor() {\n        super(eBulletActionType.Bullet_SpeedAngle, 'number', 'Speed Angle');\n    }\n    \n    getValue(bullet: Bullet): number {\n        return bullet.mover.speedAngle;\n    }\n    \n    setValue(bullet: Bullet, value: number): void {\n        bullet.mover.speedAngle = value;\n    }\n    \n    canInterpolate(): boolean {\n        return true;\n    }\n    \n    isValidTarget(target: any): boolean {\n        return target instanceof Bullet;\n    }\n}\n\n/**\n * Handler for Bullet Acceleration\n */\nexport class BulletAccelerationHandler extends BaseActionHandler<number> {\n    constructor() {\n        super(eBulletActionType.Bullet_Acceleration, 'number', 'Acceleration');\n    }\n    \n    getValue(bullet: Bullet): number {\n        return bullet.mover.acceleration;\n    }\n    \n    setValue(bullet: Bullet, value: number): void {\n        bullet.mover.acceleration = value;\n    }\n    \n    canInterpolate(): boolean {\n        return true;\n    }\n    \n    isValidTarget(target: any): boolean {\n        return target instanceof Bullet;\n    }\n}\n\n/**\n * Handler for Bullet Acceleration Angle\n */\nexport class BulletAccelerationAngleHandler extends BaseActionHandler<number> {\n    constructor() {\n        super(eBulletActionType.Bullet_AccelerationAngle, 'number', 'Acceleration Angle');\n    }\n    \n    getValue(bullet: Bullet): number {\n        return bullet.mover.accelerationAngle;\n    }\n    \n    setValue(bullet: Bullet, value: number): void {\n        bullet.mover.accelerationAngle = value;\n    }\n    \n    canInterpolate(): boolean {\n        return true;\n    }\n    \n    isValidTarget(target: any): boolean {\n        return target instanceof Bullet;\n    }\n}\n\n/**\n * Handler for Bullet Scale\n */\nexport class BulletScaleHandler extends BaseActionHandler<number> {\n    constructor() {\n        super(eBulletActionType.Bullet_Scale, 'number', 'Scale');\n    }\n\n    getValue(bullet: Bullet): number {\n        return bullet.node.getScale().x;\n    }\n\n    setValue(bullet: Bullet, value: number): void {\n        bullet.node.setScale(value, value, value);\n    }\n\n    canInterpolate(): boolean {\n        return true;\n    }\n\n    isValidTarget(target: any): boolean {\n        return target instanceof Bullet;\n    }\n}\n\n/**\n * Handler for Bullet Color Red\n */\nexport class BulletColorRHandler extends BaseActionHandler<number> {\n    constructor() {\n        super(eBulletActionType.Bullet_ColorR, 'number', 'Color Red');\n    }\n\n    getValue(bullet: Bullet): number {\n        return bullet.bulletSprite.color.r;\n    }\n\n    setValue(bullet: Bullet, value: number): void {\n        const color = bullet.bulletSprite.color;\n        bullet.bulletSprite.color = new Color(value, color.g, color.b, color.a);\n    }\n\n    canInterpolate(): boolean {\n        return true;\n    }\n\n    isValidTarget(target: any): boolean {\n        return target instanceof Bullet;\n    }\n}\n\n/**\n * Handler for Bullet Color Green\n */\nexport class BulletColorGHandler extends BaseActionHandler<number> {\n    constructor() {\n        super(eBulletActionType.Bullet_ColorG, 'number', 'Color Green');\n    }\n\n    getValue(bullet: Bullet): number {\n        return bullet.bulletSprite.color.g;\n    }\n\n    setValue(bullet: Bullet, value: number): void {\n        const color = bullet.bulletSprite.color;\n        bullet.bulletSprite.color = new Color(color.r, value, color.b, color.a);\n    }\n\n    canInterpolate(): boolean {\n        return true;\n    }\n\n    isValidTarget(target: any): boolean {\n        return target instanceof Bullet;\n    }\n}\n\n/**\n * Handler for Bullet Color Blue\n */\nexport class BulletColorBHandler extends BaseActionHandler<number> {\n    constructor() {\n        super(eBulletActionType.Bullet_ColorB, 'number', 'Color Blue');\n    }\n\n    getValue(bullet: Bullet): number {\n        return bullet.bulletSprite.color.b;\n    }\n\n    setValue(bullet: Bullet, value: number): void {\n        const color = bullet.bulletSprite.color;\n        bullet.bulletSprite.color = new Color(color.r, color.g, value, color.a);\n    }\n\n    canInterpolate(): boolean {\n        return true;\n    }\n\n    isValidTarget(target: any): boolean {\n        return target instanceof Bullet;\n    }\n}\n\n/**\n * Handler for Bullet Color Alpha\n */\nexport class BulletColorAHandler extends BaseActionHandler<number> {\n    constructor() {\n        super(eBulletActionType.Bullet_ColorA, 'number', 'Color Alpha');\n    }\n\n    getValue(bullet: Bullet): number {\n        return bullet.bulletSprite.color.a;\n    }\n\n    setValue(bullet: Bullet, value: number): void {\n        const color = bullet.bulletSprite.color;\n        bullet.bulletSprite.color = new Color(color.r, color.g, color.b, value);\n    }\n\n    canInterpolate(): boolean {\n        return true;\n    }\n\n    isValidTarget(target: any): boolean {\n        return target instanceof Bullet;\n    }\n}\n\n/**\n * Handler for Bullet Face Moving Direction\n */\nexport class BulletFaceMovingDirHandler extends BaseActionHandler<boolean> {\n    constructor() {\n        super(eBulletActionType.Bullet_FaceMovingDir, 'boolean', 'Face Moving Direction');\n    }\n\n    getValue(bullet: Bullet): boolean {\n        return bullet.mover.isFacingMoveDir;\n    }\n\n    setValue(bullet: Bullet, value: boolean): void {\n        bullet.mover.isFacingMoveDir = value;\n    }\n\n    canInterpolate(): boolean {\n        return false;\n    }\n\n    isValidTarget(target: any): boolean {\n        return target instanceof Bullet;\n    }\n}\n\n/**\n * Handler for Bullet Tracking Target\n */\nexport class BulletTrackingTargetHandler extends BaseActionHandler<boolean> {\n    constructor() {\n        super(eBulletActionType.Bullet_TrackingTarget, 'boolean', 'Tracking Target');\n    }\n\n    getValue(bullet: Bullet): boolean {\n        return bullet.mover.isTrackingTarget;\n    }\n\n    setValue(bullet: Bullet, value: boolean): void {\n        bullet.mover.isTrackingTarget = value;\n    }\n\n    canInterpolate(): boolean {\n        return false;\n    }\n\n    isValidTarget(target: any): boolean {\n        return target instanceof Bullet;\n    }\n}\n\n/**\n * Handler for Bullet Destructive\n */\nexport class BulletDestructiveHandler extends BaseActionHandler<boolean> {\n    constructor() {\n        super(eBulletActionType.Bullet_Destructive, 'boolean', 'Destructive');\n    }\n\n    getValue(bullet: Bullet): boolean {\n        return bullet.isDestructive;\n    }\n\n    setValue(bullet: Bullet, value: boolean): void {\n        bullet.isDestructive = value;\n    }\n\n    canInterpolate(): boolean {\n        return false;\n    }\n\n    isValidTarget(target: any): boolean {\n        return target instanceof Bullet;\n    }\n}\n\n/**\n * Handler for Bullet Destructive On Hit\n */\nexport class BulletDestructiveOnHitHandler extends BaseActionHandler<boolean> {\n    constructor() {\n        super(eBulletActionType.Bullet_DestructiveOnHit, 'boolean', 'Destructive On Hit');\n    }\n\n    getValue(bullet: Bullet): boolean {\n        return bullet.isDestructiveOnHit;\n    }\n\n    setValue(bullet: Bullet, value: boolean): void {\n        bullet.isDestructiveOnHit = value;\n    }\n\n    canInterpolate(): boolean {\n        return false;\n    }\n\n    isValidTarget(target: any): boolean {\n        return target instanceof Bullet;\n    }\n}\n"]}