System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd, PlaneUIEvent;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "bbd61JregdCaYzCLdEd6Tnx", "PlaneEvent", undefined);

      _export("PlaneUIEvent", PlaneUIEvent = {
        TabChange: 'PlaneUIEvent_TabChange',
        SortTypeChange: 'PlaneUIEvent_SortTypeChange',
        BagItemClick: 'PlaneUIEvent_BagItemClick',
        EquipDisPlayRefresh: 'PlaneUIEvent_EquipDisPlayRefresh',
        MergeFinish: 'PlaneUIEvent_MergeFinish'
      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=ca4fb5454a959e6e8bbeb86ad7d2d01c2445c059.js.map