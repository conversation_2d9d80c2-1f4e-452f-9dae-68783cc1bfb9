System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Color, BaseActionHandler, eBulletActionType, Bullet, BulletDurationHandler, BulletElapsedTimeHandler, BulletPosXHandler, BulletPosYHandler, BulletDamageHandler, BulletSpeedHandler, BulletSpeedAngleHandler, BulletAccelerationHandler, BulletAccelerationAngleHandler, BulletScaleHandler, BulletColorRHandler, BulletColorGHandler, BulletColorBHandler, BulletColorAHandler, BulletFaceMovingDirHandler, BulletTrackingTargetHandler, BulletDestructiveHandler, BulletDestructiveOnHitHandler, _crd, ccclass;

  function _reportPossibleCrUseOfBaseActionHandler(extras) {
    _reporterNs.report("BaseActionHandler", "./IActionHandler", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeBulletActionType(extras) {
    _reporterNs.report("eBulletActionType", "../../data/EventActionData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBullet(extras) {
    _reporterNs.report("Bullet", "../Bullet", _context.meta, extras);
  }

  _export({
    BulletDurationHandler: void 0,
    BulletElapsedTimeHandler: void 0,
    BulletPosXHandler: void 0,
    BulletPosYHandler: void 0,
    BulletDamageHandler: void 0,
    BulletSpeedHandler: void 0,
    BulletSpeedAngleHandler: void 0,
    BulletAccelerationHandler: void 0,
    BulletAccelerationAngleHandler: void 0,
    BulletScaleHandler: void 0,
    BulletColorRHandler: void 0,
    BulletColorGHandler: void 0,
    BulletColorBHandler: void 0,
    BulletColorAHandler: void 0,
    BulletFaceMovingDirHandler: void 0,
    BulletTrackingTargetHandler: void 0,
    BulletDestructiveHandler: void 0,
    BulletDestructiveOnHitHandler: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Color = _cc.Color;
    }, function (_unresolved_2) {
      BaseActionHandler = _unresolved_2.BaseActionHandler;
    }, function (_unresolved_3) {
      eBulletActionType = _unresolved_3.eBulletActionType;
    }, function (_unresolved_4) {
      Bullet = _unresolved_4.Bullet;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "d6b2acHDABI67v44N2fKreS", "BulletActionHandlers", undefined);

      __checkObsolete__(['_decorator', 'Color', 'Vec3']);

      ({
        ccclass
      } = _decorator);
      /**
       * Handler for Bullet Duration
       */

      _export("BulletDurationHandler", BulletDurationHandler = class BulletDurationHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_Duration, 'number', 'Duration');
        }

        getValue(bullet) {
          return bullet.duration;
        }

        setValue(bullet, value) {
          bullet.duration = value;
        }

        canInterpolate() {
          return true;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet);
        }

      });
      /**
       * Handler for Bullet Elapsed Time
       */


      _export("BulletElapsedTimeHandler", BulletElapsedTimeHandler = class BulletElapsedTimeHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_ElapsedTime, 'number', 'Elapsed Time');
        }

        getValue(bullet) {
          return bullet.elapsedTime;
        }

        setValue(bullet, value) {
          bullet.elapsedTime = value;
        }

        canInterpolate() {
          return true;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet);
        }

      });
      /**
       * Handler for Bullet Position X
       */


      _export("BulletPosXHandler", BulletPosXHandler = class BulletPosXHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_PosX, 'number', 'Position X');
        }

        getValue(bullet) {
          return bullet.node.getPosition().x;
        }

        setValue(bullet, value) {
          var pos = bullet.node.getPosition();
          bullet.node.setPosition(value, pos.y, pos.z);
        }

        canInterpolate() {
          return true;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet);
        }

      });
      /**
       * Handler for Bullet Position Y
       */


      _export("BulletPosYHandler", BulletPosYHandler = class BulletPosYHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_PosY, 'number', 'Position Y');
        }

        getValue(bullet) {
          return bullet.node.getPosition().y;
        }

        setValue(bullet, value) {
          var pos = bullet.node.getPosition();
          bullet.node.setPosition(pos.x, value, pos.z);
        }

        canInterpolate() {
          return true;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet);
        }

      });
      /**
       * Handler for Bullet Damage
       */


      _export("BulletDamageHandler", BulletDamageHandler = class BulletDamageHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_Damage, 'number', 'Damage');
        }

        getValue(bullet) {
          return bullet.damage;
        }

        setValue(bullet, value) {
          bullet.damage = value;
        }

        canInterpolate() {
          return true;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet);
        }

      });
      /**
       * Handler for Bullet Speed
       */


      _export("BulletSpeedHandler", BulletSpeedHandler = class BulletSpeedHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_Speed, 'number', 'Speed');
        }

        getValue(bullet) {
          return bullet.mover.speed;
        }

        setValue(bullet, value) {
          bullet.mover.speed = value;
        }

        canInterpolate() {
          return true;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet);
        }

      });
      /**
       * Handler for Bullet Speed Angle
       */


      _export("BulletSpeedAngleHandler", BulletSpeedAngleHandler = class BulletSpeedAngleHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_SpeedAngle, 'number', 'Speed Angle');
        }

        getValue(bullet) {
          return bullet.mover.speedAngle;
        }

        setValue(bullet, value) {
          bullet.mover.speedAngle = value;
        }

        canInterpolate() {
          return true;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet);
        }

      });
      /**
       * Handler for Bullet Acceleration
       */


      _export("BulletAccelerationHandler", BulletAccelerationHandler = class BulletAccelerationHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_Acceleration, 'number', 'Acceleration');
        }

        getValue(bullet) {
          return bullet.mover.acceleration;
        }

        setValue(bullet, value) {
          bullet.mover.acceleration = value;
        }

        canInterpolate() {
          return true;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet);
        }

      });
      /**
       * Handler for Bullet Acceleration Angle
       */


      _export("BulletAccelerationAngleHandler", BulletAccelerationAngleHandler = class BulletAccelerationAngleHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_AccelerationAngle, 'number', 'Acceleration Angle');
        }

        getValue(bullet) {
          return bullet.mover.accelerationAngle;
        }

        setValue(bullet, value) {
          bullet.mover.accelerationAngle = value;
        }

        canInterpolate() {
          return true;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet);
        }

      });
      /**
       * Handler for Bullet Scale
       */


      _export("BulletScaleHandler", BulletScaleHandler = class BulletScaleHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_Scale, 'number', 'Scale');
        }

        getValue(bullet) {
          return bullet.node.getScale().x;
        }

        setValue(bullet, value) {
          bullet.node.setScale(value, value, value);
        }

        canInterpolate() {
          return true;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet);
        }

      });
      /**
       * Handler for Bullet Color Red
       */


      _export("BulletColorRHandler", BulletColorRHandler = class BulletColorRHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_ColorR, 'number', 'Color Red');
        }

        getValue(bullet) {
          return bullet.bulletSprite.color.r;
        }

        setValue(bullet, value) {
          var color = bullet.bulletSprite.color;
          bullet.bulletSprite.color = new Color(value, color.g, color.b, color.a);
        }

        canInterpolate() {
          return true;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet);
        }

      });
      /**
       * Handler for Bullet Color Green
       */


      _export("BulletColorGHandler", BulletColorGHandler = class BulletColorGHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_ColorG, 'number', 'Color Green');
        }

        getValue(bullet) {
          return bullet.bulletSprite.color.g;
        }

        setValue(bullet, value) {
          var color = bullet.bulletSprite.color;
          bullet.bulletSprite.color = new Color(color.r, value, color.b, color.a);
        }

        canInterpolate() {
          return true;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet);
        }

      });
      /**
       * Handler for Bullet Color Blue
       */


      _export("BulletColorBHandler", BulletColorBHandler = class BulletColorBHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_ColorB, 'number', 'Color Blue');
        }

        getValue(bullet) {
          return bullet.bulletSprite.color.b;
        }

        setValue(bullet, value) {
          var color = bullet.bulletSprite.color;
          bullet.bulletSprite.color = new Color(color.r, color.g, value, color.a);
        }

        canInterpolate() {
          return true;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet);
        }

      });
      /**
       * Handler for Bullet Color Alpha
       */


      _export("BulletColorAHandler", BulletColorAHandler = class BulletColorAHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_ColorA, 'number', 'Color Alpha');
        }

        getValue(bullet) {
          return bullet.bulletSprite.color.a;
        }

        setValue(bullet, value) {
          var color = bullet.bulletSprite.color;
          bullet.bulletSprite.color = new Color(color.r, color.g, color.b, value);
        }

        canInterpolate() {
          return true;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet);
        }

      });
      /**
       * Handler for Bullet Face Moving Direction
       */


      _export("BulletFaceMovingDirHandler", BulletFaceMovingDirHandler = class BulletFaceMovingDirHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_FaceMovingDir, 'boolean', 'Face Moving Direction');
        }

        getValue(bullet) {
          return bullet.mover.isFacingMoveDir;
        }

        setValue(bullet, value) {
          bullet.mover.isFacingMoveDir = value;
        }

        canInterpolate() {
          return false;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet);
        }

      });
      /**
       * Handler for Bullet Tracking Target
       */


      _export("BulletTrackingTargetHandler", BulletTrackingTargetHandler = class BulletTrackingTargetHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_TrackingTarget, 'boolean', 'Tracking Target');
        }

        getValue(bullet) {
          return bullet.mover.isTrackingTarget;
        }

        setValue(bullet, value) {
          bullet.mover.isTrackingTarget = value;
        }

        canInterpolate() {
          return false;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet);
        }

      });
      /**
       * Handler for Bullet Destructive
       */


      _export("BulletDestructiveHandler", BulletDestructiveHandler = class BulletDestructiveHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_Destructive, 'boolean', 'Destructive');
        }

        getValue(bullet) {
          return bullet.isDestructive;
        }

        setValue(bullet, value) {
          bullet.isDestructive = value;
        }

        canInterpolate() {
          return false;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet);
        }

      });
      /**
       * Handler for Bullet Destructive On Hit
       */


      _export("BulletDestructiveOnHitHandler", BulletDestructiveOnHitHandler = class BulletDestructiveOnHitHandler extends (_crd && BaseActionHandler === void 0 ? (_reportPossibleCrUseOfBaseActionHandler({
        error: Error()
      }), BaseActionHandler) : BaseActionHandler) {
        constructor() {
          super((_crd && eBulletActionType === void 0 ? (_reportPossibleCrUseOfeBulletActionType({
            error: Error()
          }), eBulletActionType) : eBulletActionType).Bullet_DestructiveOnHit, 'boolean', 'Destructive On Hit');
        }

        getValue(bullet) {
          return bullet.isDestructiveOnHit;
        }

        setValue(bullet, value) {
          bullet.isDestructiveOnHit = value;
        }

        canInterpolate() {
          return false;
        }

        isValidTarget(target) {
          return target instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet);
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=73ceddc080e3578026d7a71d8933049a50b1a439.js.map